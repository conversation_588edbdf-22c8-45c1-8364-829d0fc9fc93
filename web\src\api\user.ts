import request from '@/utils/request'
import type { 
  User, 
  CreateUserRequest, 
  UpdateUserRequest, 
  ResetPasswordRequest 
} from '@/types/user'
import type { ApiResponse, PageResponse, PageRequest } from '@/types/common'

// 获取用户列表
export const getUserList = (params: PageRequest & { status?: string }): Promise<PageResponse<User[]>> => {
  return request.get('/admin/users', { params })
}

// 获取用户详情
export const getUser = (id: number): Promise<ApiResponse<User>> => {
  return request.get(`/admin/users/${id}`)
}

// 创建用户
export const createUser = (data: CreateUserRequest): Promise<ApiResponse<User>> => {
  return request.post('/admin/users', data)
}

// 更新用户
export const updateUser = (id: number, data: UpdateUserRequest): Promise<ApiResponse<User>> => {
  return request.put(`/admin/users/${id}`, data)
}

// 删除用户
export const deleteUser = (id: number): Promise<ApiResponse> => {
  return request.delete(`/admin/users/${id}`)
}

// 重置用户密码
export const resetUserPassword = (id: number, data: ResetPasswordRequest): Promise<ApiResponse> => {
  return request.post(`/admin/users/${id}/reset-password`, data)
}
