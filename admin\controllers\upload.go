package controllers

import (
	"ai_select_admin/logger"
	"ai_select_admin/services"
	"ai_select_admin/utils"

	"github.com/gin-gonic/gin"
)

type UploadController struct {
	cosService *services.COSService
}

// NewUploadController 创建上传控制器
func NewUploadController() *UploadController {
	return &UploadController{
		cosService: services.NewCOSService(),
	}
}

// GetUploadCredentials 获取上传凭证
func (uc *UploadController) GetUploadCredentials(c *gin.Context) {
	filename := c.Query("filename")
	if filename == "" {
		utils.BadRequest(c, "文件名不能为空")
		return
	}

	// 获取上传凭证
	credentials, err := uc.cosService.GetUploadCredentials(filename)
	if err != nil {
		logger.Errorf("获取上传凭证失败: %v", err)
		utils.BadRequest(c, err.<PERSON>rror())
		return
	}

	utils.Success(c, credentials)
	logger.Infof("获取上传凭证成功: %s", filename)
}

// GetCOSURL 获取COS文件访问URL
func (uc *UploadController) GetCOSURL(c *gin.Context) {
	key := c.Query("key")
	if key == "" {
		utils.BadRequest(c, "文件键不能为空")
		return
	}

	url := uc.cosService.GetCOSURL(key)
	utils.Success(c, gin.H{
		"url": url,
	})
}
