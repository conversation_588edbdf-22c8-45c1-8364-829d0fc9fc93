export interface User {
  id: number
  username: string
  email: string
  phone: string
  nickname: string
  avatar: string
  gender: number
  birthday: string | null
  status: number
  generated_report_times: number
  all_report_times: number
  last_login: string | null
  created_at: string
  updated_at: string
}

export interface CreateUserRequest {
  username: string
  password: string
  email?: string
  phone?: string
  nickname?: string
  avatar?: string
  gender?: number
  birthday?: string
  status?: number
  generated_report_times?: number
  all_report_times?: number
}

export interface UpdateUserRequest {
  email?: string
  phone?: string
  nickname?: string
  avatar?: string
  gender?: number
  birthday?: string
  status?: number
  generated_report_times?: number
  all_report_times?: number
}

export interface ResetPasswordRequest {
  new_password: string
}
