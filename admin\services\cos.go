package services

import (
	"ai_select_admin/config"
	"fmt"
	"math/rand"
	"reflect"
	"strings"
	"time"
	"unicode"

	sts "github.com/tencentyun/qcloud-cos-sts-sdk/go"
)

// COSService COS服务
type COSService struct {
	config     *config.COSConfig
	permission *config.Permission
}

// NewCOSService 创建COS服务实例
func NewCOSService() *COSService {
	return &COSService{
		config:     config.GetCOSConfig(),
		permission: config.GetImagePermission(),
	}
}

// GenerateCOSKey 生成COS对象键
func (s *COSService) GenerateCOSKey(filename string) string {
	// 获取文件扩展名
	ext := ""
	if idx := strings.LastIndex(filename, "."); idx != -1 {
		ext = strings.ToLower(filename[idx+1:])
	}

	// 生成日期路径
	date := time.Now()
	ymd := fmt.Sprintf("%d%02d%02d", date.Year(), int(date.Month()), date.Day())

	// 生成随机数
	rand.Seed(time.Now().UnixNano())
	r := fmt.Sprintf("%06d", rand.Intn(1000000))

	// 生成对象键：avatars/20250627/20250627_123456.jpg
	cosKey := fmt.Sprintf("avatars/%s/%s_%s.%s", ymd, ymd, r, ext)
	return cosKey
}

// ValidateFile 验证文件
func (s *COSService) ValidateFile(filename string) error {
	// 获取文件扩展名
	ext := ""
	if idx := strings.LastIndex(filename, "."); idx != -1 {
		ext = strings.ToLower(filename[idx+1:])
	}

	// 检查扩展名
	if s.permission.LimitExt {
		if ext == "" || !stringInSlice(ext, s.permission.ExtWhiteList) {
			return fmt.Errorf("不支持的文件格式，仅支持：%s", strings.Join(s.permission.ExtWhiteList, ", "))
		}
	}

	return nil
}

// GetUploadCredentials 获取上传凭证
func (s *COSService) GetUploadCredentials(filename string) (map[string]interface{}, error) {
	// 验证文件
	if err := s.ValidateFile(filename); err != nil {
		return nil, err
	}

	// 生成对象键
	key := s.GenerateCOSKey(filename)

	// 创建STS客户端
	client := sts.NewClient(
		s.config.SecretId,
		s.config.SecretKey,
		nil,
	)

	// 构建条件
	condition := make(map[string]map[string]interface{})

	// 限制内容类型
	if s.permission.LimitContentType {
		condition["string_like_if_exist"] = map[string]interface{}{
			"cos:content-type": "image/*",
		}
	}

	// 限制文件大小
	if s.permission.LimitContentLength {
		condition["numeric_less_than_equal"] = map[string]interface{}{
			"cos:content-length": s.permission.MaxSize,
		}
	}

	// 构建策略选项
	opt := &sts.CredentialOptions{
		DurationSeconds: int64(s.config.DurationSeconds),
		Region:          s.config.Region,
		Policy: &sts.CredentialPolicy{
			Version: "2.0",
			Statement: []sts.CredentialPolicyStatement{
				{
					Action: s.config.AllowActions,
					Effect: "allow",
					Resource: []string{
						fmt.Sprintf("qcs::cos:%s:uid/%s:%s/%s",
							s.config.Region, s.config.AppId, s.config.Bucket, key),
					},
					Condition: condition,
				},
			},
		},
	}

	// 获取临时密钥
	res, err := client.GetCredential(opt)
	if err != nil {
		return nil, fmt.Errorf("获取临时密钥失败: %v", err)
	}

	// 转换为小驼峰格式的map
	resultMap := structToCamelMap(res)
	resultMap["bucket"] = s.config.Bucket
	resultMap["region"] = s.config.Region
	resultMap["key"] = key

	return resultMap, nil
}

// GetCOSURL 获取COS文件访问URL
func (s *COSService) GetCOSURL(key string) string {
	if key == "" {
		return ""
	}
	// 如果已经是完整URL，直接返回
	if strings.HasPrefix(key, "http://") || strings.HasPrefix(key, "https://") {
		return key
	}
	// 构建COS访问URL
	return fmt.Sprintf("https://%s.cos.%s.myqcloud.com/%s", s.config.Bucket, s.config.Region, key)
}

// 辅助函数
func stringInSlice(str string, list []string) bool {
	for _, v := range list {
		if v == str {
			return true
		}
	}
	return false
}

func structToCamelMap(input interface{}) map[string]interface{} {
	v := reflect.ValueOf(input)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	result := make(map[string]interface{})
	typ := v.Type()

	for i := 0; i < v.NumField(); i++ {
		field := typ.Field(i)
		fieldValue := v.Field(i)

		// 转换字段名为小驼峰
		key := toLowerCamel(field.Name)

		// 处理嵌套结构体
		if fieldValue.Kind() == reflect.Struct ||
			(fieldValue.Kind() == reflect.Ptr && fieldValue.Elem().Kind() == reflect.Struct) {

			if fieldValue.IsNil() && fieldValue.Kind() == reflect.Ptr {
				result[key] = nil
				continue
			}

			result[key] = structToCamelMap(fieldValue.Interface())
		} else {
			// 处理基本类型
			result[key] = fieldValue.Interface()
		}
	}

	return result
}

// 转换为小驼峰格式（首字母小写）
func toLowerCamel(s string) string {
	if s == "" {
		return s
	}

	// 处理全大写单词（如 ID）
	if strings.ToUpper(s) == s {
		return strings.ToLower(s)
	}

	// 普通小驼峰转换
	runes := []rune(s)
	runes[0] = unicode.ToLower(runes[0])
	return string(runes)
}
