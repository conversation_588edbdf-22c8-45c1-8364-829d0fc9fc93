package models

import (
	"time"

	"gorm.io/gorm"
)

// Video 视频模型
type Video struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Title       string         `json:"title" gorm:"type:varchar(200);not null;comment:视频标题"`
	Description string         `json:"description" gorm:"type:text;comment:视频描述"`
	FileName    string         `json:"file_name" gorm:"type:varchar(255);not null;comment:原始文件名"`
	FileId      string         `json:"file_id" gorm:"type:varchar(100);not null;unique;comment:腾讯云文件ID"`
	PlayUrl     string         `json:"play_url" gorm:"type:varchar(500);comment:播放地址"`
	CoverUrl    string         `json:"cover_url" gorm:"type:varchar(500);comment:封面图地址"`
	Duration    int            `json:"duration" gorm:"type:int;default:0;comment:视频时长(秒)"`
	FileSize    int64          `json:"file_size" gorm:"type:bigint;default:0;comment:文件大小(字节)"`
	Width       int            `json:"width" gorm:"type:int;default:0;comment:视频宽度"`
	Height      int            `json:"height" gorm:"type:int;default:0;comment:视频高度"`
	Format      string         `json:"format" gorm:"type:varchar(20);comment:视频格式"`
	Status      int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1上传中 2转码中 3正常 4失败"`
	CategoryID  uint           `json:"category_id" gorm:"type:int;default:0;comment:分类ID"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Category *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}

// VideoListRequest 视频列表请求参数
type VideoListRequest struct {
	Page       int    `form:"page" binding:"omitempty,min=1"`
	PageSize   int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Title      string `form:"title"`
	Status     *int   `form:"status" binding:"omitempty,oneof=1 2 3 4"`
	CategoryID *uint  `form:"category_id"`
}

// VideoCreateRequest 创建视频请求参数
type VideoCreateRequest struct {
	Title       string `json:"title" binding:"required,max=200"`
	Description string `json:"description" binding:"max=1000"`
	FileName    string `json:"file_name" binding:"required,max=255"`
	FileId      string `json:"file_id" binding:"required,max=100"`
	PlayUrl     string `json:"play_url" binding:"max=500"`
	CoverUrl    string `json:"cover_url" binding:"max=500"`
	Duration    int    `json:"duration" binding:"omitempty,min=0"`
	FileSize    int64  `json:"file_size" binding:"omitempty,min=0"`
	Width       int    `json:"width" binding:"omitempty,min=0"`
	Height      int    `json:"height" binding:"omitempty,min=0"`
	Format      string `json:"format" binding:"max=20"`
	CategoryID  uint   `json:"category_id" binding:"omitempty,min=0"`
}

// VideoUpdateRequest 更新视频请求参数
type VideoUpdateRequest struct {
	Title       string `json:"title" binding:"required,max=200"`
	Description string `json:"description" binding:"max=1000"`
	PlayUrl     string `json:"play_url" binding:"max=500"`
	CoverUrl    string `json:"cover_url" binding:"max=500"`
	Duration    int    `json:"duration" binding:"omitempty,min=0"`
	FileSize    int64  `json:"file_size" binding:"omitempty,min=0"`
	Width       int    `json:"width" binding:"omitempty,min=0"`
	Height      int    `json:"height" binding:"omitempty,min=0"`
	Format      string `json:"format" binding:"max=20"`
	Status      int    `json:"status" binding:"omitempty,oneof=1 2 3 4"`
	CategoryID  uint   `json:"category_id" binding:"omitempty,min=0"`
}

// VideoResponse 视频响应数据
type VideoResponse struct {
	ID          uint              `json:"id"`
	Title       string            `json:"title"`
	Description string            `json:"description"`
	FileName    string            `json:"file_name"`
	FileId      string            `json:"file_id"`
	PlayUrl     string            `json:"play_url"`
	CoverUrl    string            `json:"cover_url"`
	Duration    int               `json:"duration"`
	FileSize    int64             `json:"file_size"`
	Width       int               `json:"width"`
	Height      int               `json:"height"`
	Format      string            `json:"format"`
	Status      int               `json:"status"`
	StatusText  string            `json:"status_text"`
	CategoryID  uint              `json:"category_id"`
	Category    *CategoryResponse `json:"category,omitempty"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (v *Video) ToResponse() *VideoResponse {
	statusText := ""
	switch v.Status {
	case 1:
		statusText = "上传中"
	case 2:
		statusText = "转码中"
	case 3:
		statusText = "正常"
	case 4:
		statusText = "失败"
	}

	resp := &VideoResponse{
		ID:          v.ID,
		Title:       v.Title,
		Description: v.Description,
		FileName:    v.FileName,
		FileId:      v.FileId,
		PlayUrl:     v.PlayUrl,
		CoverUrl:    v.CoverUrl,
		Duration:    v.Duration,
		FileSize:    v.FileSize,
		Width:       v.Width,
		Height:      v.Height,
		Format:      v.Format,
		Status:      v.Status,
		StatusText:  statusText,
		CategoryID:  v.CategoryID,
		CreatedAt:   v.CreatedAt,
		UpdatedAt:   v.UpdatedAt,
	}

	if v.Category != nil {
		resp.Category = v.Category.ToResponse()
	}

	return resp
}
