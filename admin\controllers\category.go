package controllers

import (
	"ai_select_admin/database"
	"ai_select_admin/logger"
	"ai_select_admin/models"
	"ai_select_admin/utils"
	"math"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type CategoryController struct{}

// ListCategories 获取分类列表
func (cc *CategoryController) ListCategories(c *gin.Context) {
	var req models.CategoryListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := database.DB.Model(&models.Category{})

	// 按名称搜索
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 按状态筛选
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}

	// 按父级分类筛选
	if req.ParentID != nil {
		query = query.Where("parent_id = ?", *req.ParentID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Errorf("获取分类总数失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 分页查询
	var categories []models.Category
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("sort ASC, id ASC").Offset(offset).Limit(req.PageSize).Find(&categories).Error; err != nil {
		logger.Errorf("查询分类列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 转换为响应格式
	var responses []*models.CategoryResponse
	for _, category := range categories {
		responses = append(responses, category.ToResponse())
	}

	// 计算分页信息
	pages := int(math.Ceil(float64(total) / float64(req.PageSize)))
	pageInfo := utils.PageInfo{
		Current: req.Page,
		Size:    req.PageSize,
		Total:   total,
		Pages:   pages,
	}

	utils.PageSuccess(c, responses, pageInfo)
}

// GetCategory 获取分类详情
func (cc *CategoryController) GetCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的分类ID")
		return
	}

	var category models.Category
	if err := database.DB.First(&category, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "分类不存在")
		} else {
			logger.Errorf("查询分类失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	utils.Success(c, category.ToResponse())
}

// CreateCategory 创建分类
func (cc *CategoryController) CreateCategory(c *gin.Context) {
	var req models.CategoryCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 检查分类名称是否已存在
	var count int64
	if err := database.DB.Model(&models.Category{}).Where("name = ? AND parent_id = ?", req.Name, req.ParentID).Count(&count).Error; err != nil {
		logger.Errorf("检查分类名称失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "分类名称已存在")
		return
	}

	// 如果指定了父级分类，检查父级分类是否存在
	if req.ParentID > 0 {
		var parentCategory models.Category
		if err := database.DB.First(&parentCategory, req.ParentID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "父级分类不存在")
			} else {
				logger.Errorf("查询父级分类失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 创建分类
	category := models.Category{
		Name:        req.Name,
		Description: req.Description,
		Sort:        req.Sort,
		Status:      req.Status,
		ParentID:    req.ParentID,
	}

	// 设置默认状态
	if category.Status == 0 {
		category.Status = 1
	}

	if err := database.DB.Create(&category).Error; err != nil {
		logger.Errorf("创建分类失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("创建分类成功: %s", category.Name)
	utils.SuccessWithMsg(c, "创建成功", category.ToResponse())
}

// UpdateCategory 更新分类
func (cc *CategoryController) UpdateCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的分类ID")
		return
	}

	var req models.CategoryUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "参数错误: "+err.Error())
		return
	}

	// 查找分类
	var category models.Category
	if err := database.DB.First(&category, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "分类不存在")
		} else {
			logger.Errorf("查询分类失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 检查分类名称是否已存在（排除当前分类）
	var count int64
	if err := database.DB.Model(&models.Category{}).Where("name = ? AND parent_id = ? AND id != ?", req.Name, req.ParentID, uint(id)).Count(&count).Error; err != nil {
		logger.Errorf("检查分类名称失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if count > 0 {
		utils.BadRequest(c, "分类名称已存在")
		return
	}

	// 如果指定了父级分类，检查父级分类是否存在且不是自己
	if req.ParentID > 0 {
		if req.ParentID == uint(id) {
			utils.BadRequest(c, "不能将自己设为父级分类")
			return
		}

		var parentCategory models.Category
		if err := database.DB.First(&parentCategory, req.ParentID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				utils.BadRequest(c, "父级分类不存在")
			} else {
				logger.Errorf("查询父级分类失败: %v", err)
				utils.InternalServerError(c, "系统错误")
			}
			return
		}
	}

	// 更新分类
	category.Name = req.Name
	category.Description = req.Description
	category.Sort = req.Sort
	category.Status = req.Status
	category.ParentID = req.ParentID

	if err := database.DB.Save(&category).Error; err != nil {
		logger.Errorf("更新分类失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("更新分类成功: %s", category.Name)
	utils.SuccessWithMsg(c, "更新成功", category.ToResponse())
}

// DeleteCategory 删除分类
func (cc *CategoryController) DeleteCategory(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的分类ID")
		return
	}

	// 查找分类
	var category models.Category
	if err := database.DB.First(&category, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "分类不存在")
		} else {
			logger.Errorf("查询分类失败: %v", err)
			utils.InternalServerError(c, "系统错误")
		}
		return
	}

	// 检查是否有子分类
	var childCount int64
	if err := database.DB.Model(&models.Category{}).Where("parent_id = ?", uint(id)).Count(&childCount).Error; err != nil {
		logger.Errorf("检查子分类失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}
	if childCount > 0 {
		utils.BadRequest(c, "该分类下还有子分类，无法删除")
		return
	}

	// 软删除分类
	if err := database.DB.Delete(&category).Error; err != nil {
		logger.Errorf("删除分类失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	logger.Infof("删除分类成功: %s", category.Name)
	utils.SuccessWithMsg(c, "删除成功", nil)
}

// GetCategoryTree 获取分类树
func (cc *CategoryController) GetCategoryTree(c *gin.Context) {
	var categories []models.Category
	if err := database.DB.Where("status = 1").Order("sort ASC, id ASC").Find(&categories).Error; err != nil {
		logger.Errorf("查询分类列表失败: %v", err)
		utils.InternalServerError(c, "系统错误")
		return
	}

	// 构建分类树
	tree := buildCategoryTree(categories, 0)
	utils.Success(c, tree)
}

// buildCategoryTree 构建分类树
func buildCategoryTree(categories []models.Category, parentID uint) []*models.CategoryResponse {
	var tree []*models.CategoryResponse

	for _, category := range categories {
		if category.ParentID == parentID {
			response := category.ToResponse()
			response.Children = buildCategoryTree(categories, category.ID)
			tree = append(tree, response)
		}
	}

	return tree
}
