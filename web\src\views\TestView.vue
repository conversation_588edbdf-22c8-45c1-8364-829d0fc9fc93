<template>
  <div style="padding: 20px;">
    <h1>测试页面</h1>
    <p>如果你能看到这个页面，说明路由工作正常</p>
    <p>当前路径：{{ $route.path }}</p>
    <p>用户信息：{{ userInfo?.username || '未登录' }}</p>
    
    <el-button type="primary" @click="goToDashboard">
      去仪表盘
    </el-button>
    
    <el-button type="danger" @click="logout">
      退出登录
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const userInfo = computed(() => authStore.userInfo)

const goToDashboard = () => {
  router.push('/dashboard')
}

const logout = () => {
  authStore.logout()
  router.push('/login')
}
</script>
