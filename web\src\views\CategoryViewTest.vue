<template>
  <div class="category-container">
    <h2>课程分类管理</h2>
    
    <!-- 测试API导入 -->
    <el-button type="primary" @click="testAPI">测试API导入</el-button>
    
    <!-- 基础表格 -->
    <el-table :data="categoryList" border style="width: 100%; margin-top: 20px;">
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="分类名称" />
      <el-table-column prop="status" label="状态" />
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { getCategoryList } from '@/api/category'
import type { Category } from '@/types/category'

const categoryList = ref<Category[]>([])

const testAPI = async () => {
  try {
    ElMessage.info('开始测试API...')
    const response = await getCategoryList({ page: 1, page_size: 10 })
    categoryList.value = response.data || []
    ElMessage.success('API测试成功！')
  } catch (error) {
    ElMessage.error('API测试失败: ' + error)
    console.error('API Error:', error)
  }
}
</script>

<style scoped>
.category-container {
  padding: 20px;
}
</style>
