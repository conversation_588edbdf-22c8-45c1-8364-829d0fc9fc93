package config

import (
	"github.com/spf13/viper"
)

// COSConfig COS配置
type COSConfig struct {
	SecretId        string
	SecretKey       string
	AppId           string
	Bucket          string
	Region          string
	DurationSeconds int
	AllowActions    []string
}

type Config struct {
	Mode     string `mapstructure:"mode"`
	Port     string `mapstructure:"port"`
	Database struct {
		Host     string `mapstructure:"host"`
		Port     string `mapstructure:"port"`
		Username string `mapstructure:"username"`
		Password string `mapstructure:"password"`
		Database string `mapstructure:"database"`
		Charset  string `mapstructure:"charset"`
		Prefix   string `mapstructure:"prefix"`
	} `mapstructure:"database"`
	JWT struct {
		Secret     string `mapstructure:"secret"`
		ExpireTime int    `mapstructure:"expire_time"`
	} `mapstructure:"jwt"`
	Log struct {
		Level string `mapstructure:"level"`
		File  string `mapstructure:"file"`
	} `mapstructure:"log"`
	TencentCloud struct {
		SecretId  string `mapstructure:"secret_id"`
		SecretKey string `mapstructure:"secret_key"`
		Cos       struct {
			AppId           string   `mapstructure:"app_id"`
			Bucket          string   `mapstructure:"bucket"`
			Region          string   `mapstructure:"region"`
			DurationSeconds int      `mapstructure:"duration_seconds"`
			AllowActions    []string `mapstructure:"allow_actions"`
			Permission      struct {
				LimitExt           bool     `mapstructure:"limit_ext"`
				ExtWhiteList       []string `mapstructure:"ext_white_list"`
				LimitContentType   bool     `mapstructure:"limit_content_type"`
				LimitContentLength bool     `mapstructure:"limit_content_length"`
				MaxSize            int64    `mapstructure:"max_size"`
			} `mapstructure:"permission"`
		} `mapstructure:"cos"`
	} `mapstructure:"tencentcloud"`
}

var AppConfig *Config

func Init() error {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./config")
	viper.AddConfigPath(".")

	if err := viper.ReadInConfig(); err != nil {
		return err
	}

	AppConfig = &Config{}
	if err := viper.Unmarshal(AppConfig); err != nil {
		return err
	}
	return nil
}

type Permission struct {
	LimitExt           bool     `json:"limitExt"`
	ExtWhiteList       []string `json:"extWhiteList"`
	LimitContentType   bool     `json:"limitContentType"`
	LimitContentLength bool     `json:"limitContentLength"`
	MaxSize            int64    `json:"maxSize"` // 最大文件大小，单位字节
}

// GetImagePermission 获取图片上传权限配置
func GetImagePermission() *Permission {
	return &Permission{
		LimitExt:           AppConfig.TencentCloud.Cos.Permission.LimitExt,
		ExtWhiteList:       AppConfig.TencentCloud.Cos.Permission.ExtWhiteList,
		LimitContentType:   AppConfig.TencentCloud.Cos.Permission.LimitContentType,
		LimitContentLength: AppConfig.TencentCloud.Cos.Permission.LimitContentLength,
		MaxSize:            AppConfig.TencentCloud.Cos.Permission.MaxSize,
	}
}

func GetCOSConfig() *COSConfig {
	return &COSConfig{
		SecretId:        AppConfig.TencentCloud.SecretId,            // 从key.txt获取
		SecretKey:       AppConfig.TencentCloud.SecretKey,           // 从key.txt获取
		AppId:           AppConfig.TencentCloud.Cos.AppId,           // 从bucket名称提取
		Bucket:          AppConfig.TencentCloud.Cos.Bucket,          // 实际的存储桶名称
		Region:          AppConfig.TencentCloud.Cos.Region,          // 南京地域
		DurationSeconds: AppConfig.TencentCloud.Cos.DurationSeconds, // 临时密钥有效期30分钟
		AllowActions:    AppConfig.TencentCloud.Cos.AllowActions,    // 允许的操作列表
	}
}
