import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/LoginView.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      name: 'layout',
      component: () => import('@/layout/MainLayout.vue'),
      meta: { requiresAuth: true },
      redirect: '/admin',
      children: [
        {
          path: '/admin',
          name: 'admin',
          component: () => import('@/views/AdminView.vue'),
          meta: { title: '管理员管理' }
        },
        {
          path: '/category',
          name: 'category',
          component: () => import('@/views/CategoryView.vue'),
          meta: { title: '课程分类管理' }
        },
        {
          path: '/video',
          name: 'video',
          component: () => import('@/views/VideoView.vue'),
          meta: { title: '视频管理' }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()

  // 初始化认证状态
  authStore.initAuth()

  if (to.meta.requiresAuth !== false && !authStore.isLoggedIn) {
    next('/login')
  } else if (to.path === '/login' && authStore.isLoggedIn) {
    next('/')
  } else {
    next()
  }
})

export default router
