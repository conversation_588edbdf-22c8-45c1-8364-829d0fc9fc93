<template>
  <div class="dashboard">
    <div class="page-header">
      <h1>仪表盘</h1>
      <p>欢迎使用AI选择管理系统</p>
      <p>当前用户：{{ userInfo?.username || "未知" }}</p>
    </div>

    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon admin">
              <el-icon size="40"><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ adminCount }}</div>
              <div class="stats-label">管理员总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user">
              <el-icon size="40"><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ userCount }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon active">
              <el-icon size="40"><CircleCheck /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ activeUserCount }}</div>
              <div class="stats-label">活跃用户</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon today">
              <el-icon size="40"><Calendar /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ todayLoginCount }}</div>
              <div class="stats-label">今日登录</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="content-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/admin')">
              <el-icon><UserFilled /></el-icon>
              管理员管理
            </el-button>
            <el-button type="success" @click="$router.push('/user')">
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          <div class="system-info">
            <div class="info-item">
              <span class="label">系统版本：</span>
              <span class="value">v1.0.0</span>
            </div>
            <div class="info-item">
              <span class="label">当前用户：</span>
              <span class="value">{{ userInfo?.username }}</span>
            </div>
            <div class="info-item">
              <span class="label">登录时间：</span>
              <span class="value">{{ formatDate(userInfo?.last_login) }}</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useAuthStore } from "@/stores/auth";
import {
  UserFilled,
  User,
  CircleCheck,
  Calendar,
} from "@element-plus/icons-vue";

const authStore = useAuthStore();

const adminCount = ref(0);
const userCount = ref(0);
const activeUserCount = ref(0);
const todayLoginCount = ref(0);

const userInfo = computed(() => authStore.userInfo);

const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return "未知";
  return new Date(dateString).toLocaleString("zh-CN");
};

onMounted(() => {
  // 这里可以调用API获取统计数据
  // 暂时使用模拟数据
  adminCount.value = 5;
  userCount.value = 128;
  activeUserCount.value = 89;
  todayLoginCount.value = 23;
});
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.page-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stats-icon.admin {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.user {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.active {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.content-row {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

.quick-actions .el-button {
  flex: 1;
  height: 60px;
  font-size: 16px;
}

.system-info {
  padding: 10px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: #666;
  font-weight: 500;
}

.value {
  color: #333;
}
</style>
