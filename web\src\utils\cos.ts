/**
 * 腾讯云COS上传工具
 */
import COS from 'cos-js-sdk-v5'
import request from './request'

// COS上传响应接口
interface COSUploadResponse {
  Location: string
  Bucket: string
  Key: string
  ETag: string
}

// 上传凭证接口
interface UploadCredentials {
  credentials: {
    tmpSecretID?: string  // 后端返回的字段名
    tmpSecretId?: string  // 兼容字段名
    tmpSecretKey: string
    sessionToken: string
  }
  startTime: number
  expiredTime: number
  bucket: string
  region: string
  key: string
}

/**
 * 上传文件到COS
 * @param file 要上传的文件
 * @returns Promise<string> 返回文件的访问URL
 */
export async function uploadToCOS(file: File): Promise<string> {
  try {
    // 1. 获取上传凭证
    const response = await request.get('/public/upload/credentials', {
      params: { filename: file.name }
    })

    if (response.data?.code !== 200) {
      throw new Error(response.data?.msg || '获取上传凭证失败')
    }

    const credentials: UploadCredentials = response.data?.data

    // 2. 校验凭证参数
    const credentialsData = credentials.credentials
    const tmpSecretId = credentialsData.tmpSecretID || credentialsData.tmpSecretId
    const tmpSecretKey = credentialsData.tmpSecretKey
    const sessionToken = credentialsData.sessionToken
    const { bucket, region, key, startTime, expiredTime } = credentials

    const params = { tmpSecretId, tmpSecretKey, sessionToken, bucket, region, key }
    const emptyParam = Object.keys(params).find(k => !params[k as keyof typeof params])
    if (emptyParam) {
      throw new Error(`参数错误: ${emptyParam} 不能为空`)
    }

    // 3. 创建COS实例
    const cos = new COS({
      SecretId: tmpSecretId,
      SecretKey: tmpSecretKey,
      SecurityToken: sessionToken,
      StartTime: startTime,
      ExpiredTime: expiredTime,
    })

    // 4. 上传文件
    const uploadResult = await new Promise<COSUploadResponse>((resolve, reject) => {
      cos.uploadFile({
        Bucket: bucket,
        Region: region,
        Key: key,
        Body: file,
        onProgress: function(progressData) {
          console.log('上传进度：', progressData)
        }
      }, function (err, data) {
        if (err) {
          reject(err)
        } else {
          resolve(data as COSUploadResponse)
        }
      })
    })

    // 5. 返回文件访问URL
    const fileUrl = `https://${bucket}.cos.${region}.myqcloud.com/${key}`
    console.log('文件上传成功:', fileUrl)
    return fileUrl

  } catch (error) {
    console.error('COS上传失败:', error)
    throw error
  }
}

/**
 * 获取COS文件访问URL
 * @param key COS对象键
 * @returns Promise<string> 返回文件的访问URL
 */
export async function getCOSUrl(key: string): Promise<string> {
  if (!key) return ''
  
  // 如果已经是完整URL，直接返回
  if (key.startsWith('http://') || key.startsWith('https://')) {
    return key
  }

  try {
    const response = await request.get('/public/upload/cos-url', {
      params: { key }
    })

    if (response.data?.code === 200) {
      return response.data?.data?.url || ''
    }
  } catch (error) {
    console.error('获取COS URL失败:', error)
  }

  return key
}

/**
 * 验证文件类型和大小
 * @param file 文件对象
 * @param options 验证选项
 */
export function validateFile(file: File, options: {
  maxSize?: number // 最大文件大小，单位字节
  allowedTypes?: string[] // 允许的文件类型
} = {}) {
  const { maxSize = 5 * 1024 * 1024, allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/bmp'] } = options

  // 检查文件类型
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    throw new Error(`不支持的文件格式，仅支持：${allowedTypes.join(', ')}`)
  }

  // 检查文件大小
  if (file.size > maxSize) {
    const maxSizeMB = (maxSize / 1024 / 1024).toFixed(1)
    throw new Error(`文件大小不能超过 ${maxSizeMB}MB`)
  }

  return true
}
