/**
 * 图片URL处理工具
 */

// 获取完整的图片URL
export function getImageUrl(path: string): string {
  if (!path) return '';

  // 如果已经是完整URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path;
  }

  // 如果是COS对象键，构建COS URL（兼容旧数据）
  if (path.startsWith('avatars/')) {
    return `https://zxw-1300870289.cos.ap-nanjing.myqcloud.com/${path}`;
  }

  // 如果是相对路径，直接返回（兼容旧的本地存储）
  // 开发环境：通过Vite代理访问后端
  // 生产环境：nginx会处理/uploads路径的静态文件服务
  if (path.startsWith('/uploads/')) {
    return path;
  }

  return path;
}

// 获取头像URL（带默认头像）
export function getAvatarUrl(avatar: string, defaultAvatar?: string): string {
  if (avatar) {
    return getImageUrl(avatar);
  }
  
  // 返回默认头像或占位符
  return defaultAvatar || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iMzUiIHI9IjE1IiBmaWxsPSIjQ0NDIi8+CjxwYXRoIGQ9Ik0yMCA4MEM0MCA2MCA2MCA2MCA4MCA4MEgyMFoiIGZpbGw9IiNDQ0MiLz4KPC9zdmc+';
}
