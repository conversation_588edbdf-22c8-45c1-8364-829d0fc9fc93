package middleware

import (
	"ai_select_admin/logger"
	"bytes"
	"io"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoggerMiddleware 请求日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logger.Logger.WithFields(logrus.Fields{
			"status_code": param.StatusCode,
			"latency":     param.Latency,
			"client_ip":   param.ClientIP,
			"method":      param.Method,
			"path":        param.Path,
			"user_agent":  param.Request.UserAgent(),
			"error":       param.ErrorMessage,
			"body_size":   param.BodySize,
			"timestamp":   param.TimeStamp.Format("2006-01-02 15:04:05"),
		}).Info("HTTP Request")

		return ""
	})
}

// RequestBodyLoggerMiddleware 请求体日志中间件
func RequestBodyLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只记录POST、PUT、PATCH请求的body
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			// 读取请求体
			bodyBytes, err := io.ReadAll(c.Request.Body)
			if err != nil {
				logger.Errorf("读取请求体失败: %v", err)
				c.Next()
				return
			}

			// 恢复请求体，以便后续处理
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

			// 记录请求体（注意：生产环境中可能需要过滤敏感信息）
			if len(bodyBytes) > 0 && len(bodyBytes) < 1024 { // 只记录小于1KB的请求体
				logger.Logger.WithFields(logrus.Fields{
					"method": c.Request.Method,
					"path":   c.Request.URL.Path,
					"body":   string(bodyBytes),
				}).Debug("Request Body")
			}
		}

		c.Next()
	}
}

// ErrorLoggerMiddleware 错误日志中间件
func ErrorLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 记录错误
		if len(c.Errors) > 0 {
			for _, err := range c.Errors {
				logger.Logger.WithFields(logrus.Fields{
					"method": c.Request.Method,
					"path":   c.Request.URL.Path,
					"error":  err.Error(),
					"type":   err.Type,
				}).Error("Request Error")
			}
		}
	}
}
