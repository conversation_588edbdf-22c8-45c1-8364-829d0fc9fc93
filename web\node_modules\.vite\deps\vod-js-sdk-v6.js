import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/js-sha1/src/sha1.js
var require_sha1 = __commonJS({
  "node_modules/js-sha1/src/sha1.js"(exports, module) {
    (function() {
      "use strict";
      var root = typeof window === "object" ? window : {};
      var NODE_JS = !root.JS_SHA1_NO_NODE_JS && typeof process === "object" && process.versions && process.versions.node;
      if (NODE_JS) {
        root = global;
      }
      var COMMON_JS = !root.JS_SHA1_NO_COMMON_JS && typeof module === "object" && module.exports;
      var AMD = typeof define === "function" && define.amd;
      var HEX_CHARS = "0123456789abcdef".split("");
      var EXTRA = [-2147483648, 8388608, 32768, 128];
      var SHIFT = [24, 16, 8, 0];
      var OUTPUT_TYPES = ["hex", "array", "digest", "arrayBuffer"];
      var blocks = [];
      var createOutputMethod = function(outputType) {
        return function(message) {
          return new Sha1(true).update(message)[outputType]();
        };
      };
      var createMethod = function() {
        var method2 = createOutputMethod("hex");
        if (NODE_JS) {
          method2 = nodeWrap(method2);
        }
        method2.create = function() {
          return new Sha1();
        };
        method2.update = function(message) {
          return method2.create().update(message);
        };
        for (var i = 0; i < OUTPUT_TYPES.length; ++i) {
          var type = OUTPUT_TYPES[i];
          method2[type] = createOutputMethod(type);
        }
        return method2;
      };
      var nodeWrap = function(method) {
        var crypto = eval("require('crypto')");
        var Buffer = eval("require('buffer').Buffer");
        var nodeMethod = function(message) {
          if (typeof message === "string") {
            return crypto.createHash("sha1").update(message, "utf8").digest("hex");
          } else if (message.constructor === ArrayBuffer) {
            message = new Uint8Array(message);
          } else if (message.length === void 0) {
            return method(message);
          }
          return crypto.createHash("sha1").update(new Buffer(message)).digest("hex");
        };
        return nodeMethod;
      };
      function Sha1(sharedMemory) {
        if (sharedMemory) {
          blocks[0] = blocks[16] = blocks[1] = blocks[2] = blocks[3] = blocks[4] = blocks[5] = blocks[6] = blocks[7] = blocks[8] = blocks[9] = blocks[10] = blocks[11] = blocks[12] = blocks[13] = blocks[14] = blocks[15] = 0;
          this.blocks = blocks;
        } else {
          this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
        }
        this.h0 = 1732584193;
        this.h1 = 4023233417;
        this.h2 = 2562383102;
        this.h3 = 271733878;
        this.h4 = 3285377520;
        this.block = this.start = this.bytes = this.hBytes = 0;
        this.finalized = this.hashed = false;
        this.first = true;
      }
      Sha1.prototype.update = function(message) {
        if (this.finalized) {
          return;
        }
        var notString = typeof message !== "string";
        if (notString && message.constructor === root.ArrayBuffer) {
          message = new Uint8Array(message);
        }
        var code, index = 0, i, length = message.length || 0, blocks2 = this.blocks;
        while (index < length) {
          if (this.hashed) {
            this.hashed = false;
            blocks2[0] = this.block;
            blocks2[16] = blocks2[1] = blocks2[2] = blocks2[3] = blocks2[4] = blocks2[5] = blocks2[6] = blocks2[7] = blocks2[8] = blocks2[9] = blocks2[10] = blocks2[11] = blocks2[12] = blocks2[13] = blocks2[14] = blocks2[15] = 0;
          }
          if (notString) {
            for (i = this.start; index < length && i < 64; ++index) {
              blocks2[i >> 2] |= message[index] << SHIFT[i++ & 3];
            }
          } else {
            for (i = this.start; index < length && i < 64; ++index) {
              code = message.charCodeAt(index);
              if (code < 128) {
                blocks2[i >> 2] |= code << SHIFT[i++ & 3];
              } else if (code < 2048) {
                blocks2[i >> 2] |= (192 | code >> 6) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | code & 63) << SHIFT[i++ & 3];
              } else if (code < 55296 || code >= 57344) {
                blocks2[i >> 2] |= (224 | code >> 12) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | code >> 6 & 63) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | code & 63) << SHIFT[i++ & 3];
              } else {
                code = 65536 + ((code & 1023) << 10 | message.charCodeAt(++index) & 1023);
                blocks2[i >> 2] |= (240 | code >> 18) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | code >> 12 & 63) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | code >> 6 & 63) << SHIFT[i++ & 3];
                blocks2[i >> 2] |= (128 | code & 63) << SHIFT[i++ & 3];
              }
            }
          }
          this.lastByteIndex = i;
          this.bytes += i - this.start;
          if (i >= 64) {
            this.block = blocks2[16];
            this.start = i - 64;
            this.hash();
            this.hashed = true;
          } else {
            this.start = i;
          }
        }
        if (this.bytes > 4294967295) {
          this.hBytes += this.bytes / 4294967296 << 0;
          this.bytes = this.bytes % 4294967296;
        }
        return this;
      };
      Sha1.prototype.finalize = function() {
        if (this.finalized) {
          return;
        }
        this.finalized = true;
        var blocks2 = this.blocks, i = this.lastByteIndex;
        blocks2[16] = this.block;
        blocks2[i >> 2] |= EXTRA[i & 3];
        this.block = blocks2[16];
        if (i >= 56) {
          if (!this.hashed) {
            this.hash();
          }
          blocks2[0] = this.block;
          blocks2[16] = blocks2[1] = blocks2[2] = blocks2[3] = blocks2[4] = blocks2[5] = blocks2[6] = blocks2[7] = blocks2[8] = blocks2[9] = blocks2[10] = blocks2[11] = blocks2[12] = blocks2[13] = blocks2[14] = blocks2[15] = 0;
        }
        blocks2[14] = this.hBytes << 3 | this.bytes >>> 29;
        blocks2[15] = this.bytes << 3;
        this.hash();
      };
      Sha1.prototype.hash = function() {
        var a = this.h0, b = this.h1, c = this.h2, d = this.h3, e = this.h4;
        var f, j, t, blocks2 = this.blocks;
        for (j = 16; j < 80; ++j) {
          t = blocks2[j - 3] ^ blocks2[j - 8] ^ blocks2[j - 14] ^ blocks2[j - 16];
          blocks2[j] = t << 1 | t >>> 31;
        }
        for (j = 0; j < 20; j += 5) {
          f = b & c | ~b & d;
          t = a << 5 | a >>> 27;
          e = t + f + e + 1518500249 + blocks2[j] << 0;
          b = b << 30 | b >>> 2;
          f = a & b | ~a & c;
          t = e << 5 | e >>> 27;
          d = t + f + d + 1518500249 + blocks2[j + 1] << 0;
          a = a << 30 | a >>> 2;
          f = e & a | ~e & b;
          t = d << 5 | d >>> 27;
          c = t + f + c + 1518500249 + blocks2[j + 2] << 0;
          e = e << 30 | e >>> 2;
          f = d & e | ~d & a;
          t = c << 5 | c >>> 27;
          b = t + f + b + 1518500249 + blocks2[j + 3] << 0;
          d = d << 30 | d >>> 2;
          f = c & d | ~c & e;
          t = b << 5 | b >>> 27;
          a = t + f + a + 1518500249 + blocks2[j + 4] << 0;
          c = c << 30 | c >>> 2;
        }
        for (; j < 40; j += 5) {
          f = b ^ c ^ d;
          t = a << 5 | a >>> 27;
          e = t + f + e + 1859775393 + blocks2[j] << 0;
          b = b << 30 | b >>> 2;
          f = a ^ b ^ c;
          t = e << 5 | e >>> 27;
          d = t + f + d + 1859775393 + blocks2[j + 1] << 0;
          a = a << 30 | a >>> 2;
          f = e ^ a ^ b;
          t = d << 5 | d >>> 27;
          c = t + f + c + 1859775393 + blocks2[j + 2] << 0;
          e = e << 30 | e >>> 2;
          f = d ^ e ^ a;
          t = c << 5 | c >>> 27;
          b = t + f + b + 1859775393 + blocks2[j + 3] << 0;
          d = d << 30 | d >>> 2;
          f = c ^ d ^ e;
          t = b << 5 | b >>> 27;
          a = t + f + a + 1859775393 + blocks2[j + 4] << 0;
          c = c << 30 | c >>> 2;
        }
        for (; j < 60; j += 5) {
          f = b & c | b & d | c & d;
          t = a << 5 | a >>> 27;
          e = t + f + e - 1894007588 + blocks2[j] << 0;
          b = b << 30 | b >>> 2;
          f = a & b | a & c | b & c;
          t = e << 5 | e >>> 27;
          d = t + f + d - 1894007588 + blocks2[j + 1] << 0;
          a = a << 30 | a >>> 2;
          f = e & a | e & b | a & b;
          t = d << 5 | d >>> 27;
          c = t + f + c - 1894007588 + blocks2[j + 2] << 0;
          e = e << 30 | e >>> 2;
          f = d & e | d & a | e & a;
          t = c << 5 | c >>> 27;
          b = t + f + b - 1894007588 + blocks2[j + 3] << 0;
          d = d << 30 | d >>> 2;
          f = c & d | c & e | d & e;
          t = b << 5 | b >>> 27;
          a = t + f + a - 1894007588 + blocks2[j + 4] << 0;
          c = c << 30 | c >>> 2;
        }
        for (; j < 80; j += 5) {
          f = b ^ c ^ d;
          t = a << 5 | a >>> 27;
          e = t + f + e - 899497514 + blocks2[j] << 0;
          b = b << 30 | b >>> 2;
          f = a ^ b ^ c;
          t = e << 5 | e >>> 27;
          d = t + f + d - 899497514 + blocks2[j + 1] << 0;
          a = a << 30 | a >>> 2;
          f = e ^ a ^ b;
          t = d << 5 | d >>> 27;
          c = t + f + c - 899497514 + blocks2[j + 2] << 0;
          e = e << 30 | e >>> 2;
          f = d ^ e ^ a;
          t = c << 5 | c >>> 27;
          b = t + f + b - 899497514 + blocks2[j + 3] << 0;
          d = d << 30 | d >>> 2;
          f = c ^ d ^ e;
          t = b << 5 | b >>> 27;
          a = t + f + a - 899497514 + blocks2[j + 4] << 0;
          c = c << 30 | c >>> 2;
        }
        this.h0 = this.h0 + a << 0;
        this.h1 = this.h1 + b << 0;
        this.h2 = this.h2 + c << 0;
        this.h3 = this.h3 + d << 0;
        this.h4 = this.h4 + e << 0;
      };
      Sha1.prototype.hex = function() {
        this.finalize();
        var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4;
        return HEX_CHARS[h0 >> 28 & 15] + HEX_CHARS[h0 >> 24 & 15] + HEX_CHARS[h0 >> 20 & 15] + HEX_CHARS[h0 >> 16 & 15] + HEX_CHARS[h0 >> 12 & 15] + HEX_CHARS[h0 >> 8 & 15] + HEX_CHARS[h0 >> 4 & 15] + HEX_CHARS[h0 & 15] + HEX_CHARS[h1 >> 28 & 15] + HEX_CHARS[h1 >> 24 & 15] + HEX_CHARS[h1 >> 20 & 15] + HEX_CHARS[h1 >> 16 & 15] + HEX_CHARS[h1 >> 12 & 15] + HEX_CHARS[h1 >> 8 & 15] + HEX_CHARS[h1 >> 4 & 15] + HEX_CHARS[h1 & 15] + HEX_CHARS[h2 >> 28 & 15] + HEX_CHARS[h2 >> 24 & 15] + HEX_CHARS[h2 >> 20 & 15] + HEX_CHARS[h2 >> 16 & 15] + HEX_CHARS[h2 >> 12 & 15] + HEX_CHARS[h2 >> 8 & 15] + HEX_CHARS[h2 >> 4 & 15] + HEX_CHARS[h2 & 15] + HEX_CHARS[h3 >> 28 & 15] + HEX_CHARS[h3 >> 24 & 15] + HEX_CHARS[h3 >> 20 & 15] + HEX_CHARS[h3 >> 16 & 15] + HEX_CHARS[h3 >> 12 & 15] + HEX_CHARS[h3 >> 8 & 15] + HEX_CHARS[h3 >> 4 & 15] + HEX_CHARS[h3 & 15] + HEX_CHARS[h4 >> 28 & 15] + HEX_CHARS[h4 >> 24 & 15] + HEX_CHARS[h4 >> 20 & 15] + HEX_CHARS[h4 >> 16 & 15] + HEX_CHARS[h4 >> 12 & 15] + HEX_CHARS[h4 >> 8 & 15] + HEX_CHARS[h4 >> 4 & 15] + HEX_CHARS[h4 & 15];
      };
      Sha1.prototype.toString = Sha1.prototype.hex;
      Sha1.prototype.digest = function() {
        this.finalize();
        var h0 = this.h0, h1 = this.h1, h2 = this.h2, h3 = this.h3, h4 = this.h4;
        return [
          h0 >> 24 & 255,
          h0 >> 16 & 255,
          h0 >> 8 & 255,
          h0 & 255,
          h1 >> 24 & 255,
          h1 >> 16 & 255,
          h1 >> 8 & 255,
          h1 & 255,
          h2 >> 24 & 255,
          h2 >> 16 & 255,
          h2 >> 8 & 255,
          h2 & 255,
          h3 >> 24 & 255,
          h3 >> 16 & 255,
          h3 >> 8 & 255,
          h3 & 255,
          h4 >> 24 & 255,
          h4 >> 16 & 255,
          h4 >> 8 & 255,
          h4 & 255
        ];
      };
      Sha1.prototype.array = Sha1.prototype.digest;
      Sha1.prototype.arrayBuffer = function() {
        this.finalize();
        var buffer = new ArrayBuffer(20);
        var dataView = new DataView(buffer);
        dataView.setUint32(0, this.h0);
        dataView.setUint32(4, this.h1);
        dataView.setUint32(8, this.h2);
        dataView.setUint32(12, this.h3);
        dataView.setUint32(16, this.h4);
        return buffer;
      };
      var exports = createMethod();
      if (COMMON_JS) {
        module.exports = exports;
      } else {
        root.sha1 = exports;
        if (AMD) {
          define(function() {
            return exports;
          });
        }
      }
    })();
  }
});

// node_modules/vod-js-sdk-v6/lib/src/base.js
var require_base = __commonJS({
  "node_modules/vod-js-sdk-v6/lib/src/base.js"(exports2, module2) {
    !function(e, t) {
      "object" == typeof exports2 && "object" == typeof module2 ? module2.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports2 ? exports2.COS = t() : e.COS = t();
    }(exports2, function() {
      return function(e) {
        var t = {};
        function r(n) {
          if (t[n]) return t[n].exports;
          var o = t[n] = { i: n, l: false, exports: {} };
          return e[n].call(o.exports, o, o.exports, r), o.l = true, o.exports;
        }
        return r.m = e, r.c = t, r.d = function(e2, t2, n) {
          r.o(e2, t2) || Object.defineProperty(e2, t2, { enumerable: true, get: n });
        }, r.r = function(e2) {
          "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e2, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e2, "__esModule", { value: true });
        }, r.t = function(e2, t2) {
          if (1 & t2 && (e2 = r(e2)), 8 & t2) return e2;
          if (4 & t2 && "object" == typeof e2 && e2 && e2.__esModule) return e2;
          var n = /* @__PURE__ */ Object.create(null);
          if (r.r(n), Object.defineProperty(n, "default", { enumerable: true, value: e2 }), 2 & t2 && "string" != typeof e2) for (var o in e2) r.d(n, o, (function(t3) {
            return e2[t3];
          }).bind(null, o));
          return n;
        }, r.n = function(e2) {
          var t2 = e2 && e2.__esModule ? function() {
            return e2.default;
          } : function() {
            return e2;
          };
          return r.d(t2, "a", t2), t2;
        }, r.o = function(e2, t2) {
          return Object.prototype.hasOwnProperty.call(e2, t2);
        }, r.p = "/dist/", r(r.s = 14);
      }([function(e, t) {
        function r(t2) {
          return e.exports = r = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e2) {
            return typeof e2;
          } : function(e2) {
            return e2 && "function" == typeof Symbol && e2.constructor === Symbol && e2 !== Symbol.prototype ? "symbol" : typeof e2;
          }, e.exports.__esModule = true, e.exports.default = e.exports, r(t2);
        }
        e.exports = r, e.exports.__esModule = true, e.exports.default = e.exports;
      }, function(e, t, r) {
        "use strict";
        (function(t2) {
          var n = r(0);
          function o(e2, t3) {
            var r2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
            if (!r2) {
              if (Array.isArray(e2) || (r2 = function(e3, t4) {
                if (e3) {
                  if ("string" == typeof e3) return a(e3, t4);
                  var r3 = {}.toString.call(e3).slice(8, -1);
                  return "Object" === r3 && e3.constructor && (r3 = e3.constructor.name), "Map" === r3 || "Set" === r3 ? Array.from(e3) : "Arguments" === r3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r3) ? a(e3, t4) : void 0;
                }
              }(e2)) || t3 && e2 && "number" == typeof e2.length) {
                r2 && (e2 = r2);
                var n2 = 0, o2 = function() {
                };
                return { s: o2, n: function() {
                  return n2 >= e2.length ? { done: true } : { done: false, value: e2[n2++] };
                }, e: function(e3) {
                  throw e3;
                }, f: o2 };
              }
              throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
            }
            var i2, s2 = true, c2 = false;
            return { s: function() {
              r2 = r2.call(e2);
            }, n: function() {
              var e3 = r2.next();
              return s2 = e3.done, e3;
            }, e: function(e3) {
              c2 = true, i2 = e3;
            }, f: function() {
              try {
                s2 || null == r2.return || r2.return();
              } finally {
                if (c2) throw i2;
              }
            } };
          }
          function a(e2, t3) {
            (null == t3 || t3 > e2.length) && (t3 = e2.length);
            for (var r2 = 0, n2 = Array(t3); r2 < t3; r2++) n2[r2] = e2[r2];
            return n2;
          }
          var i = r(17), s = r(19), c = r(20), u = r(25), l = r(26), d = r(7);
          function p(e2) {
            return encodeURIComponent(e2).replace(/!/g, "%21").replace(/'/g, "%27").replace(/\(/g, "%28").replace(/\)/g, "%29").replace(/\*/g, "%2A");
          }
          function f(e2, t3) {
            var r2 = [];
            for (var n2 in e2) e2.hasOwnProperty(n2) && r2.push(t3 ? p(n2).toLowerCase() : n2);
            return r2.sort(function(e3, t4) {
              return (e3 = e3.toLowerCase()) === (t4 = t4.toLowerCase()) ? 0 : e3 > t4 ? 1 : -1;
            });
          }
          var h, m = ["cache-control", "content-disposition", "content-encoding", "content-length", "content-md5", "expect", "expires", "host", "if-match", "if-modified-since", "if-none-match", "if-unmodified-since", "origin", "range", "transfer-encoding", "pic-operations"], g = function(e2, t3, r2) {
            var n2 = t3 / 8, o2 = e2.slice(r2, r2 + n2);
            return new Uint8Array(o2).reverse(), new { 8: Uint8Array, 16: Uint16Array, 32: Uint32Array }[t3](o2)[0];
          }, y = function(e2, t3, r2, n2) {
            var o2 = e2.slice(t3, r2), a2 = "";
            return new Uint8Array(o2).forEach(function(e3) {
              a2 += String.fromCharCode(e3);
            }), n2 && (a2 = decodeURIComponent(escape(a2))), a2;
          }, v = function() {
          }, k = function(e2) {
            var t3 = {};
            for (var r2 in e2) e2.hasOwnProperty(r2) && void 0 !== e2[r2] && null !== e2[r2] && (t3[r2] = e2[r2]);
            return t3;
          }, b = function(e2, t3) {
            var r2, n2 = new FileReader();
            FileReader.prototype.readAsBinaryString ? (r2 = FileReader.prototype.readAsBinaryString, n2.onload = function() {
              t3(this.result);
            }) : FileReader.prototype.readAsArrayBuffer && (r2 = function(e3) {
              var r3 = "", n3 = new FileReader();
              n3.onload = function(e4) {
                for (var o2 = new Uint8Array(n3.result), a2 = o2.byteLength, i2 = 0; i2 < a2; i2++) r3 += String.fromCharCode(o2[i2]);
                t3(r3);
              }, n3.readAsArrayBuffer(e3);
            }), r2.call(n2, e2);
          }, C = (h = function(e2, t3) {
            e2 = e2.split("."), t3 = t3.split(".");
            for (var r2 = 0; r2 < t3.length; r2++) if (e2[r2] !== t3[r2]) return parseInt(e2[r2]) > parseInt(t3[r2]) ? 1 : -1;
            return 0;
          }, function(e2) {
            if (!e2) return false;
            var t3 = (e2.match(/Chrome\/([.\d]+)/) || [])[1], r2 = (e2.match(/QBCore\/([.\d]+)/) || [])[1], n2 = (e2.match(/QQBrowser\/([.\d]+)/) || [])[1];
            return t3 && h(t3, "53.0.2785.116") < 0 && r2 && h(r2, "3.53.991.400") < 0 && n2 && h(n2, "9.0.2524.400") <= 0 || false;
          }("undefined" != typeof navigator && navigator.userAgent)), S = 1048576;
          function T(e2) {
            return A(e2, function(e3) {
              return "object" === n(e3) && null !== e3 ? T(e3) : e3;
            });
          }
          function w(e2, t3) {
            return x(t3, function(r2, n2) {
              e2[n2] = t3[n2];
            }), e2;
          }
          function R(e2) {
            return e2 instanceof Array;
          }
          function x(e2, t3) {
            for (var r2 in e2) e2.hasOwnProperty(r2) && t3(e2[r2], r2);
          }
          function A(e2, t3) {
            var r2 = R(e2) ? [] : {};
            for (var n2 in e2) e2.hasOwnProperty(n2) && (r2[n2] = t3(e2[n2], n2));
            return r2;
          }
          var E = function(e2, t3) {
            var r2 = t3.Bucket, n2 = t3.Region, o2 = t3.Key, a2 = this.options.Domain, i2 = !a2 || "string" == typeof a2 && a2.indexOf("{Bucket}") > -1, s2 = !a2 || "string" == typeof a2 && a2.indexOf("{Region}") > -1;
            if (e2.indexOf("Bucket") > -1 || "deleteMultipleObject" === e2 || "multipartList" === e2 || "listObjectVersions" === e2) {
              if (i2 && !r2) return "Bucket";
              if (s2 && !n2) return "Region";
            } else if (e2.indexOf("Object") > -1 || e2.indexOf("multipart") > -1 || "sliceUploadFile" === e2 || "abortUploadTask" === e2 || "uploadFile" === e2) {
              if (i2 && !r2) return "Bucket";
              if (s2 && !n2) return "Region";
              if (!o2) return "Key";
            }
            return false;
          }, B = function(e2, t3) {
            if (t3 = w({}, t3), "getAuth" !== e2 && "getV4Auth" !== e2 && "getObjectUrl" !== e2) {
              var r2 = t3.Headers || {};
              if (t3 && "object" === n(t3)) {
                !function() {
                  for (var e3 in t3) t3.hasOwnProperty(e3) && e3.indexOf("x-cos-") > -1 && (r2[e3] = t3[e3]);
                }();
                O.each({ "x-cos-mfa": "MFA", "Content-MD5": "ContentMD5", "Content-Length": "ContentLength", "Content-Type": "ContentType", Expect: "Expect", Expires: "Expires", "Cache-Control": "CacheControl", "Content-Disposition": "ContentDisposition", "Content-Encoding": "ContentEncoding", Range: "Range", "If-Modified-Since": "IfModifiedSince", "If-Unmodified-Since": "IfUnmodifiedSince", "If-Match": "IfMatch", "If-None-Match": "IfNoneMatch", "x-cos-copy-source": "CopySource", "x-cos-copy-source-Range": "CopySourceRange", "x-cos-metadata-directive": "MetadataDirective", "x-cos-copy-source-If-Modified-Since": "CopySourceIfModifiedSince", "x-cos-copy-source-If-Unmodified-Since": "CopySourceIfUnmodifiedSince", "x-cos-copy-source-If-Match": "CopySourceIfMatch", "x-cos-copy-source-If-None-Match": "CopySourceIfNoneMatch", "x-cos-acl": "ACL", "x-cos-grant-read": "GrantRead", "x-cos-grant-write": "GrantWrite", "x-cos-grant-full-control": "GrantFullControl", "x-cos-grant-read-acp": "GrantReadAcp", "x-cos-grant-write-acp": "GrantWriteAcp", "x-cos-storage-class": "StorageClass", "x-cos-traffic-limit": "TrafficLimit", "x-cos-mime-limit": "MimeLimit", "x-cos-server-side-encryption-customer-algorithm": "SSECustomerAlgorithm", "x-cos-server-side-encryption-customer-key": "SSECustomerKey", "x-cos-server-side-encryption-customer-key-MD5": "SSECustomerKeyMD5", "x-cos-server-side-encryption": "ServerSideEncryption", "x-cos-server-side-encryption-cos-kms-key-id": "SSEKMSKeyId", "x-cos-server-side-encryption-context": "SSEContext", "Pic-Operations": "PicOperations", "x-cos-callback": "Callback", "x-cos-callback-var": "CallbackVar", "x-cos-return-body": "ReturnBody" }, function(e3, n2) {
                  void 0 !== t3[e3] && (r2[n2] = t3[e3]);
                }), t3.Headers = k(r2);
              }
            }
            return t3;
          }, D = function(e2) {
            return Date.now() + (e2 || 0);
          }, N = "object" === ("undefined" == typeof navigator ? "undefined" : n(navigator)) && !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), P = "object" === ("undefined" == typeof navigator ? "undefined" : n(navigator)) && /\sQQ/i.test(navigator.userAgent), O = { noop: v, formatParams: B, apiWrapper: function(e2, t3) {
            return function(r2, n2) {
              var o2, a2 = this;
              if ("function" == typeof r2 && (n2 = r2, r2 = {}), r2 = B(e2, r2), a2.options.EnableReporter) if ("sliceUploadFile" === r2.calledBySdk || "sliceCopyFile" === r2.calledBySdk) o2 = r2.tracker && r2.tracker.generateSubTracker({ apiName: e2 });
              else if (["uploadFile", "uploadFiles"].includes(e2)) o2 = null;
              else {
                var i2 = 0;
                r2.Body && (i2 = "string" == typeof r2.Body ? r2.Body.length : r2.Body.size || r2.Body.byteLength || 0);
                var s2 = a2.options.UseAccelerate || "string" == typeof a2.options.Domain && a2.options.Domain.includes("accelerate.");
                o2 = new d({ Beacon: a2.options.BeaconReporter, clsReporter: a2.options.ClsReporter, bucket: r2.Bucket, region: r2.Region, apiName: e2, realApi: e2, accelerate: s2, fileKey: r2.Key, fileSize: i2, deepTracker: a2.options.DeepTracker, customId: a2.options.CustomId, delay: a2.options.TrackerDelay });
              }
              r2.tracker = o2;
              var c2 = function(e3) {
                return e3 && e3.headers && (e3.headers["x-cos-request-id"] && (e3.RequestId = e3.headers["x-cos-request-id"]), e3.headers["x-ci-request-id"] && (e3.RequestId = e3.headers["x-ci-request-id"]), e3.headers["x-cos-version-id"] && (e3.VersionId = e3.headers["x-cos-version-id"]), e3.headers["x-cos-delete-marker"] && (e3.DeleteMarker = e3.headers["x-cos-delete-marker"])), e3;
              }, u2 = function(e3, t4) {
                o2 && o2.report(e3, t4), n2 && n2(c2(e3), c2(t4));
              }, l2 = function() {
                if ("getService" !== e2 && "abortUploadTask" !== e2) {
                  var t4 = E.call(a2, e2, r2);
                  if (t4) return "missing param " + t4;
                  if (r2.Region) {
                    if (a2.options.CompatibilityMode) {
                      if (!/^([a-z\d-.]+)$/.test(r2.Region)) return "Region format error.";
                    } else {
                      if (r2.Region.indexOf("cos.") > -1) return 'param Region should not be start with "cos."';
                      if (!/^([a-z\d-]+)$/.test(r2.Region)) return "Region format error.";
                    }
                    !a2.options.CompatibilityMode && -1 === r2.Region.indexOf("-") && "yfb" !== r2.Region && "default" !== r2.Region && r2.Region;
                  }
                  if (r2.Bucket) {
                    if (!/^([a-z\d-]+)-(\d+)$/.test(r2.Bucket)) if (r2.AppId) r2.Bucket = r2.Bucket + "-" + r2.AppId;
                    else {
                      if (!a2.options.AppId) return 'Bucket should format as "test-1250000000".';
                      r2.Bucket = r2.Bucket + "-" + a2.options.AppId;
                    }
                    r2.AppId && delete r2.AppId;
                  }
                  !a2.options.UseRawKey && r2.Key && "/" === r2.Key.substr(0, 1) && (r2.Key = r2.Key.substr(1));
                }
              }(), p2 = ["getAuth", "getObjectUrl"].includes(e2);
              if ("function" == typeof Promise && !p2 && !n2) return new Promise(function(e3, o3) {
                if (n2 = function(t4, r3) {
                  t4 ? o3(t4) : e3(r3);
                }, l2) return u2(O.error(new Error(l2)));
                t3.call(a2, r2, u2);
              });
              if (l2) return u2(O.error(new Error(l2)));
              var f2 = t3.call(a2, r2, u2);
              return p2 ? f2 : void 0;
            };
          }, xml2json: c, json2xml: u, md5: i, clearKey: k, fileSlice: function(e2, t3, r2, n2, o2) {
            var a2;
            if (e2.slice ? a2 = e2.slice(t3, r2) : e2.mozSlice ? a2 = e2.mozSlice(t3, r2) : e2.webkitSlice && (a2 = e2.webkitSlice(t3, r2)), n2 && C) {
              var i2 = new FileReader();
              i2.onload = function(e3) {
                a2 = null, o2(new Blob([i2.result]));
              }, i2.readAsArrayBuffer(a2);
            } else o2(a2);
          }, getBodyMd5: function(e2, t3, r2, n2) {
            r2 = r2 || v, e2 ? "string" == typeof t3 ? r2(O.md5(t3, true)) : Blob && t3 instanceof Blob ? O.getFileMd5(t3, function(e3, t4) {
              r2(t4);
            }, n2) : r2() : r2();
          }, getFileMd5: function(e2, t3, r2) {
            var n2 = e2.size, o2 = 0, a2 = i.getCtx();
            !function i2(s2) {
              if (s2 >= n2) {
                var c2 = a2.digest("hex");
                t3(null, c2);
              } else {
                var u2 = Math.min(n2, s2 + S);
                O.fileSlice(e2, s2, u2, false, function(e3) {
                  b(e3, function(t4) {
                    e3 = null, a2 = a2.update(t4, true), o2 += t4.length, t4 = null, r2 && r2({ loaded: o2, total: n2, percent: Math.round(o2 / n2 * 1e4) / 1e4 }), i2(s2 + S);
                  });
                });
              }
            }(0);
          }, b64: function(e2) {
            var t3, r2, n2, o2 = "";
            for (t3 = 0, r2 = e2.length / 2; t3 < r2; t3++) n2 = parseInt(e2[2 * t3] + e2[2 * t3 + 1], 16), o2 += String.fromCharCode(n2);
            return btoa(o2);
          }, extend: w, isArray: R, isInArray: function(e2, t3) {
            for (var r2 = false, n2 = 0; n2 < e2.length; n2++) if (t3 === e2[n2]) {
              r2 = true;
              break;
            }
            return r2;
          }, makeArray: function(e2) {
            return R(e2) ? e2 : [e2];
          }, each: x, map: A, filter: function(e2, t3) {
            var r2 = R(e2), n2 = r2 ? [] : {};
            for (var o2 in e2) e2.hasOwnProperty(o2) && t3(e2[o2], o2) && (r2 ? n2.push(e2[o2]) : n2[o2] = e2[o2]);
            return n2;
          }, clone: T, attr: function(e2, t3, r2) {
            return e2 && t3 in e2 ? e2[t3] : r2;
          }, uuid: function() {
            var e2 = function() {
              return (65536 * (1 + Math.random()) | 0).toString(16).substring(1);
            };
            return e2() + e2() + "-" + e2() + "-" + e2() + "-" + e2() + "-" + e2() + e2() + e2();
          }, camSafeUrlEncode: p, throttleOnProgress: function(e2, t3) {
            var r2, n2, o2 = this, a2 = 0, i2 = 0, s2 = Date.now();
            function c2() {
              if (n2 = 0, t3 && "function" == typeof t3) {
                r2 = Date.now();
                var o3, c3 = Math.max(0, Math.round((i2 - a2) / ((r2 - s2) / 1e3) * 100) / 100) || 0;
                o3 = 0 === i2 && 0 === e2 ? 1 : Math.floor(i2 / e2 * 100) / 100 || 0, s2 = r2, a2 = i2;
                try {
                  t3({ loaded: i2, total: e2, speed: c3, percent: o3 });
                } catch (e3) {
                }
              }
            }
            return function(t4, r3) {
              if (t4 && (i2 = t4.loaded, e2 = t4.total), r3) clearTimeout(n2), c2();
              else {
                if (n2) return;
                n2 = setTimeout(c2, o2.options.ProgressInterval);
              }
            };
          }, getFileSize: function(e2, t3, r2) {
            var n2;
            "string" == typeof t3.Body ? t3.Body = new Blob([t3.Body], { type: "text/plain" }) : t3.Body instanceof ArrayBuffer && (t3.Body = new Blob([t3.Body])), t3.Body && (t3.Body instanceof Blob || "[object File]" === t3.Body.toString() || "[object Blob]" === t3.Body.toString()) ? (n2 = t3.Body.size, t3.ContentLength = n2, r2(null, n2)) : r2(O.error(new Error("params body format error, Only allow File|Blob|String.")));
          }, getSkewTime: D, error: function(e2, t3) {
            var r2 = e2;
            return e2.message = e2.message || null, "string" == typeof t3 ? (e2.error = t3, e2.message = t3) : "object" === n(t3) && null !== t3 && (w(e2, t3), (t3.code || t3.name) && (e2.code = t3.code || t3.name), t3.message && (e2.message = t3.message), t3.stack && (e2.stack = t3.stack)), "function" == typeof Object.defineProperty && (Object.defineProperty(e2, "name", { writable: true, enumerable: false }), Object.defineProperty(e2, "message", { enumerable: true })), e2.name = t3 && t3.name || e2.name || e2.code || "Error", e2.code || (e2.code = e2.name), e2.error || (e2.error = T(r2)), e2;
          }, obj2str: function(e2, t3) {
            var r2, n2, o2, a2 = [], i2 = f(e2);
            for (r2 = 0; r2 < i2.length; r2++) o2 = void 0 === e2[n2 = i2[r2]] || null === e2[n2] ? "" : "" + e2[n2], n2 = t3 ? p(n2).toLowerCase() : p(n2), o2 = p(o2) || "", a2.push(n2 + "=" + o2);
            return a2.join("&");
          }, getAuth: function(e2) {
            var t3, r2 = (e2 = e2 || {}).SecretId, n2 = e2.SecretKey, o2 = e2.KeyTime, a2 = (e2.method || e2.Method || "get").toLowerCase(), i2 = T(e2.Query || e2.params || {}), c2 = function(e3) {
              var t4 = {};
              for (var r3 in e3) {
                var n3 = r3.toLowerCase();
                (n3.indexOf("x-cos-") > -1 || m.indexOf(n3) > -1) && (t4[r3] = e3[r3]);
              }
              return t4;
            }(T(e2.Headers || e2.headers || {})), u2 = e2.Key || "";
            e2.UseRawKey ? t3 = e2.Pathname || e2.pathname || "/" + u2 : 0 !== (t3 = e2.Pathname || e2.pathname || u2).indexOf("/") && (t3 = "/" + t3);
            var l2 = false !== e2.ForceSignHost;
            if (!c2.Host && !c2.host && e2.Bucket && e2.Region && l2 && (c2.Host = e2.Bucket + ".cos." + e2.Region + ".myqcloud.com"), !r2) throw new Error("missing param SecretId");
            if (!n2) throw new Error("missing param SecretKey");
            var d2 = Math.round(D(e2.SystemClockOffset) / 1e3) - 1, p2 = d2, h2 = e2.Expires || e2.expires;
            p2 += void 0 === h2 ? 900 : 1 * h2 || 0;
            var g2 = r2, y2 = o2 || d2 + ";" + p2, v2 = o2 || d2 + ";" + p2, k2 = f(c2, true).join(";").toLowerCase(), b2 = f(i2, true).join(";").toLowerCase(), C2 = s.HmacSHA1(v2, n2).toString(), S2 = [a2, t3, O.obj2str(i2, true), O.obj2str(c2, true), ""].join("\n"), w2 = ["sha1", y2, s.SHA1(S2).toString(), ""].join("\n");
            return ["q-sign-algorithm=sha1", "q-ak=" + g2, "q-sign-time=" + y2, "q-key-time=" + v2, "q-header-list=" + k2, "q-url-param-list=" + b2, "q-signature=" + s.HmacSHA1(w2, C2).toString()].join("&");
          }, parseSelectPayload: function(e2) {
            for (var t3 = {}, r2 = y(e2), n2 = { records: [] }; e2.byteLength; ) {
              var o2, a2 = g(e2, 32, 0), i2 = g(e2, 32, 4), s2 = a2 - i2 - 16, c2 = 0;
              for (e2 = e2.slice(12); c2 < i2; ) {
                var u2 = g(e2, 8, c2), l2 = y(e2, c2 + 1, c2 + 1 + u2), d2 = g(e2, 16, c2 + u2 + 2), p2 = y(e2, c2 + u2 + 4, c2 + u2 + 4 + d2);
                t3[l2] = p2, c2 += u2 + 4 + d2;
              }
              if ("Records" === t3[":event-type"]) o2 = y(e2, c2, c2 + s2, true), n2.records.push(o2);
              else if ("Stats" === t3[":event-type"]) o2 = y(e2, c2, c2 + s2, true), n2.stats = O.xml2json(o2).Stats;
              else if ("error" === t3[":event-type"]) {
                var f2 = t3[":error-code"], h2 = t3[":error-message"], m2 = new Error(h2);
                m2.message = h2, m2.name = m2.code = f2, n2.error = m2;
              } else ["Progress", "Continuation", "End"].includes(t3[":event-type"]);
              e2 = e2.slice(c2 + s2 + 4);
            }
            return { payload: n2.records.join(""), body: r2 };
          }, getSourceParams: function(e2) {
            var t3 = this.options.CopySourceParser;
            if (t3) return t3(e2);
            var r2 = e2.match(/^([^.]+-\d+)\.cos(v6|-cdc|-cdz|-internal)?\.([^.]+)\.((myqcloud\.com)|(tencentcos\.cn))\/(.+)$/);
            return r2 ? { Bucket: r2[1], Region: r2[3], Key: r2[7] } : null;
          }, isBrowser: true, isNode: function() {
            return "object" !== ("undefined" == typeof window ? "undefined" : n(window)) && "object" === (void 0 === t2 ? "undefined" : n(t2)) && !("object" === ("undefined" == typeof globalThis ? "undefined" : n(globalThis)) && ("DedicatedWorkerGlobalScope" === globalThis.constructor.name || globalThis.FileReaderSync));
          }, isCIHost: function(e2) {
            return /^https?:\/\/([^/]+\.)?ci\.[^/]+/.test(e2);
          }, isIOS_QQ: N && P, encodeBase64: function(e2, t3) {
            var r2 = l.encode(e2);
            return t3 && (r2 = r2.replaceAll("+", "-").replaceAll("/", "_").replaceAll("=", "")), r2;
          }, decodeBase64: function(e2) {
            return e2 ? l.decode(e2) : "";
          }, simplifyPath: function(e2) {
            var t3, r2 = [], n2 = o(e2.split("/"));
            try {
              for (n2.s(); !(t3 = n2.n()).done; ) {
                var a2 = t3.value;
                ".." === a2 ? r2.length && r2.pop() : a2.length && "." !== a2 && r2.push(a2);
              }
            } catch (e3) {
              n2.e(e3);
            } finally {
              n2.f();
            }
            return "/" + r2.join("/");
          }, readAsBinaryString: b, parseResBody: function(e2) {
            var t3;
            if (e2 && "string" == typeof e2) {
              var r2 = e2.trim(), n2 = 0 === r2.indexOf("<"), o2 = 0 === r2.indexOf("{");
              if (n2) t3 = O.xml2json(e2) || {};
              else if (o2) try {
                var a2 = e2.replace(/\n/g, " "), i2 = JSON.parse(a2);
                t3 = "[object Object]" === Object.prototype.toString.call(i2) ? i2 : e2;
              } catch (r3) {
                t3 = e2;
              }
              else t3 = e2;
            } else t3 = e2 || {};
            return t3;
          } };
          e.exports = O;
        }).call(this, r(16));
      }, function(e, t, r) {
        "use strict";
        function n(e2, t2) {
          return void 0 === t2 && (t2 = Object), t2 && "function" == typeof t2.freeze ? t2.freeze(e2) : e2;
        }
        var o = n({ HTML: "text/html", isHTML: function(e2) {
          return e2 === o.HTML;
        }, XML_APPLICATION: "application/xml", XML_TEXT: "text/xml", XML_XHTML_APPLICATION: "application/xhtml+xml", XML_SVG_IMAGE: "image/svg+xml" }), a = n({ HTML: "http://www.w3.org/1999/xhtml", isHTML: function(e2) {
          return e2 === a.HTML;
        }, SVG: "http://www.w3.org/2000/svg", XML: "http://www.w3.org/XML/1998/namespace", XMLNS: "http://www.w3.org/2000/xmlns/" });
        t.assign = function(e2, t2) {
          if (null === e2 || "object" != typeof e2) throw new TypeError("target is not an object");
          for (var r2 in t2) Object.prototype.hasOwnProperty.call(t2, r2) && (e2[r2] = t2[r2]);
          return e2;
        }, t.find = function(e2, t2, r2) {
          if (void 0 === r2 && (r2 = Array.prototype), e2 && "function" == typeof r2.find) return r2.find.call(e2, t2);
          for (var n2 = 0; n2 < e2.length; n2++) if (Object.prototype.hasOwnProperty.call(e2, n2)) {
            var o2 = e2[n2];
            if (t2.call(void 0, o2, n2, e2)) return o2;
          }
        }, t.freeze = n, t.MIME_TYPE = o, t.NAMESPACE = a;
      }, function(e) {
        e.exports = JSON.parse('{"name":"cos-js-sdk-v5","version":"1.8.3","description":"JavaScript SDK for [腾讯云对象存储](https://cloud.tencent.com/product/cos)","main":"dist/cos-js-sdk-v5.js","types":"index.d.ts","scripts":{"prettier":"prettier --write src demo/demo.js demo/CIDemos/*.js test/test.js server/sts.js lib/request.js index.d.ts","server":"node server/sts.js","dev":"cross-env NODE_ENV=development webpack -w --mode=development","build":"cross-env NODE_ENV=production webpack --mode=production","cos-auth.min.js":"uglifyjs ./demo/common/cos-auth.js -o ./demo/common/cos-auth.min.js -c -m","test":"jest --runInBand --coverage"},"repository":{"type":"git","url":"git+https://github.com/tencentyun/cos-js-sdk-v5.git"},"keywords":[],"author":"carsonxu","license":"ISC","bugs":{"url":"https://github.com/tencentyun/cos-js-sdk-v5/issues"},"homepage":"https://github.com/tencentyun/cos-js-sdk-v5#readme","dependencies":{"@xmldom/xmldom":"^0.8.6"},"devDependencies":{"@babel/core":"7.17.9","@babel/plugin-transform-runtime":"7.18.10","@babel/preset-env":"7.16.11","babel-loader":"8.2.5","body-parser":"^1.18.3","cross-env":"^5.2.0","express":"^4.16.4","jest":"^29.3.1","jest-environment-jsdom":"^29.3.1","prettier":"^3.0.1","qcloud-cos-sts":"^3.0.2","request":"^2.87.0","terser-webpack-plugin":"4.2.3","uglifyjs":"^2.4.11","webpack":"4.46.0","webpack-cli":"4.10.0"}}');
      }, function(e, t) {
        var r = function(e2) {
          var t2 = {}, r2 = function(e3) {
            return !t2[e3] && (t2[e3] = []), t2[e3];
          };
          e2.on = function(e3, t3) {
            r2(e3).push(t3);
          }, e2.off = function(e3, t3) {
            for (var n = r2(e3), o = n.length - 1; o >= 0; o--) t3 === n[o] && n.splice(o, 1);
          }, e2.emit = function(e3, t3) {
            for (var n = r2(e3).map(function(e4) {
              return e4;
            }), o = 0; o < n.length; o++) n[o](t3);
          };
        };
        e.exports.init = r, e.exports.EventProxy = function() {
          r(this);
        };
      }, function(e, t) {
        e.exports = function(e2) {
          return e2.webpackPolyfill || (e2.deprecate = function() {
          }, e2.paths = [], e2.children || (e2.children = []), Object.defineProperty(e2, "loaded", { enumerable: true, get: function() {
            return e2.l;
          } }), Object.defineProperty(e2, "id", { enumerable: true, get: function() {
            return e2.i;
          } }), e2.webpackPolyfill = 1), e2;
        };
      }, function(e, t, r) {
        var n = r(2), o = n.find, a = n.NAMESPACE;
        function i(e2) {
          return "" !== e2;
        }
        function s(e2, t2) {
          return e2.hasOwnProperty(t2) || (e2[t2] = true), e2;
        }
        function c(e2) {
          if (!e2) return [];
          var t2 = function(e3) {
            return e3 ? e3.split(/[\t\n\f\r ]+/).filter(i) : [];
          }(e2);
          return Object.keys(t2.reduce(s, {}));
        }
        function u(e2, t2) {
          for (var r2 in e2) Object.prototype.hasOwnProperty.call(e2, r2) && (t2[r2] = e2[r2]);
        }
        function l(e2, t2) {
          var r2 = e2.prototype;
          if (!(r2 instanceof t2)) {
            let n3 = function() {
            };
            var n2 = n3;
            n3.prototype = t2.prototype, u(r2, n3 = new n3()), e2.prototype = r2 = n3;
          }
          r2.constructor != e2 && (r2.constructor = e2);
        }
        var d = {}, p = d.ELEMENT_NODE = 1, f = d.ATTRIBUTE_NODE = 2, h = d.TEXT_NODE = 3, m = d.CDATA_SECTION_NODE = 4, g = d.ENTITY_REFERENCE_NODE = 5, y = d.ENTITY_NODE = 6, v = d.PROCESSING_INSTRUCTION_NODE = 7, k = d.COMMENT_NODE = 8, b = d.DOCUMENT_NODE = 9, C = d.DOCUMENT_TYPE_NODE = 10, S = d.DOCUMENT_FRAGMENT_NODE = 11, T = d.NOTATION_NODE = 12, w = {}, R = {}, x = (w.INDEX_SIZE_ERR = (R[1] = "Index size error", 1), w.DOMSTRING_SIZE_ERR = (R[2] = "DOMString size error", 2), w.HIERARCHY_REQUEST_ERR = (R[3] = "Hierarchy request error", 3)), A = (w.WRONG_DOCUMENT_ERR = (R[4] = "Wrong document", 4), w.INVALID_CHARACTER_ERR = (R[5] = "Invalid character", 5), w.NO_DATA_ALLOWED_ERR = (R[6] = "No data allowed", 6), w.NO_MODIFICATION_ALLOWED_ERR = (R[7] = "No modification allowed", 7), w.NOT_FOUND_ERR = (R[8] = "Not found", 8)), E = (w.NOT_SUPPORTED_ERR = (R[9] = "Not supported", 9), w.INUSE_ATTRIBUTE_ERR = (R[10] = "Attribute in use", 10));
        w.INVALID_STATE_ERR = (R[11] = "Invalid state", 11), w.SYNTAX_ERR = (R[12] = "Syntax error", 12), w.INVALID_MODIFICATION_ERR = (R[13] = "Invalid modification", 13), w.NAMESPACE_ERR = (R[14] = "Invalid namespace", 14), w.INVALID_ACCESS_ERR = (R[15] = "Invalid access", 15);
        function B(e2, t2) {
          if (t2 instanceof Error) var r2 = t2;
          else r2 = this, Error.call(this, R[e2]), this.message = R[e2], Error.captureStackTrace && Error.captureStackTrace(this, B);
          return r2.code = e2, t2 && (this.message = this.message + ": " + t2), r2;
        }
        function D() {
        }
        function N(e2, t2) {
          this._node = e2, this._refresh = t2, P(this);
        }
        function P(e2) {
          var t2 = e2._node._inc || e2._node.ownerDocument._inc;
          if (e2._inc !== t2) {
            var r2 = e2._refresh(e2._node);
            if (ke(e2, "length", r2.length), !e2.$$length || r2.length < e2.$$length) for (var n2 = r2.length; n2 in e2; n2++) Object.prototype.hasOwnProperty.call(e2, n2) && delete e2[n2];
            u(r2, e2), e2._inc = t2;
          }
        }
        function O() {
        }
        function I(e2, t2) {
          for (var r2 = e2.length; r2--; ) if (e2[r2] === t2) return r2;
        }
        function L(e2, t2, r2, n2) {
          if (n2 ? t2[I(t2, n2)] = r2 : t2[t2.length++] = r2, e2) {
            r2.ownerElement = e2;
            var o2 = e2.ownerDocument;
            o2 && (n2 && F(o2, e2, n2), function(e3, t3, r3) {
              e3 && e3._inc++;
              var n3 = r3.namespaceURI;
              n3 === a.XMLNS && (t3._nsMap[r3.prefix ? r3.localName : ""] = r3.value);
            }(o2, e2, r2));
          }
        }
        function U(e2, t2, r2) {
          var n2 = I(t2, r2);
          if (!(n2 >= 0)) throw new B(A, new Error(e2.tagName + "@" + r2));
          for (var o2 = t2.length - 1; n2 < o2; ) t2[n2] = t2[++n2];
          if (t2.length = o2, e2) {
            var a2 = e2.ownerDocument;
            a2 && (F(a2, e2, r2), r2.ownerElement = null);
          }
        }
        function _() {
        }
        function M() {
        }
        function q(e2) {
          return ("<" == e2 ? "&lt;" : ">" == e2 && "&gt;") || "&" == e2 && "&amp;" || '"' == e2 && "&quot;" || "&#" + e2.charCodeAt() + ";";
        }
        function j(e2, t2) {
          if (t2(e2)) return true;
          if (e2 = e2.firstChild) do {
            if (j(e2, t2)) return true;
          } while (e2 = e2.nextSibling);
        }
        function H() {
          this.ownerDocument = this;
        }
        function F(e2, t2, r2, n2) {
          e2 && e2._inc++, r2.namespaceURI === a.XMLNS && delete t2._nsMap[r2.prefix ? r2.localName : ""];
        }
        function K(e2, t2, r2) {
          if (e2 && e2._inc) {
            e2._inc++;
            var n2 = t2.childNodes;
            if (r2) n2[n2.length++] = r2;
            else {
              for (var o2 = t2.firstChild, a2 = 0; o2; ) n2[a2++] = o2, o2 = o2.nextSibling;
              n2.length = a2, delete n2[n2.length];
            }
          }
        }
        function z(e2, t2) {
          var r2 = t2.previousSibling, n2 = t2.nextSibling;
          return r2 ? r2.nextSibling = n2 : e2.firstChild = n2, n2 ? n2.previousSibling = r2 : e2.lastChild = r2, t2.parentNode = null, t2.previousSibling = null, t2.nextSibling = null, K(e2.ownerDocument, e2), t2;
        }
        function V(e2) {
          return e2 && e2.nodeType === M.DOCUMENT_TYPE_NODE;
        }
        function G(e2) {
          return e2 && e2.nodeType === M.ELEMENT_NODE;
        }
        function W(e2) {
          return e2 && e2.nodeType === M.TEXT_NODE;
        }
        function Q(e2, t2) {
          var r2 = e2.childNodes || [];
          if (o(r2, G) || V(t2)) return false;
          var n2 = o(r2, V);
          return !(t2 && n2 && r2.indexOf(n2) > r2.indexOf(t2));
        }
        function X(e2, t2) {
          var r2 = e2.childNodes || [];
          if (o(r2, function(e3) {
            return G(e3) && e3 !== t2;
          })) return false;
          var n2 = o(r2, V);
          return !(t2 && n2 && r2.indexOf(n2) > r2.indexOf(t2));
        }
        function $(e2, t2, r2) {
          if (!function(e3) {
            return e3 && (e3.nodeType === M.DOCUMENT_NODE || e3.nodeType === M.DOCUMENT_FRAGMENT_NODE || e3.nodeType === M.ELEMENT_NODE);
          }(e2)) throw new B(x, "Unexpected parent node type " + e2.nodeType);
          if (r2 && r2.parentNode !== e2) throw new B(A, "child not in parent");
          if (!function(e3) {
            return e3 && (G(e3) || W(e3) || V(e3) || e3.nodeType === M.DOCUMENT_FRAGMENT_NODE || e3.nodeType === M.COMMENT_NODE || e3.nodeType === M.PROCESSING_INSTRUCTION_NODE);
          }(t2) || V(t2) && e2.nodeType !== M.DOCUMENT_NODE) throw new B(x, "Unexpected node type " + t2.nodeType + " for parent node type " + e2.nodeType);
        }
        function J(e2, t2, r2) {
          var n2 = e2.childNodes || [], a2 = t2.childNodes || [];
          if (t2.nodeType === M.DOCUMENT_FRAGMENT_NODE) {
            var i2 = a2.filter(G);
            if (i2.length > 1 || o(a2, W)) throw new B(x, "More than one element or text in fragment");
            if (1 === i2.length && !Q(e2, r2)) throw new B(x, "Element in fragment can not be inserted before doctype");
          }
          if (G(t2) && !Q(e2, r2)) throw new B(x, "Only one element can be added and only after doctype");
          if (V(t2)) {
            if (o(n2, V)) throw new B(x, "Only one doctype is allowed");
            var s2 = o(n2, G);
            if (r2 && n2.indexOf(s2) < n2.indexOf(r2)) throw new B(x, "Doctype can only be inserted before an element");
            if (!r2 && s2) throw new B(x, "Doctype can not be appended since element is present");
          }
        }
        function Y(e2, t2, r2) {
          var n2 = e2.childNodes || [], a2 = t2.childNodes || [];
          if (t2.nodeType === M.DOCUMENT_FRAGMENT_NODE) {
            var i2 = a2.filter(G);
            if (i2.length > 1 || o(a2, W)) throw new B(x, "More than one element or text in fragment");
            if (1 === i2.length && !X(e2, r2)) throw new B(x, "Element in fragment can not be inserted before doctype");
          }
          if (G(t2) && !X(e2, r2)) throw new B(x, "Only one element can be added and only after doctype");
          if (V(t2)) {
            if (o(n2, function(e3) {
              return V(e3) && e3 !== r2;
            })) throw new B(x, "Only one doctype is allowed");
            var s2 = o(n2, G);
            if (r2 && n2.indexOf(s2) < n2.indexOf(r2)) throw new B(x, "Doctype can only be inserted before an element");
          }
        }
        function Z(e2, t2, r2, n2) {
          $(e2, t2, r2), e2.nodeType === M.DOCUMENT_NODE && (n2 || J)(e2, t2, r2);
          var o2 = t2.parentNode;
          if (o2 && o2.removeChild(t2), t2.nodeType === S) {
            var a2 = t2.firstChild;
            if (null == a2) return t2;
            var i2 = t2.lastChild;
          } else a2 = i2 = t2;
          var s2 = r2 ? r2.previousSibling : e2.lastChild;
          a2.previousSibling = s2, i2.nextSibling = r2, s2 ? s2.nextSibling = a2 : e2.firstChild = a2, null == r2 ? e2.lastChild = i2 : r2.previousSibling = i2;
          do {
            a2.parentNode = e2;
          } while (a2 !== i2 && (a2 = a2.nextSibling));
          return K(e2.ownerDocument || e2, e2), t2.nodeType == S && (t2.firstChild = t2.lastChild = null), t2;
        }
        function ee() {
          this._nsMap = {};
        }
        function te() {
        }
        function re() {
        }
        function ne() {
        }
        function oe() {
        }
        function ae() {
        }
        function ie() {
        }
        function se() {
        }
        function ce() {
        }
        function ue() {
        }
        function le() {
        }
        function de() {
        }
        function pe() {
        }
        function fe(e2, t2) {
          var r2 = [], n2 = 9 == this.nodeType && this.documentElement || this, o2 = n2.prefix, a2 = n2.namespaceURI;
          if (a2 && null == o2 && null == (o2 = n2.lookupPrefix(a2))) var i2 = [{ namespace: a2, prefix: null }];
          return ge(this, r2, e2, t2, i2), r2.join("");
        }
        function he(e2, t2, r2) {
          var n2 = e2.prefix || "", o2 = e2.namespaceURI;
          if (!o2) return false;
          if ("xml" === n2 && o2 === a.XML || o2 === a.XMLNS) return false;
          for (var i2 = r2.length; i2--; ) {
            var s2 = r2[i2];
            if (s2.prefix === n2) return s2.namespace !== o2;
          }
          return true;
        }
        function me(e2, t2, r2) {
          e2.push(" ", t2, '="', r2.replace(/[<>&"\t\n\r]/g, q), '"');
        }
        function ge(e2, t2, r2, n2, o2) {
          if (o2 || (o2 = []), n2) {
            if (!(e2 = n2(e2))) return;
            if ("string" == typeof e2) return void t2.push(e2);
          }
          switch (e2.nodeType) {
            case p:
              var i2 = e2.attributes, s2 = i2.length, c2 = e2.firstChild, u2 = e2.tagName, l2 = u2;
              if (!(r2 = a.isHTML(e2.namespaceURI) || r2) && !e2.prefix && e2.namespaceURI) {
                for (var d2, y2 = 0; y2 < i2.length; y2++) if ("xmlns" === i2.item(y2).name) {
                  d2 = i2.item(y2).value;
                  break;
                }
                if (!d2) for (var T2 = o2.length - 1; T2 >= 0; T2--) {
                  if ("" === (w2 = o2[T2]).prefix && w2.namespace === e2.namespaceURI) {
                    d2 = w2.namespace;
                    break;
                  }
                }
                if (d2 !== e2.namespaceURI) for (T2 = o2.length - 1; T2 >= 0; T2--) {
                  var w2;
                  if ((w2 = o2[T2]).namespace === e2.namespaceURI) {
                    w2.prefix && (l2 = w2.prefix + ":" + u2);
                    break;
                  }
                }
              }
              t2.push("<", l2);
              for (var R2 = 0; R2 < s2; R2++) {
                "xmlns" == (x2 = i2.item(R2)).prefix ? o2.push({ prefix: x2.localName, namespace: x2.value }) : "xmlns" == x2.nodeName && o2.push({ prefix: "", namespace: x2.value });
              }
              for (R2 = 0; R2 < s2; R2++) {
                var x2, A2, E2;
                if (he(x2 = i2.item(R2), 0, o2)) me(t2, (A2 = x2.prefix || "") ? "xmlns:" + A2 : "xmlns", E2 = x2.namespaceURI), o2.push({ prefix: A2, namespace: E2 });
                ge(x2, t2, r2, n2, o2);
              }
              if (u2 === l2 && he(e2, 0, o2)) me(t2, (A2 = e2.prefix || "") ? "xmlns:" + A2 : "xmlns", E2 = e2.namespaceURI), o2.push({ prefix: A2, namespace: E2 });
              if (c2 || r2 && !/^(?:meta|link|img|br|hr|input)$/i.test(u2)) {
                if (t2.push(">"), r2 && /^script$/i.test(u2)) for (; c2; ) c2.data ? t2.push(c2.data) : ge(c2, t2, r2, n2, o2.slice()), c2 = c2.nextSibling;
                else for (; c2; ) ge(c2, t2, r2, n2, o2.slice()), c2 = c2.nextSibling;
                t2.push("</", l2, ">");
              } else t2.push("/>");
              return;
            case b:
            case S:
              for (c2 = e2.firstChild; c2; ) ge(c2, t2, r2, n2, o2.slice()), c2 = c2.nextSibling;
              return;
            case f:
              return me(t2, e2.name, e2.value);
            case h:
              return t2.push(e2.data.replace(/[<&>]/g, q));
            case m:
              return t2.push("<![CDATA[", e2.data, "]]>");
            case k:
              return t2.push("<!--", e2.data, "-->");
            case C:
              var B2 = e2.publicId, D2 = e2.systemId;
              if (t2.push("<!DOCTYPE ", e2.name), B2) t2.push(" PUBLIC ", B2), D2 && "." != D2 && t2.push(" ", D2), t2.push(">");
              else if (D2 && "." != D2) t2.push(" SYSTEM ", D2, ">");
              else {
                var N2 = e2.internalSubset;
                N2 && t2.push(" [", N2, "]"), t2.push(">");
              }
              return;
            case v:
              return t2.push("<?", e2.target, " ", e2.data, "?>");
            case g:
              return t2.push("&", e2.nodeName, ";");
            default:
              t2.push("??", e2.nodeName);
          }
        }
        function ye(e2, t2, r2) {
          var n2;
          switch (t2.nodeType) {
            case p:
              (n2 = t2.cloneNode(false)).ownerDocument = e2;
            case S:
              break;
            case f:
              r2 = true;
          }
          if (n2 || (n2 = t2.cloneNode(false)), n2.ownerDocument = e2, n2.parentNode = null, r2) for (var o2 = t2.firstChild; o2; ) n2.appendChild(ye(e2, o2, r2)), o2 = o2.nextSibling;
          return n2;
        }
        function ve(e2, t2, r2) {
          var n2 = new t2.constructor();
          for (var o2 in t2) if (Object.prototype.hasOwnProperty.call(t2, o2)) {
            var a2 = t2[o2];
            "object" != typeof a2 && a2 != n2[o2] && (n2[o2] = a2);
          }
          switch (t2.childNodes && (n2.childNodes = new D()), n2.ownerDocument = e2, n2.nodeType) {
            case p:
              var i2 = t2.attributes, s2 = n2.attributes = new O(), c2 = i2.length;
              s2._ownerElement = n2;
              for (var u2 = 0; u2 < c2; u2++) n2.setAttributeNode(ve(e2, i2.item(u2), true));
              break;
            case f:
              r2 = true;
          }
          if (r2) for (var l2 = t2.firstChild; l2; ) n2.appendChild(ve(e2, l2, r2)), l2 = l2.nextSibling;
          return n2;
        }
        function ke(e2, t2, r2) {
          e2[t2] = r2;
        }
        B.prototype = Error.prototype, u(w, B), D.prototype = { length: 0, item: function(e2) {
          return e2 >= 0 && e2 < this.length ? this[e2] : null;
        }, toString: function(e2, t2) {
          for (var r2 = [], n2 = 0; n2 < this.length; n2++) ge(this[n2], r2, e2, t2);
          return r2.join("");
        }, filter: function(e2) {
          return Array.prototype.filter.call(this, e2);
        }, indexOf: function(e2) {
          return Array.prototype.indexOf.call(this, e2);
        } }, N.prototype.item = function(e2) {
          return P(this), this[e2] || null;
        }, l(N, D), O.prototype = { length: 0, item: D.prototype.item, getNamedItem: function(e2) {
          for (var t2 = this.length; t2--; ) {
            var r2 = this[t2];
            if (r2.nodeName == e2) return r2;
          }
        }, setNamedItem: function(e2) {
          var t2 = e2.ownerElement;
          if (t2 && t2 != this._ownerElement) throw new B(E);
          var r2 = this.getNamedItem(e2.nodeName);
          return L(this._ownerElement, this, e2, r2), r2;
        }, setNamedItemNS: function(e2) {
          var t2, r2 = e2.ownerElement;
          if (r2 && r2 != this._ownerElement) throw new B(E);
          return t2 = this.getNamedItemNS(e2.namespaceURI, e2.localName), L(this._ownerElement, this, e2, t2), t2;
        }, removeNamedItem: function(e2) {
          var t2 = this.getNamedItem(e2);
          return U(this._ownerElement, this, t2), t2;
        }, removeNamedItemNS: function(e2, t2) {
          var r2 = this.getNamedItemNS(e2, t2);
          return U(this._ownerElement, this, r2), r2;
        }, getNamedItemNS: function(e2, t2) {
          for (var r2 = this.length; r2--; ) {
            var n2 = this[r2];
            if (n2.localName == t2 && n2.namespaceURI == e2) return n2;
          }
          return null;
        } }, _.prototype = { hasFeature: function(e2, t2) {
          return true;
        }, createDocument: function(e2, t2, r2) {
          var n2 = new H();
          if (n2.implementation = this, n2.childNodes = new D(), n2.doctype = r2 || null, r2 && n2.appendChild(r2), t2) {
            var o2 = n2.createElementNS(e2, t2);
            n2.appendChild(o2);
          }
          return n2;
        }, createDocumentType: function(e2, t2, r2) {
          var n2 = new ie();
          return n2.name = e2, n2.nodeName = e2, n2.publicId = t2 || "", n2.systemId = r2 || "", n2;
        } }, M.prototype = { firstChild: null, lastChild: null, previousSibling: null, nextSibling: null, attributes: null, parentNode: null, childNodes: null, ownerDocument: null, nodeValue: null, namespaceURI: null, prefix: null, localName: null, insertBefore: function(e2, t2) {
          return Z(this, e2, t2);
        }, replaceChild: function(e2, t2) {
          Z(this, e2, t2, Y), t2 && this.removeChild(t2);
        }, removeChild: function(e2) {
          return z(this, e2);
        }, appendChild: function(e2) {
          return this.insertBefore(e2, null);
        }, hasChildNodes: function() {
          return null != this.firstChild;
        }, cloneNode: function(e2) {
          return ve(this.ownerDocument || this, this, e2);
        }, normalize: function() {
          for (var e2 = this.firstChild; e2; ) {
            var t2 = e2.nextSibling;
            t2 && t2.nodeType == h && e2.nodeType == h ? (this.removeChild(t2), e2.appendData(t2.data)) : (e2.normalize(), e2 = t2);
          }
        }, isSupported: function(e2, t2) {
          return this.ownerDocument.implementation.hasFeature(e2, t2);
        }, hasAttributes: function() {
          return this.attributes.length > 0;
        }, lookupPrefix: function(e2) {
          for (var t2 = this; t2; ) {
            var r2 = t2._nsMap;
            if (r2) {
              for (var n2 in r2) if (Object.prototype.hasOwnProperty.call(r2, n2) && r2[n2] === e2) return n2;
            }
            t2 = t2.nodeType == f ? t2.ownerDocument : t2.parentNode;
          }
          return null;
        }, lookupNamespaceURI: function(e2) {
          for (var t2 = this; t2; ) {
            var r2 = t2._nsMap;
            if (r2 && Object.prototype.hasOwnProperty.call(r2, e2)) return r2[e2];
            t2 = t2.nodeType == f ? t2.ownerDocument : t2.parentNode;
          }
          return null;
        }, isDefaultNamespace: function(e2) {
          return null == this.lookupPrefix(e2);
        } }, u(d, M), u(d, M.prototype), H.prototype = { nodeName: "#document", nodeType: b, doctype: null, documentElement: null, _inc: 1, insertBefore: function(e2, t2) {
          if (e2.nodeType == S) {
            for (var r2 = e2.firstChild; r2; ) {
              var n2 = r2.nextSibling;
              this.insertBefore(r2, t2), r2 = n2;
            }
            return e2;
          }
          return Z(this, e2, t2), e2.ownerDocument = this, null === this.documentElement && e2.nodeType === p && (this.documentElement = e2), e2;
        }, removeChild: function(e2) {
          return this.documentElement == e2 && (this.documentElement = null), z(this, e2);
        }, replaceChild: function(e2, t2) {
          Z(this, e2, t2, Y), e2.ownerDocument = this, t2 && this.removeChild(t2), G(e2) && (this.documentElement = e2);
        }, importNode: function(e2, t2) {
          return ye(this, e2, t2);
        }, getElementById: function(e2) {
          var t2 = null;
          return j(this.documentElement, function(r2) {
            if (r2.nodeType == p && r2.getAttribute("id") == e2) return t2 = r2, true;
          }), t2;
        }, getElementsByClassName: function(e2) {
          var t2 = c(e2);
          return new N(this, function(r2) {
            var n2 = [];
            return t2.length > 0 && j(r2.documentElement, function(o2) {
              if (o2 !== r2 && o2.nodeType === p) {
                var a2 = o2.getAttribute("class");
                if (a2) {
                  var i2 = e2 === a2;
                  if (!i2) {
                    var s2 = c(a2);
                    i2 = t2.every((u2 = s2, function(e3) {
                      return u2 && -1 !== u2.indexOf(e3);
                    }));
                  }
                  i2 && n2.push(o2);
                }
              }
              var u2;
            }), n2;
          });
        }, createElement: function(e2) {
          var t2 = new ee();
          return t2.ownerDocument = this, t2.nodeName = e2, t2.tagName = e2, t2.localName = e2, t2.childNodes = new D(), (t2.attributes = new O())._ownerElement = t2, t2;
        }, createDocumentFragment: function() {
          var e2 = new le();
          return e2.ownerDocument = this, e2.childNodes = new D(), e2;
        }, createTextNode: function(e2) {
          var t2 = new ne();
          return t2.ownerDocument = this, t2.appendData(e2), t2;
        }, createComment: function(e2) {
          var t2 = new oe();
          return t2.ownerDocument = this, t2.appendData(e2), t2;
        }, createCDATASection: function(e2) {
          var t2 = new ae();
          return t2.ownerDocument = this, t2.appendData(e2), t2;
        }, createProcessingInstruction: function(e2, t2) {
          var r2 = new de();
          return r2.ownerDocument = this, r2.tagName = r2.nodeName = r2.target = e2, r2.nodeValue = r2.data = t2, r2;
        }, createAttribute: function(e2) {
          var t2 = new te();
          return t2.ownerDocument = this, t2.name = e2, t2.nodeName = e2, t2.localName = e2, t2.specified = true, t2;
        }, createEntityReference: function(e2) {
          var t2 = new ue();
          return t2.ownerDocument = this, t2.nodeName = e2, t2;
        }, createElementNS: function(e2, t2) {
          var r2 = new ee(), n2 = t2.split(":"), o2 = r2.attributes = new O();
          return r2.childNodes = new D(), r2.ownerDocument = this, r2.nodeName = t2, r2.tagName = t2, r2.namespaceURI = e2, 2 == n2.length ? (r2.prefix = n2[0], r2.localName = n2[1]) : r2.localName = t2, o2._ownerElement = r2, r2;
        }, createAttributeNS: function(e2, t2) {
          var r2 = new te(), n2 = t2.split(":");
          return r2.ownerDocument = this, r2.nodeName = t2, r2.name = t2, r2.namespaceURI = e2, r2.specified = true, 2 == n2.length ? (r2.prefix = n2[0], r2.localName = n2[1]) : r2.localName = t2, r2;
        } }, l(H, M), ee.prototype = { nodeType: p, hasAttribute: function(e2) {
          return null != this.getAttributeNode(e2);
        }, getAttribute: function(e2) {
          var t2 = this.getAttributeNode(e2);
          return t2 && t2.value || "";
        }, getAttributeNode: function(e2) {
          return this.attributes.getNamedItem(e2);
        }, setAttribute: function(e2, t2) {
          var r2 = this.ownerDocument.createAttribute(e2);
          r2.value = r2.nodeValue = "" + t2, this.setAttributeNode(r2);
        }, removeAttribute: function(e2) {
          var t2 = this.getAttributeNode(e2);
          t2 && this.removeAttributeNode(t2);
        }, appendChild: function(e2) {
          return e2.nodeType === S ? this.insertBefore(e2, null) : function(e3, t2) {
            return t2.parentNode && t2.parentNode.removeChild(t2), t2.parentNode = e3, t2.previousSibling = e3.lastChild, t2.nextSibling = null, t2.previousSibling ? t2.previousSibling.nextSibling = t2 : e3.firstChild = t2, e3.lastChild = t2, K(e3.ownerDocument, e3, t2), t2;
          }(this, e2);
        }, setAttributeNode: function(e2) {
          return this.attributes.setNamedItem(e2);
        }, setAttributeNodeNS: function(e2) {
          return this.attributes.setNamedItemNS(e2);
        }, removeAttributeNode: function(e2) {
          return this.attributes.removeNamedItem(e2.nodeName);
        }, removeAttributeNS: function(e2, t2) {
          var r2 = this.getAttributeNodeNS(e2, t2);
          r2 && this.removeAttributeNode(r2);
        }, hasAttributeNS: function(e2, t2) {
          return null != this.getAttributeNodeNS(e2, t2);
        }, getAttributeNS: function(e2, t2) {
          var r2 = this.getAttributeNodeNS(e2, t2);
          return r2 && r2.value || "";
        }, setAttributeNS: function(e2, t2, r2) {
          var n2 = this.ownerDocument.createAttributeNS(e2, t2);
          n2.value = n2.nodeValue = "" + r2, this.setAttributeNode(n2);
        }, getAttributeNodeNS: function(e2, t2) {
          return this.attributes.getNamedItemNS(e2, t2);
        }, getElementsByTagName: function(e2) {
          return new N(this, function(t2) {
            var r2 = [];
            return j(t2, function(n2) {
              n2 === t2 || n2.nodeType != p || "*" !== e2 && n2.tagName != e2 || r2.push(n2);
            }), r2;
          });
        }, getElementsByTagNameNS: function(e2, t2) {
          return new N(this, function(r2) {
            var n2 = [];
            return j(r2, function(o2) {
              o2 === r2 || o2.nodeType !== p || "*" !== e2 && o2.namespaceURI !== e2 || "*" !== t2 && o2.localName != t2 || n2.push(o2);
            }), n2;
          });
        } }, H.prototype.getElementsByTagName = ee.prototype.getElementsByTagName, H.prototype.getElementsByTagNameNS = ee.prototype.getElementsByTagNameNS, l(ee, M), te.prototype.nodeType = f, l(te, M), re.prototype = { data: "", substringData: function(e2, t2) {
          return this.data.substring(e2, e2 + t2);
        }, appendData: function(e2) {
          e2 = this.data + e2, this.nodeValue = this.data = e2, this.length = e2.length;
        }, insertData: function(e2, t2) {
          this.replaceData(e2, 0, t2);
        }, appendChild: function(e2) {
          throw new Error(R[x]);
        }, deleteData: function(e2, t2) {
          this.replaceData(e2, t2, "");
        }, replaceData: function(e2, t2, r2) {
          r2 = this.data.substring(0, e2) + r2 + this.data.substring(e2 + t2), this.nodeValue = this.data = r2, this.length = r2.length;
        } }, l(re, M), ne.prototype = { nodeName: "#text", nodeType: h, splitText: function(e2) {
          var t2 = this.data, r2 = t2.substring(e2);
          t2 = t2.substring(0, e2), this.data = this.nodeValue = t2, this.length = t2.length;
          var n2 = this.ownerDocument.createTextNode(r2);
          return this.parentNode && this.parentNode.insertBefore(n2, this.nextSibling), n2;
        } }, l(ne, re), oe.prototype = { nodeName: "#comment", nodeType: k }, l(oe, re), ae.prototype = { nodeName: "#cdata-section", nodeType: m }, l(ae, re), ie.prototype.nodeType = C, l(ie, M), se.prototype.nodeType = T, l(se, M), ce.prototype.nodeType = y, l(ce, M), ue.prototype.nodeType = g, l(ue, M), le.prototype.nodeName = "#document-fragment", le.prototype.nodeType = S, l(le, M), de.prototype.nodeType = v, l(de, M), pe.prototype.serializeToString = function(e2, t2, r2) {
          return fe.call(e2, t2, r2);
        }, M.prototype.toString = fe;
        try {
          if (Object.defineProperty) {
            let be2 = function(e2) {
              switch (e2.nodeType) {
                case p:
                case S:
                  var t2 = [];
                  for (e2 = e2.firstChild; e2; ) 7 !== e2.nodeType && 8 !== e2.nodeType && t2.push(be2(e2)), e2 = e2.nextSibling;
                  return t2.join("");
                default:
                  return e2.nodeValue;
              }
            };
            var be = be2;
            Object.defineProperty(N.prototype, "length", { get: function() {
              return P(this), this.$$length;
            } }), Object.defineProperty(M.prototype, "textContent", { get: function() {
              return be2(this);
            }, set: function(e2) {
              switch (this.nodeType) {
                case p:
                case S:
                  for (; this.firstChild; ) this.removeChild(this.firstChild);
                  (e2 || String(e2)) && this.appendChild(this.ownerDocument.createTextNode(e2));
                  break;
                default:
                  this.data = e2, this.value = e2, this.nodeValue = e2;
              }
            } }), ke = function(e2, t2, r2) {
              e2["$$" + t2] = r2;
            };
          }
        } catch (Ce) {
        }
        t.DocumentType = ie, t.DOMException = B, t.DOMImplementation = _, t.Element = ee, t.Node = M, t.NodeList = D, t.XMLSerializer = pe;
      }, function(e, t, r) {
        var n = r(8), o = r(9), a = r(0), i = r(3), s = null, c = function(e2) {
          return !e2 || e2 < 0 ? 0 : (e2 / 1e3).toFixed(3);
        }, u = function() {
          var e2 = function() {
            return (65536 * (1 + Math.random()) | 0).toString(16).substring(1);
          };
          return e2() + e2() + "-" + e2() + "-" + e2() + "-" + e2() + "-" + e2() + e2() + e2();
        }, l = function() {
          if ("object" === ("undefined" == typeof navigator ? "undefined" : a(navigator))) {
            var e2 = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            return (null == e2 ? void 0 : e2.type) || (null == e2 ? void 0 : e2.effectiveType) || "unknown";
          }
          return "unknown";
        }, d = function() {
          return "object" === ("undefined" == typeof location ? "undefined" : a(location)) ? location.protocol.replace(/:/, "") : "unknown protocol";
        }, p = function() {
          if ("object" !== ("undefined" == typeof navigator ? "undefined" : a(navigator))) return "unknown os";
          var e2 = navigator.userAgent.toLowerCase(), t2 = /macintosh|mac os x/i.test(navigator.userAgent);
          return e2.indexOf("win32") >= 0 || e2.indexOf("wow32") >= 0 ? "win32" : e2.indexOf("win64") >= 0 || e2.indexOf("wow64") >= 0 ? "win64" : t2 ? "mac" : "unknown os";
        }, f = function() {
          return !("object" !== ("undefined" == typeof navigator ? "undefined" : a(navigator)) || !navigator.userAgent.match(/(Android|Adr|Linux)/i));
        }, h = function() {
          return !("object" !== ("undefined" == typeof navigator ? "undefined" : a(navigator)) || !navigator.userAgent.match(/(iPhone|iPod|iPad|iOS)/i));
        }, m = function() {
          return "object" !== ("undefined" == typeof navigator ? "undefined" : a(navigator)) ? "unknown device" : navigator.userAgent;
        }, g = function() {
          return !("object" !== ("undefined" == typeof navigator ? "undefined" : a(navigator)) || !navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i));
        }(), y = f() ? "android" : h ? "ios" : "other_mobile", v = p(), k = g ? y : v, b = m(), C = d();
        function S(e2) {
          return e2.replace(/([A-Z])/g, "_$1").toLowerCase();
        }
        var T = function() {
          "use strict";
          function e2(t2) {
            n(this, e2);
            var r2 = t2.parent, o2 = t2.traceId, a2 = t2.bucket, c2 = t2.region, l2 = t2.apiName, d2 = t2.realApi, p2 = t2.httpMethod, f2 = t2.fileKey, h2 = t2.fileSize, m2 = t2.accelerate, g2 = t2.customId, y2 = t2.delay, v2 = t2.deepTracker, S2 = t2.Beacon, T2 = t2.clsReporter, w = a2 && a2.substr(a2.lastIndexOf("-") + 1) || "";
            this.parent = r2, this.deepTracker = v2, this.delay = y2, T2 && !this.clsReporter && (this.clsReporter = T2), this.params = { sdkVersionName: "cos-js-sdk-v5", sdkVersionCode: i.version, osName: k, networkType: "", requestName: l2 || "", requestResult: "", realApi: d2, bucket: a2, region: c2, accelerate: m2, httpMethod: p2, url: "", host: "", httpDomain: "", requestPath: f2 || "", userAgent: b, networkProtocol: C, errorType: "", errorCode: "", errorName: "", errorMessage: "", errorRequestId: "", errorHttpCode: 0, errorServiceName: "", errorNode: "", httpTookTime: 0, httpSize: h2 || 0, httpMd5: 0, httpSign: 0, httpFullTime: 0, httpSpeed: 0, md5StartTime: 0, md5EndTime: 0, signStartTime: 0, signEndTime: 0, httpStartTime: 0, httpEndTime: 0, startTime: (/* @__PURE__ */ new Date()).getTime(), endTime: 0, traceId: o2 || u(), appid: w, partNumber: 0, httpRetryTimes: 0, customId: g2 || "", partTime: 0 }, S2 && (this.beacon = function(e3, t3) {
              if (!s) {
                if ("function" != typeof e3) throw new Error("Beacon not found");
                s = new e3({ appkey: "0WEB05PY6MHRGK0U", versionCode: i.version, channelID: "js_sdk", openid: "openid", unionid: "unid", strictMode: false, delay: t3, sessionDuration: 6e4 });
              }
              return s;
            }(S2, y2));
          }
          return o(e2, [{ key: "formatResult", value: function(e3, t2) {
            var r2, n2, o2, a2, i2, s2, u2 = (/* @__PURE__ */ new Date()).getTime(), d2 = l(), p2 = e3 ? (null == e3 ? void 0 : e3.code) || (null == e3 || null === (r2 = e3.error) || void 0 === r2 ? void 0 : r2.code) || (null == e3 || null === (n2 = e3.error) || void 0 === n2 ? void 0 : n2.Code) : "", f2 = e3 ? (null == e3 ? void 0 : e3.message) || (null == e3 || null === (o2 = e3.error) || void 0 === o2 ? void 0 : o2.message) || (null == e3 || null === (a2 = e3.error) || void 0 === a2 ? void 0 : a2.Message) : "", h2 = f2, m2 = e3 ? (null == e3 ? void 0 : e3.resource) || (null == e3 || null === (i2 = e3.error) || void 0 === i2 ? void 0 : i2.resource) || (null == e3 || null === (s2 = e3.error) || void 0 === s2 ? void 0 : s2.Resource) : "", g2 = e3 ? null == e3 ? void 0 : e3.statusCode : t2.statusCode, y2 = e3 ? (null == e3 ? void 0 : e3.headers) && (null == e3 ? void 0 : e3.headers["x-cos-request-id"]) : (null == t2 ? void 0 : t2.headers) && (null == t2 ? void 0 : t2.headers["x-cos-request-id"]), v2 = e3 ? y2 ? "Server" : "Client" : "";
            "getObject" === this.params.requestName && (this.params.httpSize = t2 ? t2.headers && t2.headers["content-length"] : 0);
            var k2 = "sliceUploadFile" === this.params.realApi, b2 = "sliceCopyFile" === this.params.realApi;
            if (k2 || b2) {
              var C2 = this.params.httpSize / 1024 / this.params.partTime;
              Object.assign(this.params, { httpSpeed: C2 < 0 ? 0 : C2.toFixed(3) });
            } else {
              var S2 = u2 - this.params.startTime, T2 = this.params.httpEndTime - this.params.httpStartTime, w = this.params.httpSize / 1024 / (T2 / 1e3), R = this.params.md5EndTime - this.params.md5StartTime, x = this.params.signEndTime - this.params.signStartTime;
              this.parent && (this.parent.addParamValue("httpTookTime", c(T2)), this.parent.addParamValue("httpFullTime", c(S2)), this.parent.addParamValue("httpMd5", c(R)), this.parent.addParamValue("httpSign", c(x)), ["multipartUpload", "uploadPartCopy", "putObjectCopy"].includes(this.params.requestName) && this.parent.addParamValue("partTime", c(T2))), Object.assign(this.params, { httpFullTime: c(S2), httpMd5: c(R), httpSign: c(x), httpTookTime: c(T2), httpSpeed: w < 0 ? 0 : w.toFixed(3) });
            }
            if (Object.assign(this.params, { networkType: d2, requestResult: e3 ? "Failure" : "Success", errorType: v2, errorCode: p2, errorHttpCode: g2, errorName: h2, errorMessage: f2, errorServiceName: m2, errorRequestId: y2 }), !e3 || p2 && f2 || (this.params.fullError = e3 ? JSON.stringify(e3) : ""), this.params.url) {
              try {
                var A = /^http(s)?:\/\/(.*?)\//.exec(this.params.url);
                this.params.host = A[2];
              } catch (e4) {
                this.params.host = this.params.url;
              }
              this.params.httpDomain = this.params.host;
            }
          } }, { key: "report", value: function(e3, t2) {
            if (this.beacon || this.clsReporter) {
              this.formatResult(e3, t2);
              var r2 = function(e4) {
                var t3, r3 = {}, n2 = ["sdkVersionName", "sdkVersionCode", "osName", "networkType", "requestName", "requestResult", "bucket", "region", "appid", "accelerate", "url", "host", "requestPath", "userAgent", "networkProtocol", "httpMethod", "httpSize", "httpSpeed", "httpTookTime", "httpMd5", "httpSign", "httpFullTime", "httpDomain", "partNumber", "httpRetryTimes", "customId", "traceId", "realApi"], o2 = [].concat(n2, ["errorNode", "errorCode", "errorName", "errorMessage", "errorRequestId", "errorHttpCode", "errorServiceName", "errorType", "fullError"]), a2 = "Success" === e4.requestResult ? n2 : o2;
                for (var i2 in e4) a2.includes(i2) && (r3[S(i2)] = e4[i2]);
                return r3.request_name = e4.realApi ? (t3 = e4.realApi, ["putObject", "sliceUploadFile", "uploadFile", "uploadFiles"].includes(t3) ? "UploadTask" : "getObject" === t3 ? "DownloadTask" : ["putObjectCopy", "sliceCopyFile"].includes(t3) ? "CopyTask" : t3) : e4.requestName, r3;
              }(this.params);
              this.beacon && this.sendEventsToBeacon(r2), this.clsReporter && this.sendEventsToCLS(r2);
            }
          } }, { key: "setParams", value: function(e3) {
            Object.assign(this.params, e3);
          } }, { key: "addParamValue", value: function(e3, t2) {
            this.params[e3] = (+this.params[e3] + +t2).toFixed(3);
          } }, { key: "sendEventsToBeacon", value: function(e3) {
            if (!("sliceUploadFile" === this.params.requestName || "sliceUploadFile" === this.params.realApi) || this.deepTracker) {
              var t2 = "qcloud_track_cos_sdk";
              0 === this.delay ? this.beacon && this.beacon.onDirectUserAction(t2, e3) : this.beacon && this.beacon.onUserAction(t2, e3);
            }
          } }, { key: "sendEventsToCLS", value: function(e3) {
            var t2 = !(0 !== this.delay);
            this.clsReporter.log(e3, t2);
          } }, { key: "generateSubTracker", value: function(t2) {
            return Object.assign(t2, { parent: this, deepTracker: this.deepTracker, traceId: this.params.traceId, bucket: this.params.bucket, region: this.params.region, accelerate: this.params.accelerate, fileKey: this.params.requestPath, customId: this.params.customId, delay: this.delay, clsReporter: this.clsReporter }), new e2(t2);
          } }]);
        }();
        e.exports = T;
      }, function(e, t) {
        e.exports = function(e2, t2) {
          if (!(e2 instanceof t2)) throw new TypeError("Cannot call a class as a function");
        }, e.exports.__esModule = true, e.exports.default = e.exports;
      }, function(e, t, r) {
        var n = r(27);
        function o(e2, t2) {
          for (var r2 = 0; r2 < t2.length; r2++) {
            var o2 = t2[r2];
            o2.enumerable = o2.enumerable || false, o2.configurable = true, "value" in o2 && (o2.writable = true), Object.defineProperty(e2, n(o2.key), o2);
          }
        }
        e.exports = function(e2, t2, r2) {
          return t2 && o(e2.prototype, t2), r2 && o(e2, r2), Object.defineProperty(e2, "prototype", { writable: false }), e2;
        }, e.exports.__esModule = true, e.exports.default = e.exports;
      }, function(e, t, r) {
        var n = r(11), o = r(1), a = {};
        e.exports.transferToTaskMethod = function(e2, t2) {
          a[t2] = e2[t2], e2[t2] = function(e3, r2) {
            e3.SkipTask ? a[t2].call(this, e3, r2) : this._addTask(t2, e3, r2);
          };
        }, e.exports.init = function(e2) {
          var t2, r2, i = [], s = {}, c = 0, u = 0, l = function(e3) {
            var t3 = { id: e3.id, Bucket: e3.Bucket, Region: e3.Region, Key: e3.Key, FilePath: e3.FilePath, state: e3.state, loaded: e3.loaded, size: e3.size, speed: e3.speed, percent: e3.percent, hashPercent: e3.hashPercent, error: e3.error };
            return e3.FilePath && (t3.FilePath = e3.FilePath), e3._custom && (t3._custom = e3._custom), t3;
          }, d = (r2 = function() {
            t2 = 0, e2.emit("task-list-update", { list: o.map(i, l) }), e2.emit("list-update", { list: o.map(i, l) });
          }, function() {
            t2 || (t2 = setTimeout(r2));
          }), p = function() {
            if (!(i.length <= e2.options.UploadQueueSize)) {
              for (var t3 = 0; t3 < u && t3 < i.length && i.length > e2.options.UploadQueueSize; ) {
                var r3 = "waiting" === i[t3].state || "checking" === i[t3].state || "uploading" === i[t3].state;
                i[t3] && r3 ? t3++ : (s[i[t3].id] && delete s[i[t3].id], i.splice(t3, 1), u--);
              }
              d();
            }
          }, f = function t3() {
            if (!(c >= e2.options.FileParallelLimit)) {
              for (; i[u] && "waiting" !== i[u].state; ) u++;
              if (!(u >= i.length)) {
                var r3 = i[u];
                u++, c++, r3.state = "checking", r3.params.onTaskStart && r3.params.onTaskStart(l(r3)), !r3.params.UploadData && (r3.params.UploadData = {});
                var n2 = o.formatParams(r3.api, r3.params);
                a[r3.api].call(e2, n2, function(n3, o2) {
                  e2._isRunningTask(r3.id) && ("checking" !== r3.state && "uploading" !== r3.state || (r3.state = n3 ? "error" : "success", n3 && (r3.error = n3), c--, d(), t3(), r3.callback && r3.callback(n3, o2), "success" === r3.state && (r3.params && (delete r3.params.UploadData, delete r3.params.Body, delete r3.params), delete r3.callback)), p());
                }), d(), setTimeout(t3);
              }
            }
          }, h = function(t3, r3) {
            var o2 = s[t3];
            if (o2) {
              var a2 = o2 && "waiting" === o2.state, i2 = o2 && ("checking" === o2.state || "uploading" === o2.state);
              if ("canceled" === r3 && "canceled" !== o2.state || "paused" === r3 && a2 || "paused" === r3 && i2) {
                o2.state = r3, e2.emit("inner-kill-task", { TaskId: t3, toState: r3 });
                try {
                  var u2 = o2 && o2.params && o2.params.UploadData.UploadId;
                } catch (e3) {
                }
                "canceled" === r3 && u2 && n.removeUsing(u2), d(), i2 && (c--, f()), "canceled" === r3 && (o2.params && (delete o2.params.UploadData, delete o2.params.Body, delete o2.params), delete o2.callback);
              }
              p();
            }
          };
          e2._addTasks = function(t3) {
            o.each(t3, function(t4) {
              e2._addTask(t4.api, t4.params, t4.callback, true);
            }), d();
          };
          e2._addTask = function(t3, r3, n2, a2) {
            r3 = o.formatParams(t3, r3);
            var c2 = o.uuid();
            r3.TaskId = c2, r3.onTaskReady && r3.onTaskReady(c2), r3.TaskReady && (r3.TaskReady(c2), false);
            var u2 = { params: r3, callback: n2, api: t3, index: i.length, id: c2, Bucket: r3.Bucket, Region: r3.Region, Key: r3.Key, FilePath: r3.FilePath || "", state: "waiting", loaded: 0, size: 0, speed: 0, percent: 0, hashPercent: 0, error: null, _custom: r3._custom }, l2 = r3.onHashProgress;
            r3.onHashProgress = function(t4) {
              e2._isRunningTask(u2.id) && (u2.hashPercent = t4.percent, l2 && l2(t4), d());
            };
            var h2 = r3.onProgress;
            return r3.onProgress = function(t4) {
              e2._isRunningTask(u2.id) && ("checking" === u2.state && (u2.state = "uploading"), u2.loaded = t4.loaded, u2.speed = t4.speed, u2.percent = t4.percent, h2 && h2(t4), d());
            }, o.getFileSize(t3, r3, function(e3, t4) {
              if (e3) return n2(o.error(e3));
              s[c2] = u2, i.push(u2), u2.size = t4, !a2 && d(), f(), p();
            }), c2;
          }, e2._isRunningTask = function(e3) {
            var t3 = s[e3];
            return !(!t3 || "checking" !== t3.state && "uploading" !== t3.state);
          }, e2.getTaskList = function() {
            return o.map(i, l);
          }, e2.cancelTask = function(e3) {
            h(e3, "canceled");
          }, e2.pauseTask = function(e3) {
            h(e3, "paused");
          }, e2.restartTask = function(e3) {
            var t3 = s[e3];
            !t3 || "paused" !== t3.state && "error" !== t3.state || (t3.state = "waiting", d(), u = Math.min(u, t3.index), f());
          }, e2.isUploadRunning = function() {
            return c || u < i.length;
          };
        };
      }, function(e, t, r) {
        var n, o, a = r(1), i = "cos_sdk_upload_cache", s = function() {
          try {
            var e2 = JSON.parse(localStorage.getItem(i));
          } catch (e3) {
          }
          e2 || (e2 = []), n = e2;
        }, c = function() {
          try {
            n.length ? localStorage.setItem(i, JSON.stringify(n)) : localStorage.removeItem(i);
          } catch (e2) {
          }
        }, u = function() {
          if (!n) {
            s.call(this);
            for (var e2 = false, t2 = Math.round(Date.now() / 1e3), r2 = n.length - 1; r2 >= 0; r2--) {
              var o2 = n[r2][2];
              (!o2 || o2 + 2592e3 < t2) && (n.splice(r2, 1), e2 = true);
            }
            e2 && c();
          }
        }, l = function() {
          o || (o = setTimeout(function() {
            c(), o = null;
          }, 400));
        }, d = { using: {}, setUsing: function(e2) {
          d.using[e2] = true;
        }, removeUsing: function(e2) {
          delete d.using[e2];
        }, getFileId: function(e2, t2, r2, n2) {
          return e2.name && e2.size && e2.lastModifiedDate && t2 ? a.md5([e2.name, e2.size, e2.lastModifiedDate, t2, r2, n2].join("::")) : null;
        }, getCopyFileId: function(e2, t2, r2, n2, o2) {
          var i2 = t2["content-length"], s2 = t2.etag || "", c2 = t2["last-modified"];
          return e2 && r2 ? a.md5([e2, i2, s2, c2, r2, n2, o2].join("::")) : null;
        }, getUploadIdList: function(e2) {
          if (!e2) return null;
          u.call(this);
          for (var t2 = [], r2 = 0; r2 < n.length; r2++) n[r2][0] === e2 && t2.push(n[r2][1]);
          return t2.length ? t2 : null;
        }, saveUploadId: function(e2, t2, r2) {
          if (u.call(this), e2) {
            for (var o2 = n.length - 1; o2 >= 0; o2--) {
              var a2 = n[o2];
              a2[0] === e2 && a2[1] === t2 && n.splice(o2, 1);
            }
            n.unshift([e2, t2, Math.round(Date.now() / 1e3)]), n.length > r2 && n.splice(r2), l();
          }
        }, removeUploadId: function(e2) {
          u.call(this), delete d.using[e2];
          for (var t2 = n.length - 1; t2 >= 0; t2--) n[t2][1] === e2 && n.splice(t2, 1);
          l();
        } };
        e.exports = d;
      }, function(e, t, r) {
        var n = r(0), o = r(29), a = r(1);
        function i(e2, t2, r2) {
          var n2 = a.clone(t2.InventoryConfiguration);
          if (n2.OptionalFields) {
            var o2 = n2.OptionalFields || [];
            n2.OptionalFields = { Field: o2 };
          }
          if (n2.Destination && n2.Destination.COSBucketDestination && n2.Destination.COSBucketDestination.Encryption) {
            var i2 = n2.Destination.COSBucketDestination.Encryption;
            Object.keys(i2).indexOf("SSECOS") > -1 && (i2["SSE-COS"] = i2.SSECOS, delete i2.SSECOS);
          }
          var s2 = a.json2xml({ InventoryConfiguration: n2 }), c2 = t2.Headers;
          c2["Content-Type"] = "application/xml", c2["Content-MD5"] = a.b64(a.md5(s2));
          var u2 = "PUT" === e2 ? "name/cos:PutBucketInventory" : "name/cos:PostBucketInventory";
          h.call(this, { Action: u2, method: e2, Bucket: t2.Bucket, Region: t2.Region, body: s2, action: "inventory", qs: { id: t2.Id }, headers: c2, tracker: t2.tracker }, function(e3, t3) {
            return e3 && 204 === e3.statusCode ? r2(null, { statusCode: e3.statusCode }) : e3 ? r2(e3) : void r2(null, { statusCode: t3.statusCode, headers: t3.headers });
          });
        }
        function s(e2) {
          var t2 = { GrantFullControl: [], GrantWrite: [], GrantRead: [], GrantReadAcp: [], GrantWriteAcp: [], ACL: "" }, r2 = { FULL_CONTROL: "GrantFullControl", WRITE: "GrantWrite", READ: "GrantRead", READ_ACP: "GrantReadAcp", WRITE_ACP: "GrantWriteAcp" }, n2 = (e2 && e2.AccessControlList || {}).Grant;
          n2 && (n2 = a.isArray(n2) ? n2 : [n2]);
          var o2 = { READ: 0, WRITE: 0, FULL_CONTROL: 0 };
          return n2 && n2.length && a.each(n2, function(n3) {
            "qcs::cam::anyone:anyone" === n3.Grantee.ID || "http://cam.qcloud.com/groups/global/AllUsers" === n3.Grantee.URI ? o2[n3.Permission] = 1 : n3.Grantee.ID !== e2.Owner.ID && t2[r2[n3.Permission]].push('id="' + n3.Grantee.ID + '"');
          }), o2.FULL_CONTROL || o2.WRITE && o2.READ ? t2.ACL = "public-read-write" : o2.READ ? t2.ACL = "public-read" : t2.ACL = "private", a.each(r2, function(e3) {
            t2[e3] = c(t2[e3].join(","));
          }), t2;
        }
        function c(e2) {
          var t2, r2, n2 = e2.split(","), o2 = {};
          for (t2 = 0; t2 < n2.length; ) o2[r2 = n2[t2].trim()] ? n2.splice(t2, 1) : (o2[r2] = true, n2[t2] = r2, t2++);
          return n2.join(",");
        }
        function u(e2) {
          var t2 = e2.region || "", r2 = e2.bucket || "", o2 = r2.substr(0, r2.lastIndexOf("-")), i2 = r2.substr(r2.lastIndexOf("-") + 1), s2 = e2.domain, c2 = e2.object;
          "function" == typeof s2 && (s2 = s2({ Bucket: r2, Region: t2 })), ["http", "https"].includes(e2.protocol) && (e2.protocol = e2.protocol + ":");
          var u2 = e2.protocol || (a.isBrowser && "object" === ("undefined" == typeof location ? "undefined" : n(location)) && "http:" === location.protocol ? "http:" : "https:");
          s2 || (s2 = ["cn-south", "cn-south-2", "cn-north", "cn-east", "cn-southwest", "sg"].indexOf(t2) > -1 ? "{Region}.myqcloud.com" : "cos.{Region}.myqcloud.com", e2.ForcePathStyle || (s2 = "{Bucket}." + s2)), s2 = (s2 = s2.replace(/\{\{AppId\}\}/gi, i2).replace(/\{\{Bucket\}\}/gi, o2).replace(/\{\{Region\}\}/gi, t2).replace(/\{\{.*?\}\}/gi, "")).replace(/\{AppId\}/gi, i2).replace(/\{BucketName\}/gi, o2).replace(/\{Bucket\}/gi, r2).replace(/\{Region\}/gi, t2).replace(/\{.*?\}/gi, ""), /^[a-zA-Z]+:\/\//.test(s2) || (s2 = u2 + "//" + s2), "/" === s2.slice(-1) && (s2 = s2.slice(0, -1));
          var l2 = s2;
          return e2.ForcePathStyle && (l2 += "/" + r2), l2 += "/", c2 && (l2 += a.camSafeUrlEncode(c2).replace(/%2F/g, "/")), e2.isLocation && (l2 = l2.replace(/^https?:\/\//, "")), l2;
        }
        var l = function(e2) {
          if (!e2.Bucket || !e2.Region) return "";
          var t2 = void 0 === e2.UseAccelerate ? this.options.UseAccelerate : e2.UseAccelerate;
          return (e2.Url || u({ ForcePathStyle: this.options.ForcePathStyle, protocol: this.options.Protocol, domain: this.options.Domain, bucket: e2.Bucket, region: t2 ? "accelerate" : e2.Region })).replace(/^https?:\/\/([^/]+)(\/.*)?$/, "$1");
        };
        function d(e2, t2) {
          var r2 = a.clone(e2.Headers), n2 = "";
          a.each(r2, function(e3, t3) {
            ("" === e3 || ["content-type", "cache-control", "expires"].indexOf(t3.toLowerCase()) > -1) && delete r2[t3], "host" === t3.toLowerCase() && (n2 = e3);
          });
          var o2 = false !== e2.ForceSignHost;
          !n2 && e2.SignHost && o2 && (r2.Host = e2.SignHost);
          var i2 = false, s2 = function(e3, r3) {
            i2 || (i2 = true, r3 && r3.XCosSecurityToken && !r3.SecurityToken && ((r3 = a.clone(r3)).SecurityToken = r3.XCosSecurityToken, delete r3.XCosSecurityToken), t2 && t2(e3, r3));
          }, c2 = this, u2 = e2.Bucket || "", l2 = e2.Region || "", d2 = e2.Key || "";
          c2.options.ForcePathStyle && u2 && (d2 = u2 + "/" + d2);
          var p2 = "/" + d2, f2 = {}, h2 = e2.Scope;
          if (!h2) {
            var m2 = e2.Action || "", g2 = e2.ResourceKey || e2.Key || "";
            h2 = e2.Scope || [{ action: m2, bucket: u2, region: l2, prefix: g2 }];
          }
          var y = a.md5(JSON.stringify(h2));
          c2._StsCache = c2._StsCache || [], function() {
            var e3, t3;
            for (e3 = c2._StsCache.length - 1; e3 >= 0; e3--) {
              t3 = c2._StsCache[e3];
              var r3 = Math.round(a.getSkewTime(c2.options.SystemClockOffset) / 1e3) + 30;
              if (t3.StartTime && r3 < t3.StartTime || r3 >= t3.ExpiredTime) c2._StsCache.splice(e3, 1);
              else if (!t3.ScopeLimit || t3.ScopeLimit && t3.ScopeKey === y) {
                f2 = t3;
                break;
              }
            }
          }();
          var v = function() {
            var t3 = "";
            f2.StartTime && e2.Expires ? t3 = f2.StartTime + ";" + (f2.StartTime + 1 * e2.Expires) : f2.StartTime && f2.ExpiredTime && (t3 = f2.StartTime + ";" + f2.ExpiredTime);
            var n3 = { Authorization: a.getAuth({ SecretId: f2.TmpSecretId, SecretKey: f2.TmpSecretKey, Method: e2.Method, Pathname: p2, Query: e2.Query, Headers: r2, Expires: e2.Expires, UseRawKey: c2.options.UseRawKey, SystemClockOffset: c2.options.SystemClockOffset, KeyTime: t3, ForceSignHost: o2 }), SecurityToken: f2.SecurityToken || f2.XCosSecurityToken || "", Token: f2.Token || "", ClientIP: f2.ClientIP || "", ClientUA: f2.ClientUA || "", SignFrom: "client" };
            s2(null, n3);
          }, k = function(e3) {
            if (e3.Authorization) {
              var t3 = false, r3 = e3.Authorization;
              if (r3) if (r3.indexOf(" ") > -1) t3 = false;
              else if (r3.indexOf("q-sign-algorithm=") > -1 && r3.indexOf("q-ak=") > -1 && r3.indexOf("q-sign-time=") > -1 && r3.indexOf("q-key-time=") > -1 && r3.indexOf("q-url-param-list=") > -1) t3 = true;
              else try {
                (r3 = atob(r3)).indexOf("a=") > -1 && r3.indexOf("k=") > -1 && r3.indexOf("t=") > -1 && r3.indexOf("r=") > -1 && r3.indexOf("b=") > -1 && (t3 = true);
              } catch (e4) {
              }
              if (!t3) return a.error(new Error("getAuthorization callback params format error"));
            } else {
              if (!e3.TmpSecretId) return a.error(new Error('getAuthorization callback params missing "TmpSecretId"'));
              if (!e3.TmpSecretKey) return a.error(new Error('getAuthorization callback params missing "TmpSecretKey"'));
              if (!e3.SecurityToken && !e3.XCosSecurityToken) return a.error(new Error('getAuthorization callback params missing "SecurityToken"'));
              if (!e3.ExpiredTime) return a.error(new Error('getAuthorization callback params missing "ExpiredTime"'));
              if (e3.ExpiredTime && 10 !== e3.ExpiredTime.toString().length) return a.error(new Error('getAuthorization callback params "ExpiredTime" should be 10 digits'));
              if (e3.StartTime && 10 !== e3.StartTime.toString().length) return a.error(new Error('getAuthorization callback params "StartTime" should be 10 StartTime'));
            }
            return false;
          };
          if (f2.ExpiredTime && f2.ExpiredTime - a.getSkewTime(c2.options.SystemClockOffset) / 1e3 > 60) v();
          else if (c2.options.getAuthorization) c2.options.getAuthorization.call(c2, { Bucket: u2, Region: l2, Method: e2.Method, Key: d2, Pathname: p2, Query: e2.Query, Headers: r2, Scope: h2, SystemClockOffset: c2.options.SystemClockOffset, ForceSignHost: o2 }, function(e3) {
            "string" == typeof e3 && (e3 = { Authorization: e3 });
            var t3 = k(e3);
            if (t3) return s2(t3);
            e3.Authorization ? s2(null, e3) : ((f2 = e3 || {}).Scope = h2, f2.ScopeKey = y, c2._StsCache.push(f2), v());
          });
          else {
            if (!c2.options.getSTS) return function() {
              var t3 = "";
              c2.options.StartTime && e2.Expires ? t3 = c2.options.StartTime + ";" + (c2.options.StartTime + 1 * e2.Expires) : c2.options.StartTime && c2.options.ExpiredTime && (t3 = c2.options.StartTime + ";" + c2.options.ExpiredTime);
              var n3 = { Authorization: a.getAuth({ SecretId: e2.SecretId || c2.options.SecretId, SecretKey: e2.SecretKey || c2.options.SecretKey, Method: e2.Method, Pathname: p2, Query: e2.Query, Headers: r2, Expires: e2.Expires, KeyTime: t3, UseRawKey: c2.options.UseRawKey, SystemClockOffset: c2.options.SystemClockOffset, ForceSignHost: o2 }), SecurityToken: c2.options.SecurityToken || c2.options.XCosSecurityToken, SignFrom: "client" };
              return s2(null, n3), n3;
            }();
            c2.options.getSTS.call(c2, { Bucket: u2, Region: l2 }, function(e3) {
              (f2 = e3 || {}).Scope = h2, f2.ScopeKey = y, f2.TmpSecretId || (f2.TmpSecretId = f2.SecretId), f2.TmpSecretKey || (f2.TmpSecretKey = f2.SecretKey);
              var t3 = k(f2);
              if (t3) return s2(t3);
              c2._StsCache.push(f2), v();
            });
          }
          return "";
        }
        function p(e2) {
          var t2 = false, r2 = false, n2 = false, o2 = e2.headers && (e2.headers.date || e2.headers.Date) || e2.error && e2.error.ServerTime;
          try {
            var i2 = e2.error.Code, s2 = e2.error.Message;
            ("RequestTimeTooSkewed" === i2 || "AccessDenied" === i2 && "Request has expired" === s2) && (r2 = true);
          } catch (e3) {
          }
          if (e2) if (r2 && o2) {
            var c2 = Date.parse(o2);
            this.options.CorrectClockSkew && Math.abs(a.getSkewTime(this.options.SystemClockOffset) - c2) >= 3e4 && (this.options.SystemClockOffset = c2 - Date.now(), t2 = true);
          } else 5 === Math.floor(e2.statusCode / 100) ? t2 = true : "CORS blocked or network error" === e2.message && (n2 = true, t2 = this.options.AutoSwitchHost);
          return { canRetry: t2, networkError: n2 };
        }
        function f(e2) {
          var t2 = e2.requestUrl, r2 = e2.clientCalcSign, n2 = e2.networkError;
          if (!this.options.AutoSwitchHost) return false;
          if (!t2) return false;
          if (!r2) return false;
          if (!n2) return false;
          return /^https?:\/\/[^\/]*\.cos\.[^\/]*\.myqcloud\.com(\/.*)?$/.test(t2) && !/^https?:\/\/[^\/]*\.cos\.accelerate\.myqcloud\.com(\/.*)?$/.test(t2);
        }
        function h(e2, t2) {
          var r2 = this;
          !e2.headers && (e2.headers = {}), !e2.qs && (e2.qs = {}), e2.VersionId && (e2.qs.versionId = e2.VersionId), e2.qs = a.clearKey(e2.qs), e2.headers && (e2.headers = a.clearKey(e2.headers)), e2.qs && (e2.qs = a.clearKey(e2.qs));
          var n2 = a.clone(e2.qs);
          e2.action && (n2[e2.action] = "");
          var o2 = e2.url || e2.Url, i2 = e2.SignHost || l.call(this, { Bucket: e2.Bucket, Region: e2.Region, Url: o2 }), s2 = e2.tracker;
          !function o3(a2) {
            var c2 = r2.options.SystemClockOffset;
            s2 && s2.setParams({ signStartTime: (/* @__PURE__ */ new Date()).getTime(), httpRetryTimes: a2 - 1 }), e2.SwitchHost && (i2 = i2.replace(/myqcloud.com/, "tencentcos.cn")), d.call(r2, { Bucket: e2.Bucket || "", Region: e2.Region || "", Method: e2.method, Key: e2.Key, Query: n2, Headers: e2.headers, SignHost: i2, Action: e2.Action, ResourceKey: e2.ResourceKey, Scope: e2.Scope, ForceSignHost: r2.options.ForceSignHost, SwitchHost: e2.SwitchHost }, function(n3, i3) {
              n3 ? t2(n3) : (s2 && s2.setParams({ signEndTime: (/* @__PURE__ */ new Date()).getTime(), httpStartTime: (/* @__PURE__ */ new Date()).getTime() }), e2.AuthData = i3, m.call(r2, e2, function(n4, u2) {
                s2 && s2.setParams({ httpEndTime: (/* @__PURE__ */ new Date()).getTime() });
                var l2 = false, d2 = false;
                if (n4) {
                  var h2 = p.call(r2, n4);
                  l2 = h2.canRetry || c2 !== r2.options.SystemClockOffset, d2 = h2.networkError;
                }
                if (n4 && a2 < 2 && l2) {
                  e2.headers && (delete e2.headers.Authorization, delete e2.headers.token, delete e2.headers.clientIP, delete e2.headers.clientUA, e2.headers["x-cos-security-token"] && delete e2.headers["x-cos-security-token"], e2.headers["x-ci-security-token"] && delete e2.headers["x-ci-security-token"]);
                  var m2 = f.call(r2, { requestUrl: (null == n4 ? void 0 : n4.url) || "", clientCalcSign: "client" === i3.SignFrom, networkError: d2 });
                  e2.SwitchHost = m2, o3(a2 + 1);
                } else t2(n4, u2);
              }));
            });
          }(1);
        }
        function m(e2, t2) {
          var r2 = this, n2 = e2.TaskId;
          if (!n2 || r2._isRunningTask(n2)) {
            var i2 = e2.Bucket, s2 = e2.Region, c2 = e2.Key, l2 = e2.method || "GET", d2 = e2.Url || e2.url, p2 = e2.body, f2 = e2.rawBody;
            r2.options.UseAccelerate && (s2 = "accelerate"), d2 = d2 || u({ ForcePathStyle: r2.options.ForcePathStyle, protocol: r2.options.Protocol, domain: r2.options.Domain, bucket: i2, region: s2, object: c2 }), e2.SwitchHost && (d2 = d2.replace(/myqcloud.com/, "tencentcos.cn"));
            var h2 = c2 ? d2 : "";
            e2.action && (d2 = d2 + "?" + (a.isIOS_QQ ? "".concat(e2.action, "=") : e2.action)), e2.qsStr && (d2 = d2.indexOf("?") > -1 ? d2 + "&" + e2.qsStr : d2 + "?" + e2.qsStr);
            var m2 = { method: l2, url: d2, headers: e2.headers, qs: e2.qs, body: p2 }, g2 = "x-cos-security-token";
            if (a.isCIHost(d2) && (g2 = "x-ci-security-token"), m2.headers.Authorization = e2.AuthData.Authorization, e2.AuthData.Token && (m2.headers.token = e2.AuthData.Token), e2.AuthData.ClientIP && (m2.headers.clientIP = e2.AuthData.ClientIP), e2.AuthData.ClientUA && (m2.headers.clientUA = e2.AuthData.ClientUA), e2.AuthData.SecurityToken && (m2.headers[g2] = e2.AuthData.SecurityToken), m2.headers && (m2.headers = a.clearKey(m2.headers)), m2 = a.clearKey(m2), e2.onProgress && "function" == typeof e2.onProgress) {
              var y = p2 && (p2.size || p2.length) || 0;
              m2.onProgress = function(t3) {
                if (!n2 || r2._isRunningTask(n2)) {
                  var o2 = t3 ? t3.loaded : 0;
                  e2.onProgress({ loaded: o2, total: y });
                }
              };
            }
            e2.onDownloadProgress && (m2.onDownloadProgress = e2.onDownloadProgress), e2.DataType && (m2.dataType = e2.DataType), this.options.Timeout && (m2.timeout = this.options.Timeout), r2.options.ForcePathStyle && (m2.pathStyle = r2.options.ForcePathStyle), r2.emit("before-send", m2);
            var v, k = m2.url.includes("accelerate."), b = m2.qs ? Object.keys(m2.qs).map(function(e3) {
              return "".concat(e3, "=").concat(m2.qs[e3]);
            }).join("&") : "", C = b ? m2.url + "?" + b : m2.url;
            if (e2.tracker) e2.tracker.setParams({ url: C, httpMethod: m2.method, accelerate: k, httpSize: (null === (v = m2.body) || void 0 === v ? void 0 : v.size) || 0 }), e2.tracker.parent && !e2.tracker.parent.params.url && e2.tracker.parent.setParams({ url: h2, accelerate: k });
            var S = (r2.options.Request || o)(m2, function(o2) {
              if (!o2 || "abort" !== o2.error) {
                var i3 = { options: m2, error: o2 && o2.error, statusCode: o2 && o2.statusCode || 0, statusMessage: o2 && o2.statusMessage || "", headers: o2 && o2.headers || {}, body: o2 && o2.body };
                r2.emit("after-receive", i3);
                var s3, c3 = i3.error, u2 = i3.body, l3 = { statusCode: i3.statusCode, statusMessage: i3.statusMessage, headers: i3.headers }, d3 = function(o3, i4) {
                  if (n2 && r2.off("inner-kill-task", T), !s3) {
                    s3 = true;
                    var c4 = {};
                    if (l3 && l3.statusCode && (c4.statusCode = l3.statusCode), l3 && l3.headers && (c4.headers = l3.headers), o3) m2.url && (c4.url = m2.url), m2.method && (c4.method = m2.method), o3 = a.extend(o3 || {}, c4), t2(o3, null);
                    else {
                      if ("name/cos:PutObject" === e2.Action) {
                        var u3 = {};
                        for (var d4 in e2.headers) {
                          u3[d4.toLowerCase()] = e2.headers[d4];
                        }
                        u3["x-cos-callback"] ? i4.Error ? (i4.CallbackError = a.clone(i4.Error), delete i4.Error) : i4.CallbackBody = a.clone(i4) : u3["x-cos-return-body"] && (i4.Error ? (i4.ReturnError = a.clone(i4.Error), delete i4.Error) : i4.ReturnBody = a.clone(i4));
                      }
                      i4 = a.extend(i4 || {}, c4), t2(null, i4);
                    }
                    S = null;
                  }
                };
                if (c3) return d3(a.error(c3));
                var p3 = l3.statusCode, h3 = 2 === Math.floor(p3 / 100);
                if (f2) {
                  if (h3) return d3(null, { body: u2 });
                  if (u2 instanceof Blob) return void a.readAsBinaryString(u2, function(e3) {
                    var t3 = a.parseResBody(e3), r3 = t3.Error || t3;
                    return d3(a.error(new Error(r3.Message || "response body error"), { code: r3.Code, error: r3 }));
                  });
                }
                var g3 = a.parseResBody(u2), y2 = g3.Error || g3;
                h3 ? d3(null, g3) : y2 ? d3(a.error(new Error(y2.Message), { code: y2.Code, error: y2 })) : p3 ? d3(a.error(new Error(l3.statusMessage), { code: "" + p3 })) : p3 && d3(a.error(new Error("statusCode error")));
              }
            }), T = function e3(t3) {
              t3.TaskId === n2 && (S && S.abort && S.abort(), r2.off("inner-kill-task", e3));
            };
            n2 && r2.on("inner-kill-task", T);
          }
        }
        var g = { getService: function(e2, t2) {
          var r2 = this.options.Protocol || (a.isBrowser && "object" === ("undefined" == typeof location ? "undefined" : n(location)) && "http:" === location.protocol ? "http:" : "https:"), o2 = this.options.ServiceDomain, i2 = e2.AppId || this.options.appId, s2 = e2.Region;
          o2 ? (o2 = o2.replace(/\{\{AppId\}\}/gi, i2 || "").replace(/\{\{Region\}\}/gi, s2 || "").replace(/\{\{.*?\}\}/gi, ""), /^[a-zA-Z]+:\/\//.test(o2) || (o2 = r2 + "//" + o2), "/" === o2.slice(-1) && (o2 = o2.slice(0, -1))) : o2 = s2 ? r2 + "//cos." + s2 + ".myqcloud.com" : r2 + "//service.cos.myqcloud.com";
          var c2 = "", u2 = s2 ? "cos." + s2 + ".myqcloud.com" : "service.cos.myqcloud.com";
          u2 === o2.replace(/^https?:\/\/([^/]+)(\/.*)?$/, "$1") && (c2 = u2), h.call(this, { Action: "name/cos:GetService", url: o2, method: "GET", headers: e2.Headers, SignHost: c2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            var n2 = r3 && r3.ListAllMyBucketsResult && r3.ListAllMyBucketsResult.Buckets && r3.ListAllMyBucketsResult.Buckets.Bucket || [];
            n2 = a.isArray(n2) ? n2 : [n2];
            var o3 = r3 && r3.ListAllMyBucketsResult && r3.ListAllMyBucketsResult.Owner || {};
            t2(null, { Buckets: n2, Owner: o3, statusCode: r3.statusCode, headers: r3.headers });
          });
        }, putBucket: function(e2, t2) {
          var r2 = this, n2 = "";
          if (e2.BucketAZConfig) {
            var o2 = { BucketAZConfig: e2.BucketAZConfig };
            n2 = a.json2xml({ CreateBucketConfiguration: o2 });
          }
          h.call(this, { Action: "name/cos:PutBucket", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, body: n2, tracker: e2.tracker }, function(n3, o3) {
            if (n3) return t2(n3);
            var a2 = u({ protocol: r2.options.Protocol, domain: r2.options.Domain, bucket: e2.Bucket, region: e2.Region, isLocation: true });
            t2(null, { Location: a2, statusCode: o3.statusCode, headers: o3.headers });
          });
        }, headBucket: function(e2, t2) {
          h.call(this, { Action: "name/cos:HeadBucket", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, method: "HEAD", tracker: e2.tracker }, t2);
        }, getBucket: function(e2, t2) {
          var r2 = {};
          r2.prefix = e2.Prefix || "", r2.delimiter = e2.Delimiter, r2.marker = e2.Marker, r2["max-keys"] = e2.MaxKeys, r2["encoding-type"] = e2.EncodingType, h.call(this, { Action: "name/cos:GetBucket", ResourceKey: r2.prefix, method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, qs: r2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            var n2 = r3.ListBucketResult || {}, o2 = n2.Contents || [], i2 = n2.CommonPrefixes || [];
            o2 = a.isArray(o2) ? o2 : [o2], i2 = a.isArray(i2) ? i2 : [i2];
            var s2 = a.clone(n2);
            a.extend(s2, { Contents: o2, CommonPrefixes: i2, statusCode: r3.statusCode, headers: r3.headers }), t2(null, s2);
          });
        }, deleteBucket: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucket", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, method: "DELETE", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketAcl: function(e2, t2) {
          var r2 = e2.Headers, n2 = "";
          if (e2.AccessControlPolicy) {
            var o2 = a.clone(e2.AccessControlPolicy || {}), i2 = o2.Grants || o2.Grant;
            i2 = a.isArray(i2) ? i2 : [i2], delete o2.Grant, delete o2.Grants, o2.AccessControlList = { Grant: i2 }, n2 = a.json2xml({ AccessControlPolicy: o2 }), r2["Content-Type"] = "application/xml", r2["Content-MD5"] = a.b64(a.md5(n2));
          }
          a.each(r2, function(e3, t3) {
            0 === t3.indexOf("x-cos-grant-") && (r2[t3] = c(r2[t3]));
          }), h.call(this, { Action: "name/cos:PutBucketACL", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, headers: r2, action: "acl", body: n2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketAcl: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketACL", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "acl", tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            var n2 = r2.AccessControlPolicy || {}, o2 = n2.Owner || {}, i2 = n2.AccessControlList.Grant || [];
            i2 = a.isArray(i2) ? i2 : [i2];
            var c2 = s(n2);
            r2.headers && r2.headers["x-cos-acl"] && (c2.ACL = r2.headers["x-cos-acl"]), c2 = a.extend(c2, { Owner: o2, Grants: i2, statusCode: r2.statusCode, headers: r2.headers }), t2(null, c2);
          });
        }, putBucketCors: function(e2, t2) {
          var r2 = (e2.CORSConfiguration || {}).CORSRules || e2.CORSRules || [];
          r2 = a.clone(a.isArray(r2) ? r2 : [r2]), a.each(r2, function(e3) {
            a.each(["AllowedOrigin", "AllowedHeader", "AllowedMethod", "ExposeHeader"], function(t3) {
              var r3 = t3 + "s", n3 = e3[r3] || e3[t3] || [];
              delete e3[r3], e3[t3] = a.isArray(n3) ? n3 : [n3];
            });
          });
          var n2 = { CORSRule: r2 };
          e2.ResponseVary && (n2.ResponseVary = e2.ResponseVary);
          var o2 = a.json2xml({ CORSConfiguration: n2 }), i2 = e2.Headers;
          i2["Content-Type"] = "application/xml", i2["Content-MD5"] = a.b64(a.md5(o2)), h.call(this, { Action: "name/cos:PutBucketCORS", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: o2, action: "cors", headers: i2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketCors: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketCORS", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "cors", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 === e3.statusCode && e3.error && "NoSuchCORSConfiguration" === e3.error.Code) {
              var n2 = { CORSRules: [], statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            } else t2(e3);
            else {
              var o2 = r2.CORSConfiguration || {}, i2 = o2.CORSRules || o2.CORSRule || [];
              i2 = a.clone(a.isArray(i2) ? i2 : [i2]);
              var s2 = o2.ResponseVary;
              a.each(i2, function(e4) {
                a.each(["AllowedOrigin", "AllowedHeader", "AllowedMethod", "ExposeHeader"], function(t3) {
                  var r3 = t3 + "s", n3 = e4[r3] || e4[t3] || [];
                  delete e4[t3], e4[r3] = a.isArray(n3) ? n3 : [n3];
                });
              }), t2(null, { CORSRules: i2, ResponseVary: s2, statusCode: r2.statusCode, headers: r2.headers });
            }
          });
        }, deleteBucketCors: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketCORS", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "cors", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode || e3.statusCode, headers: r2.headers });
          });
        }, getBucketLocation: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketLocation", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "location", tracker: e2.tracker }, t2);
        }, getBucketPolicy: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketPolicy", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "policy", rawBody: true, tracker: e2.tracker }, function(e3, r2) {
            if (e3) return e3.statusCode && 403 === e3.statusCode ? t2(a.error(e3, { ErrorStatus: "Access Denied" })) : e3.statusCode && 405 === e3.statusCode ? t2(a.error(e3, { ErrorStatus: "Method Not Allowed" })) : e3.statusCode && 404 === e3.statusCode ? t2(a.error(e3, { ErrorStatus: "Policy Not Found" })) : t2(e3);
            var n2 = {};
            try {
              n2 = JSON.parse(r2.body);
            } catch (e4) {
            }
            t2(null, { Policy: n2, statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketPolicy: function(e2, t2) {
          var r2 = e2.Policy;
          try {
            "string" == typeof r2 && (r2 = JSON.parse(r2));
          } catch (e3) {
          }
          if (!r2 || "string" == typeof r2) return t2(a.error(new Error("Policy format error")));
          var n2 = JSON.stringify(r2);
          r2.version || (r2.version = "2.0");
          var o2 = e2.Headers;
          o2["Content-Type"] = "application/json", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketPolicy", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, action: "policy", body: n2, headers: o2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, deleteBucketPolicy: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketPolicy", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "policy", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode || e3.statusCode, headers: r2.headers });
          });
        }, putBucketTagging: function(e2, t2) {
          var r2 = e2.Tagging || {}, n2 = r2.TagSet || r2.Tags || e2.Tags || [];
          n2 = a.clone(a.isArray(n2) ? n2 : [n2]);
          var o2 = a.json2xml({ Tagging: { TagSet: { Tag: n2 } } }), i2 = e2.Headers;
          i2["Content-Type"] = "application/xml", i2["Content-MD5"] = a.b64(a.md5(o2)), h.call(this, { Action: "name/cos:PutBucketTagging", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: o2, action: "tagging", headers: i2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketTagging: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketTagging", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "tagging", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 !== e3.statusCode || !e3.error || "Not Found" !== e3.error && "NoSuchTagSet" !== e3.error.Code) t2(e3);
            else {
              var n2 = { Tags: [], statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            }
            else {
              var o2 = [];
              try {
                o2 = r2.Tagging.TagSet.Tag || [];
              } catch (e4) {
              }
              o2 = a.clone(a.isArray(o2) ? o2 : [o2]), t2(null, { Tags: o2, statusCode: r2.statusCode, headers: r2.headers });
            }
          });
        }, deleteBucketTagging: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketTagging", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "tagging", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketLifecycle: function(e2, t2) {
          var r2 = (e2.LifecycleConfiguration || {}).Rules || e2.Rules || [];
          r2 = a.clone(r2);
          var n2 = a.json2xml({ LifecycleConfiguration: { Rule: r2 } }), o2 = e2.Headers;
          o2["Content-Type"] = "application/xml", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketLifecycle", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: n2, action: "lifecycle", headers: o2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketLifecycle: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketLifecycle", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "lifecycle", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 === e3.statusCode && e3.error && "NoSuchLifecycleConfiguration" === e3.error.Code) {
              var n2 = { Rules: [], statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            } else t2(e3);
            else {
              var o2 = [];
              try {
                o2 = r2.LifecycleConfiguration.Rule || [];
              } catch (e4) {
              }
              o2 = a.clone(a.isArray(o2) ? o2 : [o2]), t2(null, { Rules: o2, statusCode: r2.statusCode, headers: r2.headers });
            }
          });
        }, deleteBucketLifecycle: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketLifecycle", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "lifecycle", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketVersioning: function(e2, t2) {
          if (e2.VersioningConfiguration) {
            var r2 = e2.VersioningConfiguration || {}, n2 = a.json2xml({ VersioningConfiguration: r2 }), o2 = e2.Headers;
            o2["Content-Type"] = "application/xml", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketVersioning", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: n2, action: "versioning", headers: o2, tracker: e2.tracker }, function(e3, r3) {
              return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
            });
          } else t2(a.error(new Error("missing param VersioningConfiguration")));
        }, getBucketVersioning: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketVersioning", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "versioning", tracker: e2.tracker }, function(e3, r2) {
            e3 || !r2.VersioningConfiguration && (r2.VersioningConfiguration = {}), t2(e3, r2);
          });
        }, putBucketReplication: function(e2, t2) {
          var r2 = a.clone(e2.ReplicationConfiguration), n2 = a.json2xml({ ReplicationConfiguration: r2 });
          n2 = (n2 = n2.replace(/<(\/?)Rules>/gi, "<$1Rule>")).replace(/<(\/?)Tags>/gi, "<$1Tag>");
          var o2 = e2.Headers;
          o2["Content-Type"] = "application/xml", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketReplication", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: n2, action: "replication", headers: o2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketReplication: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketReplication", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "replication", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 !== e3.statusCode || !e3.error || "Not Found" !== e3.error && "ReplicationConfigurationnotFoundError" !== e3.error.Code) t2(e3);
            else {
              var n2 = { ReplicationConfiguration: { Rules: [] }, statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            }
            else !r2.ReplicationConfiguration && (r2.ReplicationConfiguration = {}), r2.ReplicationConfiguration.Rule && (r2.ReplicationConfiguration.Rules = a.makeArray(r2.ReplicationConfiguration.Rule), delete r2.ReplicationConfiguration.Rule), t2(e3, r2);
          });
        }, deleteBucketReplication: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketReplication", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "replication", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketWebsite: function(e2, t2) {
          if (e2.WebsiteConfiguration) {
            var r2 = a.clone(e2.WebsiteConfiguration || {}), n2 = r2.RoutingRules || r2.RoutingRule || [];
            n2 = a.isArray(n2) ? n2 : [n2], delete r2.RoutingRule, delete r2.RoutingRules, n2.length && (r2.RoutingRules = { RoutingRule: n2 });
            var o2 = a.json2xml({ WebsiteConfiguration: r2 }), i2 = e2.Headers;
            i2["Content-Type"] = "application/xml", i2["Content-MD5"] = a.b64(a.md5(o2)), h.call(this, { Action: "name/cos:PutBucketWebsite", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: o2, action: "website", headers: i2, tracker: e2.tracker }, function(e3, r3) {
              return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
            });
          } else t2(a.error(new Error("missing param WebsiteConfiguration")));
        }, getBucketWebsite: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketWebsite", method: "GET", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, action: "website", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 === e3.statusCode && "NoSuchWebsiteConfiguration" === e3.error.Code) {
              var n2 = { WebsiteConfiguration: {}, statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            } else t2(e3);
            else {
              var o2 = r2.WebsiteConfiguration || {};
              if (o2.RoutingRules) {
                var i2 = a.clone(o2.RoutingRules.RoutingRule || []);
                i2 = a.makeArray(i2), o2.RoutingRules = i2;
              }
              t2(null, { WebsiteConfiguration: o2, statusCode: r2.statusCode, headers: r2.headers });
            }
          });
        }, deleteBucketWebsite: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketWebsite", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "website", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketReferer: function(e2, t2) {
          if (e2.RefererConfiguration) {
            var r2 = a.clone(e2.RefererConfiguration || {}), n2 = r2.DomainList || {}, o2 = n2.Domains || n2.Domain || [];
            (o2 = a.isArray(o2) ? o2 : [o2]).length && (r2.DomainList = { Domain: o2 });
            var i2 = a.json2xml({ RefererConfiguration: r2 }), s2 = e2.Headers;
            s2["Content-Type"] = "application/xml", s2["Content-MD5"] = a.b64(a.md5(i2)), h.call(this, { Action: "name/cos:PutBucketReferer", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: i2, action: "referer", headers: s2, tracker: e2.tracker }, function(e3, r3) {
              return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
            });
          } else t2(a.error(new Error("missing param RefererConfiguration")));
        }, getBucketReferer: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketReferer", method: "GET", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, action: "referer", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 === e3.statusCode && "NoSuchRefererConfiguration" === e3.error.Code) {
              var n2 = { WebsiteConfiguration: {}, statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            } else t2(e3);
            else {
              var o2 = r2.RefererConfiguration || {};
              if (o2.DomainList) {
                var i2 = a.makeArray(o2.DomainList.Domain || []);
                o2.DomainList = { Domains: i2 };
              }
              t2(null, { RefererConfiguration: o2, statusCode: r2.statusCode, headers: r2.headers });
            }
          });
        }, putBucketDomain: function(e2, t2) {
          var r2 = (e2.DomainConfiguration || {}).DomainRule || e2.DomainRule || [];
          r2 = a.clone(r2);
          var n2 = a.json2xml({ DomainConfiguration: { DomainRule: r2 } }), o2 = e2.Headers;
          o2["Content-Type"] = "application/xml", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketDomain", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: n2, action: "domain", headers: o2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketDomain: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketDomain", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "domain", tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            var n2 = [];
            try {
              n2 = r2.DomainConfiguration.DomainRule || [];
            } catch (e4) {
            }
            n2 = a.clone(a.isArray(n2) ? n2 : [n2]), t2(null, { DomainRule: n2, statusCode: r2.statusCode, headers: r2.headers });
          });
        }, deleteBucketDomain: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketDomain", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "domain", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketOrigin: function(e2, t2) {
          var r2 = (e2.OriginConfiguration || {}).OriginRule || e2.OriginRule || [];
          r2 = a.clone(r2);
          var n2 = a.json2xml({ OriginConfiguration: { OriginRule: r2 } }), o2 = e2.Headers;
          o2["Content-Type"] = "application/xml", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketOrigin", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: n2, action: "origin", headers: o2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketOrigin: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketOrigin", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "origin", tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            var n2 = [];
            try {
              n2 = r2.OriginConfiguration.OriginRule || [];
            } catch (e4) {
            }
            n2 = a.clone(a.isArray(n2) ? n2 : [n2]), t2(null, { OriginRule: n2, statusCode: r2.statusCode, headers: r2.headers });
          });
        }, deleteBucketOrigin: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketOrigin", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "origin", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketLogging: function(e2, t2) {
          var r2 = a.json2xml({ BucketLoggingStatus: e2.BucketLoggingStatus || "" }), n2 = e2.Headers;
          n2["Content-Type"] = "application/xml", n2["Content-MD5"] = a.b64(a.md5(r2)), h.call(this, { Action: "name/cos:PutBucketLogging", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: r2, action: "logging", headers: n2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketLogging: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketLogging", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "logging", tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            t2(null, { BucketLoggingStatus: r2.BucketLoggingStatus, statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketInventory: function(e2, t2) {
          return i.call(this, "PUT", e2, t2);
        }, postBucketInventory: function(e2, t2) {
          return i.call(this, "POST", e2, t2);
        }, getBucketInventory: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketInventory", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "inventory", qs: { id: e2.Id }, tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            var n2 = r2.InventoryConfiguration;
            if (n2 && n2.OptionalFields && n2.OptionalFields.Field) {
              var o2 = n2.OptionalFields.Field;
              a.isArray(o2) || (o2 = [o2]), n2.OptionalFields = o2;
            }
            if (n2.Destination && n2.Destination.COSBucketDestination && n2.Destination.COSBucketDestination.Encryption) {
              var i2 = n2.Destination.COSBucketDestination.Encryption;
              Object.keys(i2).indexOf("SSE-COS") > -1 && (i2.SSECOS = i2["SSE-COS"], delete i2["SSE-COS"]);
            }
            t2(null, { InventoryConfiguration: n2, statusCode: r2.statusCode, headers: r2.headers });
          });
        }, listBucketInventory: function(e2, t2) {
          h.call(this, { Action: "name/cos:ListBucketInventory", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "inventory", qs: { "continuation-token": e2.ContinuationToken }, tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            var n2 = r2.ListInventoryConfigurationResult, o2 = n2.InventoryConfiguration || [];
            o2 = a.isArray(o2) ? o2 : [o2], delete n2.InventoryConfiguration, a.each(o2, function(e4) {
              if (e4 && e4.OptionalFields && e4.OptionalFields.Field) {
                var t3 = e4.OptionalFields.Field;
                a.isArray(t3) || (t3 = [t3]), e4.OptionalFields = t3;
              }
              if (e4.Destination && e4.Destination.COSBucketDestination && e4.Destination.COSBucketDestination.Encryption) {
                var r3 = e4.Destination.COSBucketDestination.Encryption;
                Object.keys(r3).indexOf("SSE-COS") > -1 && (r3.SSECOS = r3["SSE-COS"], delete r3["SSE-COS"]);
              }
            }), n2.InventoryConfigurations = o2, a.extend(n2, { statusCode: r2.statusCode, headers: r2.headers }), t2(null, n2);
          });
        }, deleteBucketInventory: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketInventory", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "inventory", qs: { id: e2.Id }, tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, putBucketAccelerate: function(e2, t2) {
          if (e2.AccelerateConfiguration) {
            var r2 = { AccelerateConfiguration: e2.AccelerateConfiguration || {} }, n2 = a.json2xml(r2), o2 = { "Content-Type": "application/xml" };
            o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:PutBucketAccelerate", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: n2, action: "accelerate", headers: o2, tracker: e2.tracker }, function(e3, r3) {
              if (e3) return t2(e3);
              t2(null, { statusCode: r3.statusCode, headers: r3.headers });
            });
          } else t2(a.error(new Error("missing param AccelerateConfiguration")));
        }, getBucketAccelerate: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketAccelerate", method: "GET", Bucket: e2.Bucket, Region: e2.Region, action: "accelerate", tracker: e2.tracker }, function(e3, r2) {
            e3 || !r2.AccelerateConfiguration && (r2.AccelerateConfiguration = {}), t2(e3, r2);
          });
        }, putBucketEncryption: function(e2, t2) {
          var r2 = e2.ServerSideEncryptionConfiguration || {}, n2 = r2.Rule || r2.Rules || [], o2 = a.json2xml({ ServerSideEncryptionConfiguration: { Rule: n2 } }), i2 = e2.Headers;
          i2["Content-Type"] = "application/xml", i2["Content-MD5"] = a.b64(a.md5(o2)), h.call(this, { Action: "name/cos:PutBucketEncryption", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, body: o2, action: "encryption", headers: i2, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getBucketEncryption: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetBucketEncryption", method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "encryption", tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 === e3.statusCode && "NoSuchEncryptionConfiguration" === e3.code) {
              var n2 = { EncryptionConfiguration: { Rules: [] }, statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            } else t2(e3);
            else {
              var o2 = a.makeArray(r2.EncryptionConfiguration && r2.EncryptionConfiguration.Rule || []);
              r2.EncryptionConfiguration = { Rules: o2 }, t2(e3, r2);
            }
          });
        }, deleteBucketEncryption: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteBucketReplication", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "encryption", tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, getObject: function(e2, t2) {
          if (this.options.ObjectKeySimplifyCheck && "/" === a.simplifyPath(e2.Key)) return void t2(a.error(new Error("The Getobject Key is illegal")));
          var r2 = e2.Query || {}, n2 = e2.QueryString || "", o2 = a.throttleOnProgress.call(this, 0, e2.onProgress), i2 = e2.tracker;
          i2 && i2.setParams({ signStartTime: (/* @__PURE__ */ new Date()).getTime() }), r2["response-content-type"] = e2.ResponseContentType, r2["response-content-language"] = e2.ResponseContentLanguage, r2["response-expires"] = e2.ResponseExpires, r2["response-cache-control"] = e2.ResponseCacheControl, r2["response-content-disposition"] = e2.ResponseContentDisposition, r2["response-content-encoding"] = e2.ResponseContentEncoding, h.call(this, { Action: "name/cos:GetObject", method: "GET", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, VersionId: e2.VersionId, DataType: e2.DataType, headers: e2.Headers, qs: r2, qsStr: n2, rawBody: true, onDownloadProgress: o2, tracker: i2 }, function(r3, n3) {
            if (o2(null, true), r3) {
              var i3 = r3.statusCode;
              return e2.Headers["If-Modified-Since"] && i3 && 304 === i3 ? t2(null, { NotModified: true }) : t2(r3);
            }
            t2(null, { Body: n3.body, ETag: a.attr(n3.headers, "etag", ""), statusCode: n3.statusCode, headers: n3.headers });
          });
        }, headObject: function(e2, t2) {
          h.call(this, { Action: "name/cos:HeadObject", method: "HEAD", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, VersionId: e2.VersionId, headers: e2.Headers, tracker: e2.tracker }, function(r2, n2) {
            if (r2) {
              var o2 = r2.statusCode;
              return e2.Headers["If-Modified-Since"] && o2 && 304 === o2 ? t2(null, { NotModified: true, statusCode: o2 }) : t2(r2);
            }
            n2.ETag = a.attr(n2.headers, "etag", ""), t2(null, n2);
          });
        }, listObjectVersions: function(e2, t2) {
          var r2 = {};
          r2.prefix = e2.Prefix || "", r2.delimiter = e2.Delimiter, r2["key-marker"] = e2.KeyMarker, r2["version-id-marker"] = e2.VersionIdMarker, r2["max-keys"] = e2.MaxKeys, r2["encoding-type"] = e2.EncodingType, h.call(this, { Action: "name/cos:GetBucketObjectVersions", ResourceKey: r2.prefix, method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, qs: r2, action: "versions", tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            var n2 = r3.ListVersionsResult || {}, o2 = n2.DeleteMarker || [];
            o2 = a.isArray(o2) ? o2 : [o2];
            var i2 = n2.Version || [];
            i2 = a.isArray(i2) ? i2 : [i2];
            var s2 = a.clone(n2);
            delete s2.DeleteMarker, delete s2.Version, a.extend(s2, { DeleteMarkers: o2, Versions: i2, statusCode: r3.statusCode, headers: r3.headers }), t2(null, s2);
          });
        }, putObject: function(e2, t2) {
          var r2 = this, n2 = e2.ContentLength, o2 = a.throttleOnProgress.call(r2, n2, e2.onProgress), i2 = e2.Headers;
          i2["Cache-Control"] || i2["cache-control"] || (i2["Cache-Control"] = ""), i2["Content-Type"] || i2["content-type"] || (i2["Content-Type"] = e2.Body && e2.Body.type || "");
          var s2 = e2.UploadAddMetaMd5 || r2.options.UploadAddMetaMd5 || r2.options.UploadCheckContentMd5, c2 = e2.tracker;
          s2 && c2 && c2.setParams({ md5StartTime: (/* @__PURE__ */ new Date()).getTime() }), a.getBodyMd5(s2, e2.Body, function(s3) {
            s3 && (c2 && c2.setParams({ md5EndTime: (/* @__PURE__ */ new Date()).getTime() }), r2.options.UploadCheckContentMd5 && (i2["Content-MD5"] = a.b64(s3)), (e2.UploadAddMetaMd5 || r2.options.UploadAddMetaMd5) && (i2["x-cos-meta-md5"] = s3)), void 0 !== e2.ContentLength && (i2["Content-Length"] = e2.ContentLength), o2(null, true), h.call(r2, { Action: "name/cos:PutObject", TaskId: e2.TaskId, method: "PUT", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, qs: e2.Query, body: e2.Body, onProgress: o2, tracker: c2 }, function(i3, s4) {
              if (i3) return o2(null, true), t2(i3);
              o2({ loaded: n2, total: n2 }, true);
              var c3 = u({ ForcePathStyle: r2.options.ForcePathStyle, protocol: r2.options.Protocol, domain: r2.options.Domain, bucket: e2.Bucket, region: r2.options.UseAccelerate ? "accelerate" : e2.Region, object: e2.Key });
              c3 = c3.substr(c3.indexOf("://") + 3), s4.Location = c3, s4.ETag = a.attr(s4.headers, "etag", ""), t2(null, s4);
            });
          }, e2.onHashProgress);
        }, deleteObject: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteObject", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, VersionId: e2.VersionId, action: e2.Recursive ? "recursive" : "", tracker: e2.tracker }, function(e3, r2) {
            if (e3) {
              var n2 = e3.statusCode;
              return n2 && 404 === n2 ? t2(null, { BucketNotFound: true, statusCode: n2 }) : t2(e3);
            }
            t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, getObjectAcl: function(e2, t2) {
          var r2 = {};
          e2.VersionId && (r2.versionId = e2.VersionId), h.call(this, { Action: "name/cos:GetObjectACL", method: "GET", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, qs: r2, action: "acl", tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            var n2 = r3.AccessControlPolicy || {}, o2 = n2.Owner || {}, i2 = n2.AccessControlList && n2.AccessControlList.Grant || [];
            i2 = a.isArray(i2) ? i2 : [i2];
            var c2 = s(n2);
            delete c2.GrantWrite, r3.headers && r3.headers["x-cos-acl"] && (c2.ACL = r3.headers["x-cos-acl"]), c2 = a.extend(c2, { Owner: o2, Grants: i2, statusCode: r3.statusCode, headers: r3.headers }), t2(null, c2);
          });
        }, putObjectAcl: function(e2, t2) {
          var r2 = e2.Headers, n2 = "";
          if (e2.AccessControlPolicy) {
            var o2 = a.clone(e2.AccessControlPolicy || {}), i2 = o2.Grants || o2.Grant;
            i2 = a.isArray(i2) ? i2 : [i2], delete o2.Grant, delete o2.Grants, o2.AccessControlList = { Grant: i2 }, n2 = a.json2xml({ AccessControlPolicy: o2 }), r2["Content-Type"] = "application/xml", r2["Content-MD5"] = a.b64(a.md5(n2));
          }
          a.each(r2, function(e3, t3) {
            0 === t3.indexOf("x-cos-grant-") && (r2[t3] = c(r2[t3]));
          }), h.call(this, { Action: "name/cos:PutObjectACL", method: "PUT", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, action: "acl", headers: r2, body: n2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, optionsObject: function(e2, t2) {
          var r2 = e2.Headers;
          r2.Origin = e2.Origin, r2["Access-Control-Request-Method"] = e2.AccessControlRequestMethod, r2["Access-Control-Request-Headers"] = e2.AccessControlRequestHeaders, h.call(this, { Action: "name/cos:OptionsObject", method: "OPTIONS", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: r2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return e3.statusCode && 403 === e3.statusCode ? t2(null, { OptionsForbidden: true, statusCode: e3.statusCode }) : t2(e3);
            var n2 = r3.headers || {};
            t2(null, { AccessControlAllowOrigin: n2["access-control-allow-origin"], AccessControlAllowMethods: n2["access-control-allow-methods"], AccessControlAllowHeaders: n2["access-control-allow-headers"], AccessControlExposeHeaders: n2["access-control-expose-headers"], AccessControlMaxAge: n2["access-control-max-age"], statusCode: r3.statusCode, headers: r3.headers });
          });
        }, putObjectCopy: function(e2, t2) {
          var r2 = this, n2 = e2.Headers;
          n2["Cache-Control"] || n2["cache-control"] || (n2["Cache-Control"] = "");
          var o2 = e2.CopySource || "", i2 = a.getSourceParams.call(this, o2);
          if (i2) {
            var s2 = i2.Bucket, c2 = i2.Region, l2 = decodeURIComponent(i2.Key);
            h.call(this, { Scope: [{ action: "name/cos:GetObject", bucket: s2, region: c2, prefix: l2 }, { action: "name/cos:PutObject", bucket: e2.Bucket, region: e2.Region, prefix: e2.Key }], method: "PUT", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, VersionId: e2.VersionId, headers: e2.Headers, tracker: e2.tracker }, function(n3, o3) {
              if (n3) return t2(n3);
              var i3 = a.clone(o3.CopyObjectResult || {}), s3 = u({ ForcePathStyle: r2.options.ForcePathStyle, protocol: r2.options.Protocol, domain: r2.options.Domain, bucket: e2.Bucket, region: e2.Region, object: e2.Key, isLocation: true });
              a.extend(i3, { Location: s3, statusCode: o3.statusCode, headers: o3.headers }), t2(null, i3);
            });
          } else t2(a.error(new Error("CopySource format error")));
        }, deleteMultipleObject: function(e2, t2) {
          var r2 = e2.Objects || [], n2 = e2.Quiet;
          r2 = a.isArray(r2) ? r2 : [r2];
          var o2 = a.json2xml({ Delete: { Object: r2, Quiet: n2 || false } }), i2 = e2.Headers;
          i2["Content-Type"] = "application/xml", i2["Content-MD5"] = a.b64(a.md5(o2));
          var s2 = a.map(r2, function(t3) {
            return { action: "name/cos:DeleteObject", bucket: e2.Bucket, region: e2.Region, prefix: t3.Key };
          });
          h.call(this, { Scope: s2, method: "POST", Bucket: e2.Bucket, Region: e2.Region, body: o2, action: "delete", headers: i2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            var n3 = r3.DeleteResult || {}, o3 = n3.Deleted || [], i3 = n3.Error || [];
            o3 = a.isArray(o3) ? o3 : [o3], i3 = a.isArray(i3) ? i3 : [i3];
            var s3 = a.clone(n3);
            a.extend(s3, { Error: i3, Deleted: o3, statusCode: r3.statusCode, headers: r3.headers }), t2(null, s3);
          });
        }, restoreObject: function(e2, t2) {
          var r2 = e2.Headers;
          if (e2.RestoreRequest) {
            var n2 = e2.RestoreRequest || {}, o2 = a.json2xml({ RestoreRequest: n2 });
            r2["Content-Type"] = "application/xml", r2["Content-MD5"] = a.b64(a.md5(o2)), h.call(this, { Action: "name/cos:RestoreObject", method: "POST", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, VersionId: e2.VersionId, body: o2, action: "restore", headers: r2, tracker: e2.tracker }, t2);
          } else t2(a.error(new Error("missing param RestoreRequest")));
        }, putObjectTagging: function(e2, t2) {
          var r2 = e2.Tagging || {}, n2 = r2.TagSet || r2.Tags || e2.Tags || [];
          n2 = a.clone(a.isArray(n2) ? n2 : [n2]);
          var o2 = a.json2xml({ Tagging: { TagSet: { Tag: n2 } } }), i2 = e2.Headers;
          i2["Content-Type"] = "application/xml", i2["Content-MD5"] = a.b64(a.md5(o2)), h.call(this, { Action: "name/cos:PutObjectTagging", method: "PUT", Bucket: e2.Bucket, Key: e2.Key, Region: e2.Region, body: o2, action: "tagging", headers: i2, VersionId: e2.VersionId, tracker: e2.tracker }, function(e3, r3) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, getObjectTagging: function(e2, t2) {
          h.call(this, { Action: "name/cos:GetObjectTagging", method: "GET", Key: e2.Key, Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, action: "tagging", VersionId: e2.VersionId, tracker: e2.tracker }, function(e3, r2) {
            if (e3) if (404 !== e3.statusCode || !e3.error || "Not Found" !== e3.error && "NoSuchTagSet" !== e3.error.Code) t2(e3);
            else {
              var n2 = { Tags: [], statusCode: e3.statusCode };
              e3.headers && (n2.headers = e3.headers), t2(null, n2);
            }
            else {
              var o2 = [];
              try {
                o2 = r2.Tagging.TagSet.Tag || [];
              } catch (e4) {
              }
              o2 = a.clone(a.isArray(o2) ? o2 : [o2]), t2(null, { Tags: o2, statusCode: r2.statusCode, headers: r2.headers });
            }
          });
        }, deleteObjectTagging: function(e2, t2) {
          h.call(this, { Action: "name/cos:DeleteObjectTagging", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, action: "tagging", VersionId: e2.VersionId, tracker: e2.tracker }, function(e3, r2) {
            return e3 && 204 === e3.statusCode ? t2(null, { statusCode: e3.statusCode }) : e3 ? t2(e3) : void t2(null, { statusCode: r2.statusCode, headers: r2.headers });
          });
        }, selectObjectContent: function(e2, t2) {
          if (!e2.SelectType) return t2(a.error(new Error("missing param SelectType")));
          var r2 = e2.SelectRequest || {}, n2 = a.json2xml({ SelectRequest: r2 }), o2 = e2.Headers;
          o2["Content-Type"] = "application/xml", o2["Content-MD5"] = a.b64(a.md5(n2)), h.call(this, { Action: "name/cos:GetObject", method: "POST", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, action: "select", qs: { "select-type": e2.SelectType }, VersionId: e2.VersionId, body: n2, DataType: "arraybuffer", rawBody: true, tracker: e2.tracker }, function(e3, r3) {
            if (e3 && 204 === e3.statusCode) return t2(null, { statusCode: e3.statusCode });
            if (e3) return t2(e3);
            var n3 = a.parseSelectPayload(r3.body);
            t2(null, { statusCode: r3.statusCode, headers: r3.headers, Body: n3.body, Payload: n3.payload });
          });
        }, appendObject: function(e2, t2) {
          var r2 = e2.Headers;
          r2["Cache-Control"] || r2["cache-control"] || (r2["Cache-Control"] = ""), r2["Content-Type"] || r2["content-type"] || (r2["Content-Type"] = e2.Body && e2.Body.type || ""), h.call(this, { Action: "name/cos:AppendObject", method: "POST", Bucket: e2.Bucket, Region: e2.Region, action: "append", Key: e2.Key, body: e2.Body, qs: { position: e2.Position }, headers: e2.Headers, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            t2(null, r3);
          });
        }, uploadPartCopy: function(e2, t2) {
          var r2 = e2.CopySource || "", n2 = a.getSourceParams.call(this, r2);
          if (n2) {
            var o2 = n2.Bucket, i2 = n2.Region, s2 = decodeURIComponent(n2.Key);
            h.call(this, { Scope: [{ action: "name/cos:GetObject", bucket: o2, region: i2, prefix: s2 }, { action: "name/cos:PutObject", bucket: e2.Bucket, region: e2.Region, prefix: e2.Key }], method: "PUT", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, VersionId: e2.VersionId, qs: { partNumber: e2.PartNumber, uploadId: e2.UploadId }, headers: e2.Headers, tracker: e2.tracker }, function(e3, r3) {
              if (e3) return t2(e3);
              var n3 = a.clone(r3.CopyPartResult || {});
              a.extend(n3, { statusCode: r3.statusCode, headers: r3.headers }), t2(null, n3);
            });
          } else t2(a.error(new Error("CopySource format error")));
        }, multipartInit: function(e2, t2) {
          var r2 = this, n2 = e2.Headers, o2 = e2.tracker;
          n2["Cache-Control"] || n2["cache-control"] || (n2["Cache-Control"] = ""), n2["Content-Type"] || n2["content-type"] || (n2["Content-Type"] = e2.Body && e2.Body.type || "");
          var i2 = e2.Body && (e2.UploadAddMetaMd5 || r2.options.UploadAddMetaMd5);
          i2 && o2 && o2.setParams({ md5StartTime: (/* @__PURE__ */ new Date()).getTime() }), a.getBodyMd5(i2, e2.Body, function(n3) {
            n3 && (e2.Headers["x-cos-meta-md5"] = n3), i2 && o2 && o2.setParams({ md5EndTime: (/* @__PURE__ */ new Date()).getTime() }), h.call(r2, { Action: "name/cos:InitiateMultipartUpload", method: "POST", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, action: "uploads", headers: e2.Headers, qs: e2.Query, tracker: o2 }, function(e3, r3) {
              return e3 ? (o2 && o2.parent && o2.parent.setParams({ errorNode: "multipartInit" }), t2(e3)) : (r3 = a.clone(r3 || {})) && r3.InitiateMultipartUploadResult ? t2(null, a.extend(r3.InitiateMultipartUploadResult, { statusCode: r3.statusCode, headers: r3.headers })) : void t2(null, r3);
            });
          }, e2.onHashProgress);
        }, multipartUpload: function(e2, t2) {
          var r2 = this;
          a.getFileSize("multipartUpload", e2, function() {
            var n2 = e2.tracker, o2 = r2.options.UploadCheckContentMd5;
            o2 && n2 && n2.setParams({ md5StartTime: (/* @__PURE__ */ new Date()).getTime() }), a.getBodyMd5(o2, e2.Body, function(i2) {
              i2 && (e2.Headers["Content-MD5"] = a.b64(i2)), o2 && n2 && n2.setParams({ md5EndTime: (/* @__PURE__ */ new Date()).getTime() }), n2 && n2.setParams({ partNumber: e2.PartNumber }), h.call(r2, { Action: "name/cos:UploadPart", TaskId: e2.TaskId, method: "PUT", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, qs: { partNumber: e2.PartNumber, uploadId: e2.UploadId }, headers: e2.Headers, onProgress: e2.onProgress, body: e2.Body || null, tracker: n2 }, function(e3, r3) {
                if (e3) return n2 && n2.parent && n2.parent.setParams({ errorNode: "multipartUpload" }), t2(e3);
                t2(null, { ETag: a.attr(r3.headers, "etag", ""), statusCode: r3.statusCode, headers: r3.headers });
              });
            });
          });
        }, multipartComplete: function(e2, t2) {
          for (var r2 = this, n2 = e2.UploadId, o2 = e2.Parts, i2 = e2.tracker, s2 = 0, c2 = o2.length; s2 < c2; s2++) o2[s2].ETag && 0 === o2[s2].ETag.indexOf('"') || (o2[s2].ETag = '"' + o2[s2].ETag + '"');
          var l2 = a.json2xml({ CompleteMultipartUpload: { Part: o2 } });
          l2 = l2.replace(/\n\s*/g, "");
          var d2 = e2.Headers;
          d2["Content-Type"] = "application/xml", d2["Content-MD5"] = a.b64(a.md5(l2)), h.call(this, { Action: "name/cos:CompleteMultipartUpload", method: "POST", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, qs: { uploadId: n2 }, body: l2, headers: d2, tracker: i2 }, function(n3, o3) {
            if (n3) return i2 && i2.parent && i2.parent.setParams({ errorNode: "multipartComplete" }), t2(n3);
            var s3 = u({ ForcePathStyle: r2.options.ForcePathStyle, protocol: r2.options.Protocol, domain: r2.options.Domain, bucket: e2.Bucket, region: e2.Region, object: e2.Key, isLocation: true }), c3 = o3.CompleteMultipartUploadResult || {};
            if (c3.ProcessResults && (c3.UploadResult = { OriginalInfo: { Key: c3.Key, Location: s3, ETag: c3.ETag, ImageInfo: c3.ImageInfo }, ProcessResults: c3.ProcessResults }, delete c3.ImageInfo, delete c3.ProcessResults), c3.CallbackResult) {
              var l3 = c3.CallbackResult;
              if ("200" === l3.Status && l3.CallbackBody) try {
                c3.CallbackBody = JSON.parse(a.decodeBase64(l3.CallbackBody));
              } catch (e3) {
                c3.CallbackBody = {};
              }
              else c3.CallbackError = l3.Error || {};
              delete c3.CallbackResult;
            }
            if (c3.ReturnBodyResult) {
              var d3 = c3.ReturnBodyResult;
              if ("200" === d3.Status && d3.ReturnBody) try {
                c3.ReturnBody = JSON.parse(a.decodeBase64(d3.ReturnBody));
              } catch (e3) {
                c3.ReturnBody = {};
              }
              else c3.ReturnError = { Code: d3.Code, Message: d3.Message, Status: d3.Status };
              delete c3.ReturnBodyResult;
            }
            var p2 = a.extend(c3, { Location: s3, statusCode: o3.statusCode, headers: o3.headers });
            t2(null, p2);
          });
        }, multipartList: function(e2, t2) {
          var r2 = {};
          r2.delimiter = e2.Delimiter, r2["encoding-type"] = e2.EncodingType, r2.prefix = e2.Prefix || "", r2["max-uploads"] = e2.MaxUploads, r2["key-marker"] = e2.KeyMarker, r2["upload-id-marker"] = e2.UploadIdMarker, r2 = a.clearKey(r2);
          var n2 = e2.tracker;
          n2 && n2.setParams({ signStartTime: (/* @__PURE__ */ new Date()).getTime() }), h.call(this, { Action: "name/cos:ListMultipartUploads", ResourceKey: r2.prefix, method: "GET", Bucket: e2.Bucket, Region: e2.Region, headers: e2.Headers, qs: r2, action: "uploads", tracker: n2 }, function(e3, r3) {
            if (e3) return n2 && n2.parent && n2.parent.setParams({ errorNode: "multipartList" }), t2(e3);
            if (r3 && r3.ListMultipartUploadsResult) {
              var o2 = r3.ListMultipartUploadsResult.Upload || [];
              o2 = a.isArray(o2) ? o2 : [o2], r3.ListMultipartUploadsResult.Upload = o2;
            }
            var i2 = a.clone(r3.ListMultipartUploadsResult || {});
            a.extend(i2, { statusCode: r3.statusCode, headers: r3.headers }), t2(null, i2);
          });
        }, multipartListPart: function(e2, t2) {
          var r2 = {}, n2 = e2.tracker;
          r2.uploadId = e2.UploadId, r2["encoding-type"] = e2.EncodingType, r2["max-parts"] = e2.MaxParts, r2["part-number-marker"] = e2.PartNumberMarker, h.call(this, { Action: "name/cos:ListParts", method: "GET", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, qs: r2, tracker: n2 }, function(e3, r3) {
            if (e3) return n2 && n2.parent && n2.parent.setParams({ errorNode: "multipartListPart" }), t2(e3);
            var o2 = r3.ListPartsResult || {}, i2 = o2.Part || [];
            i2 = a.isArray(i2) ? i2 : [i2], o2.Part = i2;
            var s2 = a.clone(o2);
            a.extend(s2, { statusCode: r3.statusCode, headers: r3.headers }), t2(null, s2);
          });
        }, multipartAbort: function(e2, t2) {
          var r2 = {};
          r2.uploadId = e2.UploadId, h.call(this, { Action: "name/cos:AbortMultipartUpload", method: "DELETE", Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, headers: e2.Headers, qs: r2, tracker: e2.tracker }, function(e3, r3) {
            if (e3) return t2(e3);
            t2(null, { statusCode: r3.statusCode, headers: r3.headers });
          });
        }, request: function(e2, t2) {
          h.call(this, { method: e2.Method, Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, action: e2.Action, headers: e2.Headers, qs: e2.Query, body: e2.Body, Url: e2.Url, rawBody: e2.RawBody, DataType: e2.DataType, tracker: e2.tracker }, function(e3, r2) {
            if (e3) return t2(e3);
            r2 && r2.body && (r2.Body = r2.body, delete r2.body), t2(e3, r2);
          });
        }, getObjectUrl: function(e2, t2) {
          var r2 = this, n2 = void 0 === e2.UseAccelerate ? r2.options.UseAccelerate : e2.UseAccelerate, o2 = u({ ForcePathStyle: r2.options.ForcePathStyle, protocol: e2.Protocol || r2.options.Protocol, domain: e2.Domain || r2.options.Domain, bucket: e2.Bucket, region: n2 ? "accelerate" : e2.Region, object: e2.Key }), i2 = "";
          e2.Query && (i2 += a.obj2str(e2.Query)), e2.QueryString && (i2 += (i2 ? "&" : "") + e2.QueryString);
          var s2 = o2;
          if (void 0 !== e2.Sign && !e2.Sign) return i2 && (s2 += "?" + i2), t2(null, { Url: s2 }), s2;
          var c2 = l.call(this, { Bucket: e2.Bucket, Region: e2.Region, UseAccelerate: e2.UseAccelerate, Url: o2 }), p2 = d.call(this, { Action: "PUT" === (e2.Method || "").toUpperCase() ? "name/cos:PutObject" : "name/cos:GetObject", Bucket: e2.Bucket || "", Region: e2.Region || "", Method: e2.Method || "get", Key: e2.Key, Expires: e2.Expires, Headers: e2.Headers, Query: e2.Query, SignHost: c2, ForceSignHost: false !== e2.ForceSignHost && r2.options.ForceSignHost }, function(e3, r3) {
            if (t2) if (e3) t2(e3);
            else {
              var n3 = o2;
              n3 += "?" + (r3.Authorization.indexOf("q-signature") > -1 ? function(e4) {
                var t3 = e4.match(/q-url-param-list.*?(?=&)/g)[0], r4 = "q-url-param-list=" + encodeURIComponent(t3.replace(/q-url-param-list=/, "")).toLowerCase(), n4 = new RegExp(t3, "g");
                return e4.replace(n4, r4);
              }(r3.Authorization) : "sign=" + encodeURIComponent(r3.Authorization)), r3.SecurityToken && (n3 += "&x-cos-security-token=" + r3.SecurityToken), r3.ClientIP && (n3 += "&clientIP=" + r3.ClientIP), r3.ClientUA && (n3 += "&clientUA=" + r3.ClientUA), r3.Token && (n3 += "&token=" + r3.Token), i2 && (n3 += "&" + i2), setTimeout(function() {
                t2(null, { Url: n3 });
              });
            }
          });
          return p2 ? (s2 += "?" + p2.Authorization + (p2.SecurityToken ? "&x-cos-security-token=" + p2.SecurityToken : ""), i2 && (s2 += "&" + i2)) : i2 && (s2 += "?" + i2), s2;
        }, getAuth: function(e2) {
          return a.getAuth({ SecretId: e2.SecretId || this.options.SecretId || "", SecretKey: e2.SecretKey || this.options.SecretKey || "", Bucket: e2.Bucket, Region: e2.Region, Method: e2.Method, Key: e2.Key, Query: e2.Query, Headers: e2.Headers, Expires: e2.Expires, UseRawKey: this.options.UseRawKey, SystemClockOffset: this.options.SystemClockOffset });
        } };
        e.exports.init = function(e2, t2) {
          t2.transferToTaskMethod(g, "putObject"), a.each(g, function(t3, r2) {
            e2.prototype[r2] = a.apiWrapper(r2, t3), function(e3, t4, r3) {
              a.each(["Cors", "Acl"], function(n2) {
                if (e3.slice(-n2.length) === n2) {
                  var o2 = e3.slice(0, -n2.length) + n2.toUpperCase(), i2 = a.apiWrapper(e3, t4);
                  r3[o2] = function() {
                    i2.apply(this, arguments);
                  };
                }
              });
            }(r2, t3, e2.prototype);
          });
        };
      }, function(e, t, r) {
        var n = r(0), o = r(11), a = r(30), i = r(4).EventProxy, s = r(1), c = r(7), u = new (r(31))();
        function l(e2, t2) {
          var r2 = e2.TaskId, n2 = e2.Bucket, c2 = e2.Region, u2 = e2.Key, l2 = e2.StorageClass, f2 = this, h2 = {}, m2 = e2.FileSize, g2 = e2.SliceSize, y2 = Math.ceil(m2 / g2), v2 = 0, k = s.throttleOnProgress.call(f2, m2, e2.onHashProgress), b = function(t3, r3) {
            var n3 = t3.length;
            if (0 === n3) return r3(null, true);
            if (n3 > y2) return r3(null, false);
            if (n3 > 1 && Math.max(t3[0].Size, t3[1].Size) !== g2) return r3(null, false);
            !function o2(a2) {
              if (a2 < n3) {
                var i2 = t3[a2];
                !function(t4, r4) {
                  var n4 = g2 * (t4 - 1), o3 = Math.min(n4 + g2, m2), a3 = o3 - n4;
                  h2[t4] ? r4(null, { PartNumber: t4, ETag: h2[t4], Size: a3 }) : s.fileSlice(e2.Body, n4, o3, false, function(e3) {
                    s.getFileMd5(e3, function(e4, n5) {
                      if (e4) return r4(s.error(e4));
                      var o4 = '"' + n5 + '"';
                      h2[t4] = o4, k({ loaded: v2 += a3, total: m2 }), r4(null, { PartNumber: t4, ETag: o4, Size: a3 });
                    });
                  });
                }(i2.PartNumber, function(e3, t4) {
                  t4 && t4.ETag === i2.ETag && t4.Size === i2.Size ? o2(a2 + 1) : r3(null, false);
                });
              } else r3(null, true);
            }(0);
          }, C = new i();
          C.on("error", function(e3) {
            if (f2._isRunningTask(r2)) return t2(e3);
          }), C.on("upload_id_available", function(e3) {
            var r3 = {}, n3 = [];
            s.each(e3.PartList, function(e4) {
              r3[e4.PartNumber] = e4;
            });
            for (var o2 = 1; o2 <= y2; o2++) {
              var a2 = r3[o2];
              a2 ? (a2.PartNumber = o2, a2.Uploaded = true) : a2 = { PartNumber: o2, ETag: null, Uploaded: false }, n3.push(a2);
            }
            e3.PartList = n3, t2(null, e3);
          }), C.on("no_available_upload_id", function() {
            if (f2._isRunningTask(r2)) {
              var o2 = s.extend({ Bucket: n2, Region: c2, Key: u2, Query: s.clone(e2.Query), StorageClass: l2, Body: e2.Body, calledBySdk: "sliceUploadFile", tracker: e2.tracker }, e2), a2 = s.clone(e2.Headers);
              delete a2["x-cos-mime-limit"], o2.Headers = a2, f2.multipartInit(o2, function(e3, n3) {
                if (f2._isRunningTask(r2)) {
                  if (e3) return C.emit("error", e3);
                  var o3 = n3.UploadId;
                  if (!o3) return t2(s.error(new Error("no such upload id")));
                  C.emit("upload_id_available", { UploadId: o3, PartList: [] });
                }
              });
            }
          }), C.on("has_and_check_upload_id", function(t3) {
            t3 = t3.reverse(), a.eachLimit(t3, 1, function(t4, a2) {
              f2._isRunningTask(r2) && (o.using[t4] ? a2() : p.call(f2, { Bucket: n2, Region: c2, Key: u2, UploadId: t4, tracker: e2.tracker }, function(e3, n3) {
                if (f2._isRunningTask(r2)) {
                  if (e3) return o.removeUsing(t4), C.emit("error", e3);
                  var i2 = n3.PartList;
                  i2.forEach(function(e4) {
                    e4.PartNumber *= 1, e4.Size *= 1, e4.ETag = e4.ETag || "";
                  }), b(i2, function(e4, n4) {
                    if (f2._isRunningTask(r2)) return e4 ? C.emit("error", e4) : void (n4 ? a2({ UploadId: t4, PartList: i2 }) : a2());
                  });
                }
              }));
            }, function(e3) {
              f2._isRunningTask(r2) && (k(null, true), e3 && e3.UploadId ? C.emit("upload_id_available", e3) : C.emit("no_available_upload_id"));
            });
          }), C.on("seek_local_avail_upload_id", function(t3) {
            var a2 = o.getFileId(e2.Body, e2.ChunkSize, n2, u2), i2 = o.getUploadIdList.call(f2, a2);
            if (a2 && i2) {
              !function a3(l3) {
                if (l3 >= i2.length) C.emit("has_and_check_upload_id", t3);
                else {
                  var d2 = i2[l3];
                  if (!s.isInArray(t3, d2)) return o.removeUploadId.call(f2, d2), void a3(l3 + 1);
                  o.using[d2] ? a3(l3 + 1) : p.call(f2, { Bucket: n2, Region: c2, Key: u2, UploadId: d2, tracker: e2.tracker }, function(e3, t4) {
                    f2._isRunningTask(r2) && (e3 ? (o.removeUploadId.call(f2, d2), a3(l3 + 1)) : C.emit("upload_id_available", { UploadId: d2, PartList: t4.PartList }));
                  });
                }
              }(0);
            } else C.emit("has_and_check_upload_id", t3);
          }), C.on("get_remote_upload_id_list", function() {
            d.call(f2, { Bucket: n2, Region: c2, Key: u2, tracker: e2.tracker }, function(t3, a2) {
              if (f2._isRunningTask(r2)) {
                if (t3) return C.emit("error", t3);
                var i2 = s.filter(a2.UploadList, function(e3) {
                  return e3.Key === u2 && (!l2 || e3.StorageClass.toUpperCase() === l2.toUpperCase());
                }).reverse().map(function(e3) {
                  return e3.UploadId || e3.UploadID;
                });
                if (i2.length) C.emit("seek_local_avail_upload_id", i2);
                else {
                  var c3, d2 = o.getFileId(e2.Body, e2.ChunkSize, n2, u2);
                  d2 && (c3 = o.getUploadIdList.call(f2, d2)) && s.each(c3, function(e3) {
                    o.removeUploadId.call(f2, e3);
                  }), C.emit("no_available_upload_id");
                }
              }
            });
          }), C.emit("get_remote_upload_id_list");
        }
        function d(e2, t2) {
          var r2 = this, n2 = [], o2 = { Bucket: e2.Bucket, Region: e2.Region, Prefix: e2.Key, calledBySdk: e2.calledBySdk || "sliceUploadFile", tracker: e2.tracker };
          !function e3() {
            r2.multipartList(o2, function(r3, a2) {
              if (r3) return t2(r3);
              n2.push.apply(n2, a2.Upload || []), "true" === a2.IsTruncated ? (o2.KeyMarker = a2.NextKeyMarker, o2.UploadIdMarker = a2.NextUploadIdMarker, e3()) : t2(null, { UploadList: n2 });
            });
          }();
        }
        function p(e2, t2) {
          var r2 = this, n2 = [], o2 = { Bucket: e2.Bucket, Region: e2.Region, Key: e2.Key, UploadId: e2.UploadId, calledBySdk: "sliceUploadFile", tracker: e2.tracker };
          !function e3() {
            r2.multipartListPart(o2, function(r3, a2) {
              if (r3) return t2(r3);
              n2.push.apply(n2, a2.Part || []), "true" === a2.IsTruncated ? (o2.PartNumberMarker = a2.NextPartNumberMarker, e3()) : t2(null, { PartList: n2 });
            });
          }();
        }
        function f(e2, t2) {
          var r2 = this, n2 = e2.TaskId, o2 = e2.Bucket, i2 = e2.Region, c2 = e2.Key, l2 = e2.UploadData, d2 = e2.FileSize, p2 = e2.SliceSize, f2 = 1;
          f2 = r2.options.ChunkParallelLimit && !r2.options.DynamicAccelerate ? Math.min(e2.AsyncLimit || r2.options.ChunkParallelLimit || 1, 256) : u.getScheduledParallelLimit.bind(u);
          var m2 = e2.Body, g2 = Math.ceil(d2 / p2), y2 = 0, v2 = e2.ServerSideEncryption, k = e2.Headers, b = s.filter(l2.PartList, function(e3) {
            return e3.Uploaded && (y2 += e3.PartNumber >= g2 && d2 % p2 || p2), !e3.Uploaded;
          }), C = e2.onProgress;
          a.eachLimit(b, f2, function(t3, a2) {
            if (r2._isRunningTask(n2)) {
              var s2 = t3.PartNumber, u2 = Math.min(d2, t3.PartNumber * p2) - (t3.PartNumber - 1) * p2, f3 = 0;
              h.call(r2, { TaskId: n2, Bucket: o2, Region: i2, Key: c2, SliceSize: p2, FileSize: d2, PartNumber: s2, ServerSideEncryption: v2, Body: m2, UploadData: l2, Headers: k, onProgress: function(e3) {
                y2 += e3.loaded - f3, f3 = e3.loaded, C({ loaded: y2, total: d2 });
              }, tracker: e2.tracker }, function(e3, o3) {
                r2._isRunningTask(n2) && (e3 || o3.ETag || (e3 = 'get ETag error, please add "ETag" to CORS ExposeHeader setting.( 获取ETag失败，请在CORS ExposeHeader设置中添加ETag，请参考文档：https://cloud.tencent.com/document/product/436/13318 )'), e3 ? y2 -= f3 : (y2 += u2 - f3, t3.ETag = o3.ETag), C({ loaded: y2, total: d2 }), a2(e3 || null, o3));
              });
            }
          }, function(e3) {
            if (r2._isRunningTask(n2)) return e3 ? t2(e3) : void t2(null, { UploadId: l2.UploadId, SliceList: l2.PartList });
          });
        }
        function h(e2, t2) {
          var r2 = this, n2 = e2.TaskId, o2 = e2.Bucket, i2 = e2.Region, c2 = e2.Key, l2 = e2.FileSize, d2 = e2.Body, p2 = 1 * e2.PartNumber, f2 = e2.SliceSize, h2 = e2.ServerSideEncryption, m2 = e2.UploadData, g2 = e2.Headers || {}, y2 = r2.options.ChunkRetryTimes + 1, v2 = f2 * (p2 - 1), k = f2, b = v2 + f2;
          b > l2 && (k = (b = l2) - v2);
          var C = ["x-cos-traffic-limit", "x-cos-mime-limit"], S = {};
          s.each(g2, function(e3, t3) {
            C.indexOf(t3) > -1 && (S[t3] = e3);
          });
          var T = m2.PartList[p2 - 1];
          a.retry(y2, function(t3) {
            r2._isRunningTask(n2) && s.fileSlice(d2, v2, b, true, function(a2) {
              u.markStart(p2), r2.multipartUpload({ TaskId: n2, Bucket: o2, Region: i2, Key: c2, ContentLength: k, PartNumber: p2, UploadId: m2.UploadId, ServerSideEncryption: h2, Body: a2, Headers: S, onProgress: e2.onProgress, calledBySdk: "sliceUploadFile", tracker: e2.tracker }, function(e3, o3) {
                if (u.markEnd(p2), r2._isRunningTask(n2)) return e3 ? (u.triggerFallback(), t3(e3)) : (T.Uploaded = true, t3(null, o3));
              });
            });
          }, function(e3, o3) {
            if (r2._isRunningTask(n2)) return t2(e3, o3);
          });
        }
        function m(e2, t2) {
          var r2 = e2.Bucket, n2 = e2.Region, o2 = e2.Key, i2 = e2.UploadId, s2 = e2.SliceList, c2 = this, u2 = this.options.ChunkRetryTimes + 1, l2 = e2.Headers, d2 = s2.map(function(e3) {
            return { PartNumber: e3.PartNumber, ETag: e3.ETag };
          });
          a.retry(u2, function(t3) {
            c2.multipartComplete({ Bucket: r2, Region: n2, Key: o2, UploadId: i2, Parts: d2, Headers: l2, calledBySdk: "sliceUploadFile", tracker: e2.tracker }, t3);
          }, function(e3, r3) {
            t2(e3, r3);
          });
        }
        function g(e2, t2) {
          var r2 = e2.Bucket, n2 = e2.Region, o2 = e2.Key, i2 = e2.AbortArray, s2 = e2.AsyncLimit || 1, c2 = this, u2 = 0, l2 = new Array(i2.length);
          a.eachLimit(i2, s2, function(t3, a2) {
            var i3 = u2;
            if (o2 && o2 !== t3.Key) return l2[i3] = { error: { KeyNotMatch: true } }, void a2(null);
            var s3 = t3.UploadId || t3.UploadID;
            c2.multipartAbort({ Bucket: r2, Region: n2, Key: t3.Key, Headers: e2.Headers, UploadId: s3 }, function(e3) {
              var o3 = { Bucket: r2, Region: n2, Key: t3.Key, UploadId: s3 };
              l2[i3] = { error: e3, task: o3 }, a2(null);
            }), u2++;
          }, function(e3) {
            if (e3) return t2(e3);
            for (var r3 = [], n3 = [], o3 = 0, a2 = l2.length; o3 < a2; o3++) {
              var i3 = l2[o3];
              i3.task && (i3.error ? n3.push(i3.task) : r3.push(i3.task));
            }
            return t2(null, { successList: r3, errorList: n3 });
          });
        }
        function y(e2, t2) {
          var r2 = e2.TaskId, n2 = e2.Bucket, o2 = e2.Region, i2 = e2.Key, s2 = e2.CopySource, c2 = e2.UploadId, u2 = 1 * e2.PartNumber, l2 = e2.CopySourceRange, d2 = this.options.ChunkRetryTimes + 1, p2 = this;
          a.retry(d2, function(t3) {
            p2.uploadPartCopy({ TaskId: r2, Bucket: n2, Region: o2, Key: i2, CopySource: s2, UploadId: c2, PartNumber: u2, CopySourceRange: l2, tracker: e2.tracker, calledBySdk: e2.calledBySdk }, function(e3, r3) {
              t3(e3 || null, r3);
            });
          }, function(e3, r3) {
            return t2(e3, r3);
          });
        }
        var v = { sliceUploadFile: function(e2, t2) {
          var r2, n2, a2 = this, c2 = new i(), u2 = e2.TaskId, d2 = e2.Bucket, p2 = e2.Region, h2 = e2.Key, g2 = e2.Body, y2 = e2.ChunkSize || e2.SliceSize || a2.options.ChunkSize, v2 = e2.AsyncLimit, k = e2.StorageClass, b = e2.ServerSideEncryption, C = e2.onHashProgress, S = e2.tracker;
          S && S.setParams({ chunkSize: y2 }), c2.on("error", function(r3) {
            if (a2._isRunningTask(u2)) return r3.UploadId = e2.UploadData.UploadId || "", t2(r3);
          }), c2.on("upload_complete", function(r3) {
            var n3 = s.extend({ UploadId: e2.UploadData.UploadId || "" }, r3);
            t2(null, n3);
          }), c2.on("upload_slice_complete", function(t3) {
            var i2 = {};
            s.each(e2.Headers, function(e3, t4) {
              var r3 = t4.toLowerCase();
              (0 === r3.indexOf("x-cos-meta-") || ["pic-operations", "x-cos-callback", "x-cos-callback-var", "x-cos-return-body"].includes(r3)) && (i2[t4] = e3);
            }), m.call(a2, { Bucket: d2, Region: p2, Key: h2, UploadId: t3.UploadId, SliceList: t3.SliceList, Headers: i2, tracker: S }, function(e3, i3) {
              if (a2._isRunningTask(u2)) {
                if (o.removeUsing(t3.UploadId), e3) return n2(null, true), c2.emit("error", e3);
                o.removeUploadId.call(a2, t3.UploadId), n2({ loaded: r2, total: r2 }, true), c2.emit("upload_complete", i3);
              }
            });
          }), c2.on("get_upload_data_finish", function(t3) {
            var i2 = o.getFileId(g2, e2.ChunkSize, d2, h2);
            i2 && o.saveUploadId.call(a2, i2, t3.UploadId, a2.options.UploadIdCacheLimit), o.setUsing(t3.UploadId), n2(null, true), f.call(a2, { TaskId: u2, Bucket: d2, Region: p2, Key: h2, Body: g2, FileSize: r2, SliceSize: y2, AsyncLimit: v2, ServerSideEncryption: b, UploadData: t3, Headers: e2.Headers, onProgress: n2, tracker: S }, function(e3, t4) {
              if (a2._isRunningTask(u2)) return e3 ? (n2(null, true), c2.emit("error", e3)) : void c2.emit("upload_slice_complete", t4);
            });
          }), c2.on("get_file_size_finish", function() {
            if (n2 = s.throttleOnProgress.call(a2, r2, e2.onProgress), e2.UploadData.UploadId) c2.emit("get_upload_data_finish", e2.UploadData);
            else {
              var t3 = s.extend({ TaskId: u2, Bucket: d2, Region: p2, Key: h2, Headers: e2.Headers, StorageClass: k, Body: g2, FileSize: r2, SliceSize: y2, onHashProgress: C, tracker: S }, e2);
              l.call(a2, t3, function(t4, r3) {
                if (a2._isRunningTask(u2)) {
                  if (t4) return c2.emit("error", t4);
                  e2.UploadData.UploadId = r3.UploadId, e2.UploadData.PartList = r3.PartList, c2.emit("get_upload_data_finish", e2.UploadData);
                }
              });
            }
          }), r2 = e2.ContentLength, delete e2.ContentLength, !e2.Headers && (e2.Headers = {}), s.each(e2.Headers, function(t3, r3) {
            "content-length" === r3.toLowerCase() && delete e2.Headers[r3];
          }), function() {
            for (var t3 = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 5120], n3 = 1048576, o2 = 0; o2 < t3.length && !(r2 / (n3 = 1024 * t3[o2] * 1024) <= a2.options.MaxPartNumber); o2++) ;
            e2.ChunkSize = e2.SliceSize = y2 = Math.max(y2, n3);
          }(), 0 === r2 ? (e2.Body = "", e2.ContentLength = 0, e2.SkipTask = true, a2.putObject(e2, t2)) : c2.emit("get_file_size_finish");
        }, abortUploadTask: function(e2, t2) {
          var r2 = e2.Bucket, n2 = e2.Region, o2 = e2.Key, a2 = e2.UploadId, c2 = e2.Level || "task", u2 = e2.AsyncLimit, l2 = this, p2 = new i();
          if (p2.on("error", function(e3) {
            return t2(e3);
          }), p2.on("get_abort_array", function(a3) {
            g.call(l2, { Bucket: r2, Region: n2, Key: o2, Headers: e2.Headers, AsyncLimit: u2, AbortArray: a3 }, t2);
          }), "bucket" === c2) d.call(l2, { Bucket: r2, Region: n2, calledBySdk: "abortUploadTask" }, function(e3, r3) {
            if (e3) return t2(e3);
            p2.emit("get_abort_array", r3.UploadList || []);
          });
          else if ("file" === c2) {
            if (!o2) return t2(s.error(new Error("abort_upload_task_no_key")));
            d.call(l2, { Bucket: r2, Region: n2, Key: o2, calledBySdk: "abortUploadTask" }, function(e3, r3) {
              if (e3) return t2(e3);
              p2.emit("get_abort_array", r3.UploadList || []);
            });
          } else {
            if ("task" !== c2) return t2(s.error(new Error("abort_unknown_level")));
            if (!a2) return t2(s.error(new Error("abort_upload_task_no_id")));
            if (!o2) return t2(s.error(new Error("abort_upload_task_no_key")));
            p2.emit("get_abort_array", [{ Key: o2, UploadId: a2 }]);
          }
        }, uploadFile: function(e2, t2) {
          var r2 = this, o2 = void 0 === e2.SliceSize ? r2.options.SliceSize : e2.SliceSize, a2 = [], i2 = e2.Body, u2 = i2.size || i2.length || 0, l2 = { TaskId: "" };
          if (r2.options.EnableReporter) {
            var d2 = r2.options.UseAccelerate || "string" == typeof r2.options.Domain && r2.options.Domain.includes("accelerate."), p2 = u2 > o2 ? "sliceUploadFile" : "putObject";
            e2.tracker = new c({ Beacon: r2.options.BeaconReporter, clsReporter: r2.options.ClsReporter, bucket: e2.Bucket, region: e2.Region, apiName: "uploadFile", realApi: p2, fileKey: e2.Key, fileSize: u2, accelerate: d2, deepTracker: r2.options.DeepTracker, customId: r2.options.CustomId, delay: r2.options.TrackerDelay });
          }
          s.each(e2, function(e3, t3) {
            "object" !== n(e3) && "function" != typeof e3 && (l2[t3] = e3);
          });
          var f2 = e2.onTaskReady;
          e2.onTaskReady = function(e3) {
            l2.TaskId = e3, f2 && f2(e3);
          };
          var h2 = u2 > o2 ? "sliceUploadFile" : "putObject", m2 = e2.onFileFinish;
          a2.push({ api: h2, params: e2, callback: function(r3, n2) {
            e2.tracker && e2.tracker.report(r3, n2), m2 && m2(r3, n2, l2), t2 && t2(r3, n2);
          } }), r2._addTasks(a2);
        }, uploadFiles: function(e2, t2) {
          var r2 = this, o2 = void 0 === e2.SliceSize ? r2.options.SliceSize : e2.SliceSize, a2 = 0, i2 = 0, u2 = s.throttleOnProgress.call(r2, i2, e2.onProgress), l2 = e2.files.length, d2 = e2.onFileFinish, p2 = Array(l2), f2 = function(e3, r3, n2) {
            u2(null, true), d2 && d2(e3, r3, n2), p2[n2.Index] = { options: n2, error: e3, data: r3 }, --l2 <= 0 && t2 && t2(null, { files: p2 });
          }, h2 = [];
          s.each(e2.files, function(e3, t3) {
            !function() {
              var l3 = e3.Body, d3 = l3.size || l3.length || 0, p3 = { Index: t3, TaskId: "" };
              if (!r2.options.UseRawKey && e3.Key && "/" === e3.Key.substr(0, 1) && (e3.Key = e3.Key.substr(1)), a2 += d3, r2.options.EnableReporter) {
                var m2 = r2.options.UseAccelerate || "string" == typeof r2.options.Domain && r2.options.Domain.includes("accelerate."), g2 = d3 > o2 ? "sliceUploadFile" : "putObject";
                e3.tracker = new c({ Beacon: r2.options.BeaconReporter, clsReporter: r2.options.ClsReporter, bucket: e3.Bucket, region: e3.Region, apiName: "uploadFiles", realApi: g2, fileKey: e3.Key, fileSize: d3, accelerate: m2, deepTracker: r2.options.DeepTracker, customId: r2.options.CustomId, delay: r2.options.TrackerDelay });
              }
              s.each(e3, function(e4, t4) {
                "object" !== n(e4) && "function" != typeof e4 && (p3[t4] = e4);
              });
              var y2 = e3.onTaskReady;
              e3.onTaskReady = function(e4) {
                p3.TaskId = e4, y2 && y2(e4);
              };
              var v2 = 0, k = e3.onProgress;
              e3.onProgress = function(e4) {
                i2 = i2 - v2 + e4.loaded, v2 = e4.loaded, k && k(e4), u2({ loaded: i2, total: a2 });
              };
              var b = d3 > o2 ? "sliceUploadFile" : "putObject", C = e3.onFileFinish;
              h2.push({ api: b, params: e3, callback: function(t4, r3) {
                e3.tracker && e3.tracker.report(t4, r3), C && C(t4, r3), f2 && f2(t4, r3, p3);
              } });
            }();
          }), r2._addTasks(h2);
        }, sliceCopyFile: function(e2, t2) {
          var r2 = new i(), n2 = this, c2 = e2.Bucket, u2 = e2.Region, l2 = e2.Key, d2 = e2.CopySource, f2 = s.getSourceParams.call(this, d2);
          if (f2) {
            var h2 = f2.Bucket, m2 = f2.Region, g2 = decodeURIComponent(f2.Key), v2 = void 0 === e2.CopySliceSize ? n2.options.CopySliceSize : e2.CopySliceSize;
            v2 = Math.max(0, v2);
            var k, b, C = e2.CopyChunkSize || this.options.CopyChunkSize, S = this.options.CopyChunkParallelLimit, T = this.options.ChunkRetryTimes + 1, w = 0, R = 0, x = {}, A = {}, E = {};
            r2.on("copy_slice_complete", function(r3) {
              var i2 = {};
              s.each(e2.Headers, function(e3, t3) {
                0 === t3.toLowerCase().indexOf("x-cos-meta-") && (i2[t3] = e3);
              });
              var d3 = s.map(r3.PartList, function(e3) {
                return { PartNumber: e3.PartNumber, ETag: e3.ETag };
              });
              a.retry(T, function(t3) {
                n2.multipartComplete({ Bucket: c2, Region: u2, Key: l2, UploadId: r3.UploadId, Parts: d3, tracker: e2.tracker, calledBySdk: "sliceCopyFile" }, t3);
              }, function(e3, n3) {
                if (o.removeUsing(r3.UploadId), e3) return b(null, true), t2(e3);
                o.removeUploadId(r3.UploadId), b({ loaded: k, total: k }, true), t2(null, n3);
              });
            }), r2.on("get_copy_data_finish", function(i2) {
              var p2 = o.getCopyFileId(d2, x, C, c2, l2);
              p2 && o.saveUploadId(p2, i2.UploadId, n2.options.UploadIdCacheLimit), o.setUsing(i2.UploadId);
              var f3 = s.filter(i2.PartList, function(e3) {
                return e3.Uploaded && (R += e3.PartNumber >= w && k % C || C), !e3.Uploaded;
              });
              a.eachLimit(f3, S, function(t3, r3) {
                var o2 = t3.PartNumber, s2 = t3.CopySourceRange, p3 = t3.end - t3.start;
                a.retry(T, function(t4) {
                  y.call(n2, { Bucket: c2, Region: u2, Key: l2, CopySource: d2, UploadId: i2.UploadId, PartNumber: o2, CopySourceRange: s2, tracker: e2.tracker, calledBySdk: "sliceCopyFile" }, t4);
                }, function(e3, n3) {
                  if (e3) return r3(e3);
                  b({ loaded: R += p3, total: k }), t3.ETag = n3.ETag, r3(e3 || null, n3);
                });
              }, function(e3) {
                if (e3) return o.removeUsing(i2.UploadId), b(null, true), t2(e3);
                r2.emit("copy_slice_complete", i2);
              });
            }), r2.on("get_chunk_size_finish", function() {
              var a2 = function() {
                n2.multipartInit({ Bucket: c2, Region: u2, Key: l2, Headers: E, tracker: e2.tracker, calledBySdk: "sliceCopyFile" }, function(n3, o2) {
                  if (n3) return t2(n3);
                  e2.UploadId = o2.UploadId, r2.emit("get_copy_data_finish", { UploadId: e2.UploadId, PartList: e2.PartList });
                });
              }, i2 = o.getCopyFileId(d2, x, C, c2, l2), f3 = o.getUploadIdList(i2);
              if (!i2 || !f3) return a2();
              !function t3(i3) {
                if (i3 >= f3.length) return a2();
                var d3 = f3[i3];
                if (o.using[d3]) return t3(i3 + 1);
                p.call(n2, { Bucket: c2, Region: u2, Key: l2, UploadId: d3, tracker: e2.tracker, calledBySdk: "sliceCopyFile" }, function(n3, a3) {
                  if (n3) o.removeUploadId(d3), t3(i3 + 1);
                  else {
                    if (o.using[d3]) return t3(i3 + 1);
                    var c3 = {}, u3 = 0;
                    s.each(a3.PartList, function(e3) {
                      var t4 = parseInt(e3.Size), r3 = u3 + t4 - 1;
                      c3[e3.PartNumber + "|" + u3 + "|" + r3] = e3.ETag, u3 += t4;
                    }), s.each(e2.PartList, function(e3) {
                      var t4 = c3[e3.PartNumber + "|" + e3.start + "|" + e3.end];
                      t4 && (e3.ETag = t4, e3.Uploaded = true);
                    }), r2.emit("get_copy_data_finish", { UploadId: d3, PartList: e2.PartList });
                  }
                });
              }(0);
            }), r2.on("get_file_size_finish", function() {
              if (function() {
                for (var t3 = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 5120], r3 = 1048576, o3 = 0; o3 < t3.length && !(k / (r3 = 1024 * t3[o3] * 1024) <= n2.options.MaxPartNumber); o3++) ;
                e2.ChunkSize = C = Math.max(C, r3), w = Math.ceil(k / C);
                for (var a2 = [], i2 = 1; i2 <= w; i2++) {
                  var s2 = (i2 - 1) * C, c3 = i2 * C < k ? i2 * C - 1 : k - 1, u3 = { PartNumber: i2, start: s2, end: c3, CopySourceRange: "bytes=" + s2 + "-" + c3 };
                  a2.push(u3);
                }
                e2.PartList = a2;
              }(), (E = "Replaced" === e2.Headers["x-cos-metadata-directive"] ? e2.Headers : A)["x-cos-storage-class"] = e2.Headers["x-cos-storage-class"] || A["x-cos-storage-class"], E = s.clearKey(E), "ARCHIVE" === A["x-cos-storage-class"] || "DEEP_ARCHIVE" === A["x-cos-storage-class"]) {
                var o2 = A["x-cos-restore"];
                if (!o2 || 'ongoing-request="true"' === o2) return void t2(s.error(new Error("Unrestored archive object is not allowed to be copied")));
              }
              delete E["x-cos-copy-source"], delete E["x-cos-metadata-directive"], delete E["x-cos-copy-source-If-Modified-Since"], delete E["x-cos-copy-source-If-Unmodified-Since"], delete E["x-cos-copy-source-If-Match"], delete E["x-cos-copy-source-If-None-Match"], r2.emit("get_chunk_size_finish");
            }), n2.headObject({ Bucket: h2, Region: m2, Key: g2, tracker: e2.tracker, calledBySdk: "sliceCopyFile" }, function(o2, a2) {
              if (o2) o2.statusCode && 404 === o2.statusCode ? t2(s.error(o2, { ErrorStatus: g2 + " Not Exist" })) : t2(o2);
              else if (void 0 !== (k = e2.FileSize = a2.headers["content-length"]) && k) if (e2.tracker && e2.tracker.setParams({ httpSize: k }), b = s.throttleOnProgress.call(n2, k, e2.onProgress), k <= v2) e2.Headers["x-cos-metadata-directive"] || (e2.Headers["x-cos-metadata-directive"] = "Copy"), n2.putObjectCopy(Object.assign(e2, { calledBySdk: "sliceCopyFile" }), function(e3, r3) {
                if (e3) return b(null, true), t2(e3);
                b({ loaded: k, total: k }, true), t2(e3, r3);
              });
              else {
                var i2 = a2.headers;
                x = i2, A = { "Cache-Control": i2["cache-control"], "Content-Disposition": i2["content-disposition"], "Content-Encoding": i2["content-encoding"], "Content-Type": i2["content-type"], Expires: i2.expires, "x-cos-storage-class": i2["x-cos-storage-class"] }, s.each(i2, function(e3, t3) {
                  var r3 = "x-cos-meta-";
                  0 === t3.indexOf(r3) && t3.length > 11 && (A[t3] = e3);
                }), r2.emit("get_file_size_finish");
              }
              else t2(s.error(new Error('get Content-Length error, please add "Content-Length" to CORS ExposeHeader setting.（ 获取Content-Length失败，请在CORS ExposeHeader设置中添加Content-Length，请参考文档：https://cloud.tencent.com/document/product/436/13318 ）')));
            });
          } else t2(s.error(new Error("CopySource format error")));
        } };
        e.exports.init = function(e2, t2) {
          t2.transferToTaskMethod(v, "sliceUploadFile"), s.each(v, function(t3, r2) {
            e2.prototype[r2] = s.apiWrapper(r2, t3);
          });
        };
      }, function(e, t, r) {
        var n = r(15);
        e.exports = n;
      }, function(e, t, r) {
        "use strict";
        var n = r(1), o = r(4), a = r(10), i = r(12), s = r(13), c = r(3), u = { AppId: "", SecretId: "", SecretKey: "", SecurityToken: "", StartTime: 0, ExpiredTime: 0, ChunkRetryTimes: 2, FileParallelLimit: 3, ChunkParallelLimit: 3, ChunkSize: 1048576, SliceSize: 1048576, CopyChunkParallelLimit: 20, CopyChunkSize: 10485760, CopySliceSize: 10485760, MaxPartNumber: 1e4, ProgressInterval: 1e3, Domain: "", ServiceDomain: "", Protocol: "", CompatibilityMode: false, ForcePathStyle: false, UseRawKey: false, Timeout: 0, CorrectClockSkew: true, SystemClockOffset: 0, UploadCheckContentMd5: false, UploadQueueSize: 1e4, UploadAddMetaMd5: false, UploadIdCacheLimit: 50, UseAccelerate: false, ForceSignHost: true, AutoSwitchHost: true, CopySourceParser: null, ObjectKeySimplifyCheck: true, DeepTracker: false, TrackerDelay: 5e3, CustomId: "", BeaconReporter: null, ClsReporter: null }, l = function(e2) {
          if (this.options = n.extend(n.clone(u), e2 || {}), this.options.FileParallelLimit = Math.max(1, this.options.FileParallelLimit), this.options.ChunkParallelLimit = Math.max(1, this.options.ChunkParallelLimit), this.options.ChunkRetryTimes = Math.max(0, this.options.ChunkRetryTimes), this.options.ChunkSize = Math.max(1048576, this.options.ChunkSize), this.options.CopyChunkParallelLimit = Math.max(1, this.options.CopyChunkParallelLimit), this.options.CopyChunkSize = Math.max(1048576, this.options.CopyChunkSize), this.options.CopySliceSize = Math.max(0, this.options.CopySliceSize), this.options.MaxPartNumber = Math.max(1024, Math.min(1e4, this.options.MaxPartNumber)), this.options.Timeout = Math.max(0, this.options.Timeout), this.options.EnableReporter = this.options.BeaconReporter || this.options.ClsReporter, this.options.AppId, this.options.SecretId && this.options.SecretId.indexOf(" "), this.options.SecretKey && this.options.SecretKey.indexOf(" "), n.isNode(), this.options.ForcePathStyle) throw new Error("ForcePathStyle is not supported");
          o.init(this), a.init(this);
        };
        i.init(l, a), s.init(l, a), l.util = { md5: n.md5, xml2json: n.xml2json, json2xml: n.json2xml, encodeBase64: n.encodeBase64 }, l.getAuthorization = n.getAuth, l.version = c.version, e.exports = l;
      }, function(e, t) {
        var r, n, o = e.exports = {};
        function a() {
          throw new Error("setTimeout has not been defined");
        }
        function i() {
          throw new Error("clearTimeout has not been defined");
        }
        function s(e2) {
          if (r === setTimeout) return setTimeout(e2, 0);
          if ((r === a || !r) && setTimeout) return r = setTimeout, setTimeout(e2, 0);
          try {
            return r(e2, 0);
          } catch (t2) {
            try {
              return r.call(null, e2, 0);
            } catch (t3) {
              return r.call(this, e2, 0);
            }
          }
        }
        !function() {
          try {
            r = "function" == typeof setTimeout ? setTimeout : a;
          } catch (e2) {
            r = a;
          }
          try {
            n = "function" == typeof clearTimeout ? clearTimeout : i;
          } catch (e2) {
            n = i;
          }
        }();
        var c, u = [], l = false, d = -1;
        function p() {
          l && c && (l = false, c.length ? u = c.concat(u) : d = -1, u.length && f());
        }
        function f() {
          if (!l) {
            var e2 = s(p);
            l = true;
            for (var t2 = u.length; t2; ) {
              for (c = u, u = []; ++d < t2; ) c && c[d].run();
              d = -1, t2 = u.length;
            }
            c = null, l = false, function(e3) {
              if (n === clearTimeout) return clearTimeout(e3);
              if ((n === i || !n) && clearTimeout) return n = clearTimeout, clearTimeout(e3);
              try {
                return n(e3);
              } catch (t3) {
                try {
                  return n.call(null, e3);
                } catch (t4) {
                  return n.call(this, e3);
                }
              }
            }(e2);
          }
        }
        function h(e2, t2) {
          this.fun = e2, this.array = t2;
        }
        function m() {
        }
        o.nextTick = function(e2) {
          var t2 = new Array(arguments.length - 1);
          if (arguments.length > 1) for (var r2 = 1; r2 < arguments.length; r2++) t2[r2 - 1] = arguments[r2];
          u.push(new h(e2, t2)), 1 !== u.length || l || s(f);
        }, h.prototype.run = function() {
          this.fun.apply(null, this.array);
        }, o.title = "browser", o.browser = true, o.env = {}, o.argv = [], o.version = "", o.versions = {}, o.on = m, o.addListener = m, o.once = m, o.off = m, o.removeListener = m, o.removeAllListeners = m, o.emit = m, o.prependListener = m, o.prependOnceListener = m, o.listeners = function(e2) {
          return [];
        }, o.binding = function(e2) {
          throw new Error("process.binding is not supported");
        }, o.cwd = function() {
          return "/";
        }, o.chdir = function(e2) {
          throw new Error("process.chdir is not supported");
        }, o.umask = function() {
          return 0;
        };
      }, function(e, t, r) {
        (function(e2) {
          var t2, n = r(0);
          !function() {
            "use strict";
            var o = "object" === ("undefined" == typeof window ? "undefined" : n(window)), a = o ? window : {};
            a.JS_MD5_NO_WINDOW && (o = false), !o && "object" === ("undefined" == typeof self ? "undefined" : n(self)) && (a = self);
            var i, s = !a.JS_MD5_NO_COMMON_JS && "object" === n(e2) && e2.exports, c = r(18), u = !a.JS_MD5_NO_ARRAY_BUFFER && "undefined" != typeof ArrayBuffer, l = "0123456789abcdef".split(""), d = [128, 32768, 8388608, -2147483648], p = [0, 8, 16, 24], f = ["hex", "array", "digest", "buffer", "arrayBuffer", "base64"], h = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""), m = [];
            if (u) {
              var g = new ArrayBuffer(68);
              i = new Uint8Array(g), m = new Uint32Array(g);
            }
            !a.JS_MD5_NO_NODE_JS && Array.isArray || (Array.isArray = function(e3) {
              return "[object Array]" === Object.prototype.toString.call(e3);
            }), !u || !a.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW && ArrayBuffer.isView || (ArrayBuffer.isView = function(e3) {
              return "object" === n(e3) && e3.buffer && e3.buffer.constructor === ArrayBuffer;
            });
            var y = function(e3) {
              return function(t3, r2) {
                return new v(true).update(t3, r2)[e3]();
              };
            };
            function v(e3) {
              if (e3) m[0] = m[16] = m[1] = m[2] = m[3] = m[4] = m[5] = m[6] = m[7] = m[8] = m[9] = m[10] = m[11] = m[12] = m[13] = m[14] = m[15] = 0, this.blocks = m, this.buffer8 = i;
              else if (u) {
                var t3 = new ArrayBuffer(68);
                this.buffer8 = new Uint8Array(t3), this.blocks = new Uint32Array(t3);
              } else this.blocks = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
              this.h0 = this.h1 = this.h2 = this.h3 = this.start = this.bytes = this.hBytes = 0, this.finalized = this.hashed = false, this.first = true;
            }
            v.prototype.update = function(e3, t3) {
              if (!this.finalized) {
                for (var r2, n2, o2 = 0, a2 = e3.length, i2 = this.blocks, s2 = this.buffer8; o2 < a2; ) {
                  if (this.hashed && (this.hashed = false, i2[0] = i2[16], i2[16] = i2[1] = i2[2] = i2[3] = i2[4] = i2[5] = i2[6] = i2[7] = i2[8] = i2[9] = i2[10] = i2[11] = i2[12] = i2[13] = i2[14] = i2[15] = 0), u) for (n2 = this.start; o2 < a2 && n2 < 64; ++o2) r2 = e3.charCodeAt(o2), t3 || r2 < 128 ? s2[n2++] = r2 : r2 < 2048 ? (s2[n2++] = 192 | r2 >> 6, s2[n2++] = 128 | 63 & r2) : r2 < 55296 || r2 >= 57344 ? (s2[n2++] = 224 | r2 >> 12, s2[n2++] = 128 | r2 >> 6 & 63, s2[n2++] = 128 | 63 & r2) : (r2 = 65536 + ((1023 & r2) << 10 | 1023 & e3.charCodeAt(++o2)), s2[n2++] = 240 | r2 >> 18, s2[n2++] = 128 | r2 >> 12 & 63, s2[n2++] = 128 | r2 >> 6 & 63, s2[n2++] = 128 | 63 & r2);
                  else for (n2 = this.start; o2 < a2 && n2 < 64; ++o2) r2 = e3.charCodeAt(o2), t3 || r2 < 128 ? i2[n2 >> 2] |= r2 << p[3 & n2++] : r2 < 2048 ? (i2[n2 >> 2] |= (192 | r2 >> 6) << p[3 & n2++], i2[n2 >> 2] |= (128 | 63 & r2) << p[3 & n2++]) : r2 < 55296 || r2 >= 57344 ? (i2[n2 >> 2] |= (224 | r2 >> 12) << p[3 & n2++], i2[n2 >> 2] |= (128 | r2 >> 6 & 63) << p[3 & n2++], i2[n2 >> 2] |= (128 | 63 & r2) << p[3 & n2++]) : (r2 = 65536 + ((1023 & r2) << 10 | 1023 & e3.charCodeAt(++o2)), i2[n2 >> 2] |= (240 | r2 >> 18) << p[3 & n2++], i2[n2 >> 2] |= (128 | r2 >> 12 & 63) << p[3 & n2++], i2[n2 >> 2] |= (128 | r2 >> 6 & 63) << p[3 & n2++], i2[n2 >> 2] |= (128 | 63 & r2) << p[3 & n2++]);
                  this.lastByteIndex = n2, this.bytes += n2 - this.start, n2 >= 64 ? (this.start = n2 - 64, this.hash(), this.hashed = true) : this.start = n2;
                }
                return this.bytes > 4294967295 && (this.hBytes += this.bytes / 4294967296 | 0, this.bytes = this.bytes % 4294967296), this;
              }
            }, v.prototype.finalize = function() {
              if (!this.finalized) {
                this.finalized = true;
                var e3 = this.blocks, t3 = this.lastByteIndex;
                e3[t3 >> 2] |= d[3 & t3], t3 >= 56 && (this.hashed || this.hash(), e3[0] = e3[16], e3[16] = e3[1] = e3[2] = e3[3] = e3[4] = e3[5] = e3[6] = e3[7] = e3[8] = e3[9] = e3[10] = e3[11] = e3[12] = e3[13] = e3[14] = e3[15] = 0), e3[14] = this.bytes << 3, e3[15] = this.hBytes << 3 | this.bytes >>> 29, this.hash();
              }
            }, v.prototype.hash = function() {
              var e3, t3, r2, n2, o2, a2, i2 = this.blocks;
              this.first ? t3 = ((t3 = ((e3 = ((e3 = i2[0] - 680876937) << 7 | e3 >>> 25) - 271733879 | 0) ^ (r2 = ((r2 = (-271733879 ^ (n2 = ((n2 = (-1732584194 ^ 2004318071 & e3) + i2[1] - 117830708) << 12 | n2 >>> 20) + e3 | 0) & (-271733879 ^ e3)) + i2[2] - 1126478375) << 17 | r2 >>> 15) + n2 | 0) & (n2 ^ e3)) + i2[3] - 1316259209) << 22 | t3 >>> 10) + r2 | 0 : (e3 = this.h0, t3 = this.h1, r2 = this.h2, t3 = ((t3 += ((e3 = ((e3 += ((n2 = this.h3) ^ t3 & (r2 ^ n2)) + i2[0] - 680876936) << 7 | e3 >>> 25) + t3 | 0) ^ (r2 = ((r2 += (t3 ^ (n2 = ((n2 += (r2 ^ e3 & (t3 ^ r2)) + i2[1] - 389564586) << 12 | n2 >>> 20) + e3 | 0) & (e3 ^ t3)) + i2[2] + 606105819) << 17 | r2 >>> 15) + n2 | 0) & (n2 ^ e3)) + i2[3] - 1044525330) << 22 | t3 >>> 10) + r2 | 0), t3 = ((t3 += ((e3 = ((e3 += (n2 ^ t3 & (r2 ^ n2)) + i2[4] - 176418897) << 7 | e3 >>> 25) + t3 | 0) ^ (r2 = ((r2 += (t3 ^ (n2 = ((n2 += (r2 ^ e3 & (t3 ^ r2)) + i2[5] + 1200080426) << 12 | n2 >>> 20) + e3 | 0) & (e3 ^ t3)) + i2[6] - 1473231341) << 17 | r2 >>> 15) + n2 | 0) & (n2 ^ e3)) + i2[7] - 45705983) << 22 | t3 >>> 10) + r2 | 0, t3 = ((t3 += ((e3 = ((e3 += (n2 ^ t3 & (r2 ^ n2)) + i2[8] + 1770035416) << 7 | e3 >>> 25) + t3 | 0) ^ (r2 = ((r2 += (t3 ^ (n2 = ((n2 += (r2 ^ e3 & (t3 ^ r2)) + i2[9] - 1958414417) << 12 | n2 >>> 20) + e3 | 0) & (e3 ^ t3)) + i2[10] - 42063) << 17 | r2 >>> 15) + n2 | 0) & (n2 ^ e3)) + i2[11] - 1990404162) << 22 | t3 >>> 10) + r2 | 0, t3 = ((t3 += ((e3 = ((e3 += (n2 ^ t3 & (r2 ^ n2)) + i2[12] + 1804603682) << 7 | e3 >>> 25) + t3 | 0) ^ (r2 = ((r2 += (t3 ^ (n2 = ((n2 += (r2 ^ e3 & (t3 ^ r2)) + i2[13] - 40341101) << 12 | n2 >>> 20) + e3 | 0) & (e3 ^ t3)) + i2[14] - 1502002290) << 17 | r2 >>> 15) + n2 | 0) & (n2 ^ e3)) + i2[15] + 1236535329) << 22 | t3 >>> 10) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ r2 & ((e3 = ((e3 += (r2 ^ n2 & (t3 ^ r2)) + i2[1] - 165796510) << 5 | e3 >>> 27) + t3 | 0) ^ t3)) + i2[6] - 1069501632) << 9 | n2 >>> 23) + e3 | 0) ^ e3 & ((r2 = ((r2 += (e3 ^ t3 & (n2 ^ e3)) + i2[11] + 643717713) << 14 | r2 >>> 18) + n2 | 0) ^ n2)) + i2[0] - 373897302) << 20 | t3 >>> 12) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ r2 & ((e3 = ((e3 += (r2 ^ n2 & (t3 ^ r2)) + i2[5] - 701558691) << 5 | e3 >>> 27) + t3 | 0) ^ t3)) + i2[10] + 38016083) << 9 | n2 >>> 23) + e3 | 0) ^ e3 & ((r2 = ((r2 += (e3 ^ t3 & (n2 ^ e3)) + i2[15] - 660478335) << 14 | r2 >>> 18) + n2 | 0) ^ n2)) + i2[4] - 405537848) << 20 | t3 >>> 12) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ r2 & ((e3 = ((e3 += (r2 ^ n2 & (t3 ^ r2)) + i2[9] + 568446438) << 5 | e3 >>> 27) + t3 | 0) ^ t3)) + i2[14] - 1019803690) << 9 | n2 >>> 23) + e3 | 0) ^ e3 & ((r2 = ((r2 += (e3 ^ t3 & (n2 ^ e3)) + i2[3] - 187363961) << 14 | r2 >>> 18) + n2 | 0) ^ n2)) + i2[8] + 1163531501) << 20 | t3 >>> 12) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ r2 & ((e3 = ((e3 += (r2 ^ n2 & (t3 ^ r2)) + i2[13] - 1444681467) << 5 | e3 >>> 27) + t3 | 0) ^ t3)) + i2[2] - 51403784) << 9 | n2 >>> 23) + e3 | 0) ^ e3 & ((r2 = ((r2 += (e3 ^ t3 & (n2 ^ e3)) + i2[7] + 1735328473) << 14 | r2 >>> 18) + n2 | 0) ^ n2)) + i2[12] - 1926607734) << 20 | t3 >>> 12) + r2 | 0, t3 = ((t3 += ((a2 = (n2 = ((n2 += ((o2 = t3 ^ r2) ^ (e3 = ((e3 += (o2 ^ n2) + i2[5] - 378558) << 4 | e3 >>> 28) + t3 | 0)) + i2[8] - 2022574463) << 11 | n2 >>> 21) + e3 | 0) ^ e3) ^ (r2 = ((r2 += (a2 ^ t3) + i2[11] + 1839030562) << 16 | r2 >>> 16) + n2 | 0)) + i2[14] - 35309556) << 23 | t3 >>> 9) + r2 | 0, t3 = ((t3 += ((a2 = (n2 = ((n2 += ((o2 = t3 ^ r2) ^ (e3 = ((e3 += (o2 ^ n2) + i2[1] - 1530992060) << 4 | e3 >>> 28) + t3 | 0)) + i2[4] + 1272893353) << 11 | n2 >>> 21) + e3 | 0) ^ e3) ^ (r2 = ((r2 += (a2 ^ t3) + i2[7] - 155497632) << 16 | r2 >>> 16) + n2 | 0)) + i2[10] - 1094730640) << 23 | t3 >>> 9) + r2 | 0, t3 = ((t3 += ((a2 = (n2 = ((n2 += ((o2 = t3 ^ r2) ^ (e3 = ((e3 += (o2 ^ n2) + i2[13] + 681279174) << 4 | e3 >>> 28) + t3 | 0)) + i2[0] - 358537222) << 11 | n2 >>> 21) + e3 | 0) ^ e3) ^ (r2 = ((r2 += (a2 ^ t3) + i2[3] - 722521979) << 16 | r2 >>> 16) + n2 | 0)) + i2[6] + 76029189) << 23 | t3 >>> 9) + r2 | 0, t3 = ((t3 += ((a2 = (n2 = ((n2 += ((o2 = t3 ^ r2) ^ (e3 = ((e3 += (o2 ^ n2) + i2[9] - 640364487) << 4 | e3 >>> 28) + t3 | 0)) + i2[12] - 421815835) << 11 | n2 >>> 21) + e3 | 0) ^ e3) ^ (r2 = ((r2 += (a2 ^ t3) + i2[15] + 530742520) << 16 | r2 >>> 16) + n2 | 0)) + i2[2] - 995338651) << 23 | t3 >>> 9) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ ((e3 = ((e3 += (r2 ^ (t3 | ~n2)) + i2[0] - 198630844) << 6 | e3 >>> 26) + t3 | 0) | ~r2)) + i2[7] + 1126891415) << 10 | n2 >>> 22) + e3 | 0) ^ ((r2 = ((r2 += (e3 ^ (n2 | ~t3)) + i2[14] - 1416354905) << 15 | r2 >>> 17) + n2 | 0) | ~e3)) + i2[5] - 57434055) << 21 | t3 >>> 11) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ ((e3 = ((e3 += (r2 ^ (t3 | ~n2)) + i2[12] + 1700485571) << 6 | e3 >>> 26) + t3 | 0) | ~r2)) + i2[3] - 1894986606) << 10 | n2 >>> 22) + e3 | 0) ^ ((r2 = ((r2 += (e3 ^ (n2 | ~t3)) + i2[10] - 1051523) << 15 | r2 >>> 17) + n2 | 0) | ~e3)) + i2[1] - 2054922799) << 21 | t3 >>> 11) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ ((e3 = ((e3 += (r2 ^ (t3 | ~n2)) + i2[8] + 1873313359) << 6 | e3 >>> 26) + t3 | 0) | ~r2)) + i2[15] - 30611744) << 10 | n2 >>> 22) + e3 | 0) ^ ((r2 = ((r2 += (e3 ^ (n2 | ~t3)) + i2[6] - 1560198380) << 15 | r2 >>> 17) + n2 | 0) | ~e3)) + i2[13] + 1309151649) << 21 | t3 >>> 11) + r2 | 0, t3 = ((t3 += ((n2 = ((n2 += (t3 ^ ((e3 = ((e3 += (r2 ^ (t3 | ~n2)) + i2[4] - 145523070) << 6 | e3 >>> 26) + t3 | 0) | ~r2)) + i2[11] - 1120210379) << 10 | n2 >>> 22) + e3 | 0) ^ ((r2 = ((r2 += (e3 ^ (n2 | ~t3)) + i2[2] + 718787259) << 15 | r2 >>> 17) + n2 | 0) | ~e3)) + i2[9] - 343485551) << 21 | t3 >>> 11) + r2 | 0, this.first ? (this.h0 = e3 + 1732584193 | 0, this.h1 = t3 - 271733879 | 0, this.h2 = r2 - 1732584194 | 0, this.h3 = n2 + 271733878 | 0, this.first = false) : (this.h0 = this.h0 + e3 | 0, this.h1 = this.h1 + t3 | 0, this.h2 = this.h2 + r2 | 0, this.h3 = this.h3 + n2 | 0);
            }, v.prototype.hex = function() {
              this.finalize();
              var e3 = this.h0, t3 = this.h1, r2 = this.h2, n2 = this.h3;
              return l[e3 >> 4 & 15] + l[15 & e3] + l[e3 >> 12 & 15] + l[e3 >> 8 & 15] + l[e3 >> 20 & 15] + l[e3 >> 16 & 15] + l[e3 >> 28 & 15] + l[e3 >> 24 & 15] + l[t3 >> 4 & 15] + l[15 & t3] + l[t3 >> 12 & 15] + l[t3 >> 8 & 15] + l[t3 >> 20 & 15] + l[t3 >> 16 & 15] + l[t3 >> 28 & 15] + l[t3 >> 24 & 15] + l[r2 >> 4 & 15] + l[15 & r2] + l[r2 >> 12 & 15] + l[r2 >> 8 & 15] + l[r2 >> 20 & 15] + l[r2 >> 16 & 15] + l[r2 >> 28 & 15] + l[r2 >> 24 & 15] + l[n2 >> 4 & 15] + l[15 & n2] + l[n2 >> 12 & 15] + l[n2 >> 8 & 15] + l[n2 >> 20 & 15] + l[n2 >> 16 & 15] + l[n2 >> 28 & 15] + l[n2 >> 24 & 15];
            }, v.prototype.toString = v.prototype.hex, v.prototype.digest = function(e3) {
              if ("hex" === e3) return this.hex();
              this.finalize();
              var t3 = this.h0, r2 = this.h1, n2 = this.h2, o2 = this.h3;
              return [255 & t3, t3 >> 8 & 255, t3 >> 16 & 255, t3 >> 24 & 255, 255 & r2, r2 >> 8 & 255, r2 >> 16 & 255, r2 >> 24 & 255, 255 & n2, n2 >> 8 & 255, n2 >> 16 & 255, n2 >> 24 & 255, 255 & o2, o2 >> 8 & 255, o2 >> 16 & 255, o2 >> 24 & 255];
            }, v.prototype.array = v.prototype.digest, v.prototype.arrayBuffer = function() {
              this.finalize();
              var e3 = new ArrayBuffer(16), t3 = new Uint32Array(e3);
              return t3[0] = this.h0, t3[1] = this.h1, t3[2] = this.h2, t3[3] = this.h3, e3;
            }, v.prototype.buffer = v.prototype.arrayBuffer, v.prototype.base64 = function() {
              for (var e3, t3, r2, n2 = "", o2 = this.array(), a2 = 0; a2 < 15; ) e3 = o2[a2++], t3 = o2[a2++], r2 = o2[a2++], n2 += h[e3 >>> 2] + h[63 & (e3 << 4 | t3 >>> 4)] + h[63 & (t3 << 2 | r2 >>> 6)] + h[63 & r2];
              return e3 = o2[a2], n2 += h[e3 >>> 2] + h[e3 << 4 & 63] + "==";
            };
            var k = function() {
              var e3 = y("hex");
              e3.getCtx = e3.create = function() {
                return new v();
              }, e3.update = function(t4) {
                return e3.create().update(t4);
              };
              for (var t3 = 0; t3 < f.length; ++t3) {
                var r2 = f[t3];
                e3[r2] = y(r2);
              }
              return e3;
            }();
            s ? e2.exports = k : (a.md5 = k, c && (void 0 === (t2 = (function() {
              return k;
            }).call(k, r, k, e2)) || (e2.exports = t2)));
          }();
        }).call(this, r(5)(e));
      }, function(e, t) {
        (function(t2) {
          e.exports = t2;
        }).call(this, {});
      }, function(e, t, r) {
        (function(e2) {
          var t2, n, o, a, i, s, c, u = r(0), l = l || function(e3, t3) {
            var r2 = {}, n2 = r2.lib = {}, o2 = function() {
            }, a2 = n2.Base = { extend: function(e4) {
              o2.prototype = this;
              var t4 = new o2();
              return e4 && t4.mixIn(e4), t4.hasOwnProperty("init") || (t4.init = function() {
                t4.$super.init.apply(this, arguments);
              }), t4.init.prototype = t4, t4.$super = this, t4;
            }, create: function() {
              var e4 = this.extend();
              return e4.init.apply(e4, arguments), e4;
            }, init: function() {
            }, mixIn: function(e4) {
              for (var t4 in e4) e4.hasOwnProperty(t4) && (this[t4] = e4[t4]);
              e4.hasOwnProperty("toString") && (this.toString = e4.toString);
            }, clone: function() {
              return this.init.prototype.extend(this);
            } }, i2 = n2.WordArray = a2.extend({ init: function(e4, t4) {
              e4 = this.words = e4 || [], this.sigBytes = null != t4 ? t4 : 4 * e4.length;
            }, toString: function(e4) {
              return (e4 || c2).stringify(this);
            }, concat: function(e4) {
              var t4 = this.words, r3 = e4.words, n3 = this.sigBytes;
              if (e4 = e4.sigBytes, this.clamp(), n3 % 4) for (var o3 = 0; o3 < e4; o3++) t4[n3 + o3 >>> 2] |= (r3[o3 >>> 2] >>> 24 - o3 % 4 * 8 & 255) << 24 - (n3 + o3) % 4 * 8;
              else if (65535 < r3.length) for (o3 = 0; o3 < e4; o3 += 4) t4[n3 + o3 >>> 2] = r3[o3 >>> 2];
              else t4.push.apply(t4, r3);
              return this.sigBytes += e4, this;
            }, clamp: function() {
              var t4 = this.words, r3 = this.sigBytes;
              t4[r3 >>> 2] &= 4294967295 << 32 - r3 % 4 * 8, t4.length = e3.ceil(r3 / 4);
            }, clone: function() {
              var e4 = a2.clone.call(this);
              return e4.words = this.words.slice(0), e4;
            }, random: function(t4) {
              for (var r3 = [], n3 = 0; n3 < t4; n3 += 4) r3.push(4294967296 * e3.random() | 0);
              return new i2.init(r3, t4);
            } }), s2 = r2.enc = {}, c2 = s2.Hex = { stringify: function(e4) {
              var t4 = e4.words;
              e4 = e4.sigBytes;
              for (var r3 = [], n3 = 0; n3 < e4; n3++) {
                var o3 = t4[n3 >>> 2] >>> 24 - n3 % 4 * 8 & 255;
                r3.push((o3 >>> 4).toString(16)), r3.push((15 & o3).toString(16));
              }
              return r3.join("");
            }, parse: function(e4) {
              for (var t4 = e4.length, r3 = [], n3 = 0; n3 < t4; n3 += 2) r3[n3 >>> 3] |= parseInt(e4.substr(n3, 2), 16) << 24 - n3 % 8 * 4;
              return new i2.init(r3, t4 / 2);
            } }, u2 = s2.Latin1 = { stringify: function(e4) {
              var t4 = e4.words;
              e4 = e4.sigBytes;
              for (var r3 = [], n3 = 0; n3 < e4; n3++) r3.push(String.fromCharCode(t4[n3 >>> 2] >>> 24 - n3 % 4 * 8 & 255));
              return r3.join("");
            }, parse: function(e4) {
              for (var t4 = e4.length, r3 = [], n3 = 0; n3 < t4; n3++) r3[n3 >>> 2] |= (255 & e4.charCodeAt(n3)) << 24 - n3 % 4 * 8;
              return new i2.init(r3, t4);
            } }, l2 = s2.Utf8 = { stringify: function(e4) {
              try {
                return decodeURIComponent(escape(u2.stringify(e4)));
              } catch (e5) {
                throw Error("Malformed UTF-8 data");
              }
            }, parse: function(e4) {
              return u2.parse(unescape(encodeURIComponent(e4)));
            } }, d = n2.BufferedBlockAlgorithm = a2.extend({ reset: function() {
              this._data = new i2.init(), this._nDataBytes = 0;
            }, _append: function(e4) {
              "string" == typeof e4 && (e4 = l2.parse(e4)), this._data.concat(e4), this._nDataBytes += e4.sigBytes;
            }, _process: function(t4) {
              var r3 = this._data, n3 = r3.words, o3 = r3.sigBytes, a3 = this.blockSize, s3 = o3 / (4 * a3);
              if (t4 = (s3 = t4 ? e3.ceil(s3) : e3.max((0 | s3) - this._minBufferSize, 0)) * a3, o3 = e3.min(4 * t4, o3), t4) {
                for (var c3 = 0; c3 < t4; c3 += a3) this._doProcessBlock(n3, c3);
                c3 = n3.splice(0, t4), r3.sigBytes -= o3;
              }
              return new i2.init(c3, o3);
            }, clone: function() {
              var e4 = a2.clone.call(this);
              return e4._data = this._data.clone(), e4;
            }, _minBufferSize: 0 });
            n2.Hasher = d.extend({ cfg: a2.extend(), init: function(e4) {
              this.cfg = this.cfg.extend(e4), this.reset();
            }, reset: function() {
              d.reset.call(this), this._doReset();
            }, update: function(e4) {
              return this._append(e4), this._process(), this;
            }, finalize: function(e4) {
              return e4 && this._append(e4), this._doFinalize();
            }, blockSize: 16, _createHelper: function(e4) {
              return function(t4, r3) {
                return new e4.init(r3).finalize(t4);
              };
            }, _createHmacHelper: function(e4) {
              return function(t4, r3) {
                return new p.HMAC.init(e4, r3).finalize(t4);
              };
            } });
            var p = r2.algo = {};
            return r2;
          }(Math);
          n = (i = (t2 = l).lib).WordArray, o = i.Hasher, a = [], i = t2.algo.SHA1 = o.extend({ _doReset: function() {
            this._hash = new n.init([1732584193, 4023233417, 2562383102, 271733878, 3285377520]);
          }, _doProcessBlock: function(e3, t3) {
            for (var r2 = this._hash.words, n2 = r2[0], o2 = r2[1], i2 = r2[2], s2 = r2[3], c2 = r2[4], u2 = 0; 80 > u2; u2++) {
              if (16 > u2) a[u2] = 0 | e3[t3 + u2];
              else {
                var l2 = a[u2 - 3] ^ a[u2 - 8] ^ a[u2 - 14] ^ a[u2 - 16];
                a[u2] = l2 << 1 | l2 >>> 31;
              }
              l2 = (n2 << 5 | n2 >>> 27) + c2 + a[u2], l2 = 20 > u2 ? l2 + (1518500249 + (o2 & i2 | ~o2 & s2)) : 40 > u2 ? l2 + (1859775393 + (o2 ^ i2 ^ s2)) : 60 > u2 ? l2 + ((o2 & i2 | o2 & s2 | i2 & s2) - 1894007588) : l2 + ((o2 ^ i2 ^ s2) - 899497514), c2 = s2, s2 = i2, i2 = o2 << 30 | o2 >>> 2, o2 = n2, n2 = l2;
            }
            r2[0] = r2[0] + n2 | 0, r2[1] = r2[1] + o2 | 0, r2[2] = r2[2] + i2 | 0, r2[3] = r2[3] + s2 | 0, r2[4] = r2[4] + c2 | 0;
          }, _doFinalize: function() {
            var e3 = this._data, t3 = e3.words, r2 = 8 * this._nDataBytes, n2 = 8 * e3.sigBytes;
            return t3[n2 >>> 5] |= 128 << 24 - n2 % 32, t3[14 + (n2 + 64 >>> 9 << 4)] = Math.floor(r2 / 4294967296), t3[15 + (n2 + 64 >>> 9 << 4)] = r2, e3.sigBytes = 4 * t3.length, this._process(), this._hash;
          }, clone: function() {
            var e3 = o.clone.call(this);
            return e3._hash = this._hash.clone(), e3;
          } }), t2.SHA1 = o._createHelper(i), t2.HmacSHA1 = o._createHmacHelper(i), function() {
            var e3 = l, t3 = e3.enc.Utf8;
            e3.algo.HMAC = e3.lib.Base.extend({ init: function(e4, r2) {
              e4 = this._hasher = new e4.init(), "string" == typeof r2 && (r2 = t3.parse(r2));
              var n2 = e4.blockSize, o2 = 4 * n2;
              r2.sigBytes > o2 && (r2 = e4.finalize(r2)), r2.clamp();
              for (var a2 = this._oKey = r2.clone(), i2 = this._iKey = r2.clone(), s2 = a2.words, c2 = i2.words, u2 = 0; u2 < n2; u2++) s2[u2] ^= 1549556828, c2[u2] ^= 909522486;
              a2.sigBytes = i2.sigBytes = o2, this.reset();
            }, reset: function() {
              var e4 = this._hasher;
              e4.reset(), e4.update(this._iKey);
            }, update: function(e4) {
              return this._hasher.update(e4), this;
            }, finalize: function(e4) {
              var t4 = this._hasher;
              return e4 = t4.finalize(e4), t4.reset(), t4.finalize(this._oKey.clone().concat(e4));
            } });
          }(), c = (s = l).lib.WordArray, s.enc.Base64 = { stringify: function(e3) {
            var t3 = e3.words, r2 = e3.sigBytes, n2 = this._map;
            e3.clamp();
            for (var o2 = [], a2 = 0; a2 < r2; a2 += 3) for (var i2 = (t3[a2 >>> 2] >>> 24 - a2 % 4 * 8 & 255) << 16 | (t3[a2 + 1 >>> 2] >>> 24 - (a2 + 1) % 4 * 8 & 255) << 8 | t3[a2 + 2 >>> 2] >>> 24 - (a2 + 2) % 4 * 8 & 255, s2 = 0; s2 < 4 && a2 + 0.75 * s2 < r2; s2++) o2.push(n2.charAt(i2 >>> 6 * (3 - s2) & 63));
            var c2 = n2.charAt(64);
            if (c2) for (; o2.length % 4; ) o2.push(c2);
            return o2.join("");
          }, parse: function(e3) {
            var t3 = e3.length, r2 = this._map, n2 = r2.charAt(64);
            if (n2) {
              var o2 = e3.indexOf(n2);
              -1 != o2 && (t3 = o2);
            }
            for (var a2 = [], i2 = 0, s2 = 0; s2 < t3; s2++) if (s2 % 4) {
              var u2 = r2.indexOf(e3.charAt(s2 - 1)) << s2 % 4 * 2, l2 = r2.indexOf(e3.charAt(s2)) >>> 6 - s2 % 4 * 2;
              a2[i2 >>> 2] |= (u2 | l2) << 24 - i2 % 4 * 8, i2++;
            }
            return c.create(a2, i2);
          }, _map: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=" }, "object" === u(e2) ? e2.exports = l : window.CryptoJS = l;
        }).call(this, r(5)(e));
      }, function(e, t, r) {
        var n = r(21).DOMParser, o = (function() {
          this.version = "1.3.5";
          var e2 = { mergeCDATA: true, normalize: true, stripElemPrefix: true }, t2 = new RegExp(/(?!xmlns)^.*:/);
          new RegExp(/^\s+|\s+$/g);
          return this.grokType = function(e3) {
            return /^\s*$/.test(e3) ? null : /^(?:true|false)$/i.test(e3) ? "true" === e3.toLowerCase() : isFinite(e3) ? parseFloat(e3) : e3;
          }, this.parseString = function(e3, t3) {
            if (e3) {
              var r2 = this.stringToXML(e3);
              return r2.getElementsByTagName("parsererror").length ? null : this.parseXML(r2, t3);
            }
            return null;
          }, this.parseXML = function(r2, n2) {
            for (var a in n2) e2[a] = n2[a];
            var i = {}, s = 0, c = "";
            if (r2.childNodes.length) for (var u, l, d, p = 0; p < r2.childNodes.length; p++) 4 === (u = r2.childNodes.item(p)).nodeType ? e2.mergeCDATA && (c += u.nodeValue) : 3 === u.nodeType ? c += u.nodeValue : 1 === u.nodeType && (0 === s && (i = {}), l = e2.stripElemPrefix ? u.nodeName.replace(t2, "") : u.nodeName, d = o.parseXML(u), i.hasOwnProperty(l) ? (i[l].constructor !== Array && (i[l] = [i[l]]), i[l].push(d)) : (i[l] = d, s++));
            return Object.keys(i).length || (i = c || ""), i;
          }, this.xmlToString = function(e3) {
            try {
              return e3.xml ? e3.xml : new XMLSerializer().serializeToString(e3);
            } catch (e4) {
              return null;
            }
          }, this.stringToXML = function(e3) {
            try {
              var t3 = null;
              return window.DOMParser ? t3 = new n().parseFromString(e3, "text/xml") : ((t3 = new ActiveXObject("Microsoft.XMLDOM")).async = false, t3.loadXML(e3), t3);
            } catch (e4) {
              return null;
            }
          }, this;
        }).call({});
        e.exports = function(e2) {
          return o.parseString(e2);
        };
      }, function(e, t, r) {
        var n = r(6);
        t.DOMImplementation = n.DOMImplementation, t.XMLSerializer = n.XMLSerializer, t.DOMParser = r(22).DOMParser;
      }, function(e, t, r) {
        var n = r(2), o = r(6), a = r(23), i = r(24), s = o.DOMImplementation, c = n.NAMESPACE, u = i.ParseError, l = i.XMLReader;
        function d(e2) {
          return e2.replace(/\r[\n\u0085]/g, "\n").replace(/[\r\u0085\u2028]/g, "\n");
        }
        function p(e2) {
          this.options = e2 || { locator: {} };
        }
        function f() {
          this.cdata = false;
        }
        function h(e2, t2) {
          t2.lineNumber = e2.lineNumber, t2.columnNumber = e2.columnNumber;
        }
        function m(e2, t2, r2) {
          return "string" == typeof e2 ? e2.substr(t2, r2) : e2.length >= t2 + r2 || t2 ? new java.lang.String(e2, t2, r2) + "" : e2;
        }
        function g(e2, t2) {
          e2.currentElement ? e2.currentElement.appendChild(t2) : e2.doc.appendChild(t2);
        }
        p.prototype.parseFromString = function(e2, t2) {
          var r2 = this.options, n2 = new l(), o2 = r2.domBuilder || new f(), i2 = r2.errorHandler, s2 = r2.locator, u2 = r2.xmlns || {}, p2 = /\/x?html?$/.test(t2), h2 = p2 ? a.HTML_ENTITIES : a.XML_ENTITIES;
          s2 && o2.setDocumentLocator(s2), n2.errorHandler = function(e3, t3, r3) {
            if (!e3) {
              if (t3 instanceof f) return t3;
              e3 = t3;
            }
            var n3 = {}, o3 = e3 instanceof Function;
            function a2(t4) {
              var a3 = e3[t4];
              !a3 && o3 && (a3 = 2 == e3.length ? function(r4) {
                e3(t4, r4);
              } : e3), n3[t4] = a3 && function(e4) {
                a3("[xmldom " + t4 + "]	" + e4 + function(e5) {
                  if (e5) return "\n@" + (e5.systemId || "") + "#[line:" + e5.lineNumber + ",col:" + e5.columnNumber + "]";
                }(r3));
              } || function() {
              };
            }
            return r3 = r3 || {}, a2("warning"), a2("error"), a2("fatalError"), n3;
          }(i2, o2, s2), n2.domBuilder = r2.domBuilder || o2, p2 && (u2[""] = c.HTML), u2.xml = u2.xml || c.XML;
          var m2 = r2.normalizeLineEndings || d;
          return e2 && "string" == typeof e2 ? n2.parse(m2(e2), u2, h2) : n2.errorHandler.error("invalid doc source"), o2.doc;
        }, f.prototype = { startDocument: function() {
          this.doc = new s().createDocument(null, null, null), this.locator && (this.doc.documentURI = this.locator.systemId);
        }, startElement: function(e2, t2, r2, n2) {
          var o2 = this.doc, a2 = o2.createElementNS(e2, r2 || t2), i2 = n2.length;
          g(this, a2), this.currentElement = a2, this.locator && h(this.locator, a2);
          for (var s2 = 0; s2 < i2; s2++) {
            e2 = n2.getURI(s2);
            var c2 = n2.getValue(s2), u2 = (r2 = n2.getQName(s2), o2.createAttributeNS(e2, r2));
            this.locator && h(n2.getLocator(s2), u2), u2.value = u2.nodeValue = c2, a2.setAttributeNode(u2);
          }
        }, endElement: function(e2, t2, r2) {
          var n2 = this.currentElement;
          n2.tagName;
          this.currentElement = n2.parentNode;
        }, startPrefixMapping: function(e2, t2) {
        }, endPrefixMapping: function(e2) {
        }, processingInstruction: function(e2, t2) {
          var r2 = this.doc.createProcessingInstruction(e2, t2);
          this.locator && h(this.locator, r2), g(this, r2);
        }, ignorableWhitespace: function(e2, t2, r2) {
        }, characters: function(e2, t2, r2) {
          if (e2 = m.apply(this, arguments)) {
            if (this.cdata) var n2 = this.doc.createCDATASection(e2);
            else n2 = this.doc.createTextNode(e2);
            this.currentElement ? this.currentElement.appendChild(n2) : /^\s*$/.test(e2) && this.doc.appendChild(n2), this.locator && h(this.locator, n2);
          }
        }, skippedEntity: function(e2) {
        }, endDocument: function() {
          this.doc.normalize();
        }, setDocumentLocator: function(e2) {
          (this.locator = e2) && (e2.lineNumber = 0);
        }, comment: function(e2, t2, r2) {
          e2 = m.apply(this, arguments);
          var n2 = this.doc.createComment(e2);
          this.locator && h(this.locator, n2), g(this, n2);
        }, startCDATA: function() {
          this.cdata = true;
        }, endCDATA: function() {
          this.cdata = false;
        }, startDTD: function(e2, t2, r2) {
          var n2 = this.doc.implementation;
          if (n2 && n2.createDocumentType) {
            var o2 = n2.createDocumentType(e2, t2, r2);
            this.locator && h(this.locator, o2), g(this, o2), this.doc.doctype = o2;
          }
        }, warning: function(e2) {
        }, error: function(e2) {
        }, fatalError: function(e2) {
          throw new u(e2, this.locator);
        } }, "endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g, function(e2) {
          f.prototype[e2] = function() {
            return null;
          };
        }), t.__DOMHandler = f, t.normalizeLineEndings = d, t.DOMParser = p;
      }, function(e, t, r) {
        "use strict";
        var n = r(2).freeze;
        t.XML_ENTITIES = n({ amp: "&", apos: "'", gt: ">", lt: "<", quot: '"' }), t.HTML_ENTITIES = n({ Aacute: "Á", aacute: "á", Abreve: "Ă", abreve: "ă", ac: "∾", acd: "∿", acE: "∾̳", Acirc: "Â", acirc: "â", acute: "´", Acy: "А", acy: "а", AElig: "Æ", aelig: "æ", af: "⁡", Afr: "𝔄", afr: "𝔞", Agrave: "À", agrave: "à", alefsym: "ℵ", aleph: "ℵ", Alpha: "Α", alpha: "α", Amacr: "Ā", amacr: "ā", amalg: "⨿", AMP: "&", amp: "&", And: "⩓", and: "∧", andand: "⩕", andd: "⩜", andslope: "⩘", andv: "⩚", ang: "∠", ange: "⦤", angle: "∠", angmsd: "∡", angmsdaa: "⦨", angmsdab: "⦩", angmsdac: "⦪", angmsdad: "⦫", angmsdae: "⦬", angmsdaf: "⦭", angmsdag: "⦮", angmsdah: "⦯", angrt: "∟", angrtvb: "⊾", angrtvbd: "⦝", angsph: "∢", angst: "Å", angzarr: "⍼", Aogon: "Ą", aogon: "ą", Aopf: "𝔸", aopf: "𝕒", ap: "≈", apacir: "⩯", apE: "⩰", ape: "≊", apid: "≋", apos: "'", ApplyFunction: "⁡", approx: "≈", approxeq: "≊", Aring: "Å", aring: "å", Ascr: "𝒜", ascr: "𝒶", Assign: "≔", ast: "*", asymp: "≈", asympeq: "≍", Atilde: "Ã", atilde: "ã", Auml: "Ä", auml: "ä", awconint: "∳", awint: "⨑", backcong: "≌", backepsilon: "϶", backprime: "‵", backsim: "∽", backsimeq: "⋍", Backslash: "∖", Barv: "⫧", barvee: "⊽", Barwed: "⌆", barwed: "⌅", barwedge: "⌅", bbrk: "⎵", bbrktbrk: "⎶", bcong: "≌", Bcy: "Б", bcy: "б", bdquo: "„", becaus: "∵", Because: "∵", because: "∵", bemptyv: "⦰", bepsi: "϶", bernou: "ℬ", Bernoullis: "ℬ", Beta: "Β", beta: "β", beth: "ℶ", between: "≬", Bfr: "𝔅", bfr: "𝔟", bigcap: "⋂", bigcirc: "◯", bigcup: "⋃", bigodot: "⨀", bigoplus: "⨁", bigotimes: "⨂", bigsqcup: "⨆", bigstar: "★", bigtriangledown: "▽", bigtriangleup: "△", biguplus: "⨄", bigvee: "⋁", bigwedge: "⋀", bkarow: "⤍", blacklozenge: "⧫", blacksquare: "▪", blacktriangle: "▴", blacktriangledown: "▾", blacktriangleleft: "◂", blacktriangleright: "▸", blank: "␣", blk12: "▒", blk14: "░", blk34: "▓", block: "█", bne: "=⃥", bnequiv: "≡⃥", bNot: "⫭", bnot: "⌐", Bopf: "𝔹", bopf: "𝕓", bot: "⊥", bottom: "⊥", bowtie: "⋈", boxbox: "⧉", boxDL: "╗", boxDl: "╖", boxdL: "╕", boxdl: "┐", boxDR: "╔", boxDr: "╓", boxdR: "╒", boxdr: "┌", boxH: "═", boxh: "─", boxHD: "╦", boxHd: "╤", boxhD: "╥", boxhd: "┬", boxHU: "╩", boxHu: "╧", boxhU: "╨", boxhu: "┴", boxminus: "⊟", boxplus: "⊞", boxtimes: "⊠", boxUL: "╝", boxUl: "╜", boxuL: "╛", boxul: "┘", boxUR: "╚", boxUr: "╙", boxuR: "╘", boxur: "└", boxV: "║", boxv: "│", boxVH: "╬", boxVh: "╫", boxvH: "╪", boxvh: "┼", boxVL: "╣", boxVl: "╢", boxvL: "╡", boxvl: "┤", boxVR: "╠", boxVr: "╟", boxvR: "╞", boxvr: "├", bprime: "‵", Breve: "˘", breve: "˘", brvbar: "¦", Bscr: "ℬ", bscr: "𝒷", bsemi: "⁏", bsim: "∽", bsime: "⋍", bsol: "\\", bsolb: "⧅", bsolhsub: "⟈", bull: "•", bullet: "•", bump: "≎", bumpE: "⪮", bumpe: "≏", Bumpeq: "≎", bumpeq: "≏", Cacute: "Ć", cacute: "ć", Cap: "⋒", cap: "∩", capand: "⩄", capbrcup: "⩉", capcap: "⩋", capcup: "⩇", capdot: "⩀", CapitalDifferentialD: "ⅅ", caps: "∩︀", caret: "⁁", caron: "ˇ", Cayleys: "ℭ", ccaps: "⩍", Ccaron: "Č", ccaron: "č", Ccedil: "Ç", ccedil: "ç", Ccirc: "Ĉ", ccirc: "ĉ", Cconint: "∰", ccups: "⩌", ccupssm: "⩐", Cdot: "Ċ", cdot: "ċ", cedil: "¸", Cedilla: "¸", cemptyv: "⦲", cent: "¢", CenterDot: "·", centerdot: "·", Cfr: "ℭ", cfr: "𝔠", CHcy: "Ч", chcy: "ч", check: "✓", checkmark: "✓", Chi: "Χ", chi: "χ", cir: "○", circ: "ˆ", circeq: "≗", circlearrowleft: "↺", circlearrowright: "↻", circledast: "⊛", circledcirc: "⊚", circleddash: "⊝", CircleDot: "⊙", circledR: "®", circledS: "Ⓢ", CircleMinus: "⊖", CirclePlus: "⊕", CircleTimes: "⊗", cirE: "⧃", cire: "≗", cirfnint: "⨐", cirmid: "⫯", cirscir: "⧂", ClockwiseContourIntegral: "∲", CloseCurlyDoubleQuote: "”", CloseCurlyQuote: "’", clubs: "♣", clubsuit: "♣", Colon: "∷", colon: ":", Colone: "⩴", colone: "≔", coloneq: "≔", comma: ",", commat: "@", comp: "∁", compfn: "∘", complement: "∁", complexes: "ℂ", cong: "≅", congdot: "⩭", Congruent: "≡", Conint: "∯", conint: "∮", ContourIntegral: "∮", Copf: "ℂ", copf: "𝕔", coprod: "∐", Coproduct: "∐", COPY: "©", copy: "©", copysr: "℗", CounterClockwiseContourIntegral: "∳", crarr: "↵", Cross: "⨯", cross: "✗", Cscr: "𝒞", cscr: "𝒸", csub: "⫏", csube: "⫑", csup: "⫐", csupe: "⫒", ctdot: "⋯", cudarrl: "⤸", cudarrr: "⤵", cuepr: "⋞", cuesc: "⋟", cularr: "↶", cularrp: "⤽", Cup: "⋓", cup: "∪", cupbrcap: "⩈", CupCap: "≍", cupcap: "⩆", cupcup: "⩊", cupdot: "⊍", cupor: "⩅", cups: "∪︀", curarr: "↷", curarrm: "⤼", curlyeqprec: "⋞", curlyeqsucc: "⋟", curlyvee: "⋎", curlywedge: "⋏", curren: "¤", curvearrowleft: "↶", curvearrowright: "↷", cuvee: "⋎", cuwed: "⋏", cwconint: "∲", cwint: "∱", cylcty: "⌭", Dagger: "‡", dagger: "†", daleth: "ℸ", Darr: "↡", dArr: "⇓", darr: "↓", dash: "‐", Dashv: "⫤", dashv: "⊣", dbkarow: "⤏", dblac: "˝", Dcaron: "Ď", dcaron: "ď", Dcy: "Д", dcy: "д", DD: "ⅅ", dd: "ⅆ", ddagger: "‡", ddarr: "⇊", DDotrahd: "⤑", ddotseq: "⩷", deg: "°", Del: "∇", Delta: "Δ", delta: "δ", demptyv: "⦱", dfisht: "⥿", Dfr: "𝔇", dfr: "𝔡", dHar: "⥥", dharl: "⇃", dharr: "⇂", DiacriticalAcute: "´", DiacriticalDot: "˙", DiacriticalDoubleAcute: "˝", DiacriticalGrave: "`", DiacriticalTilde: "˜", diam: "⋄", Diamond: "⋄", diamond: "⋄", diamondsuit: "♦", diams: "♦", die: "¨", DifferentialD: "ⅆ", digamma: "ϝ", disin: "⋲", div: "÷", divide: "÷", divideontimes: "⋇", divonx: "⋇", DJcy: "Ђ", djcy: "ђ", dlcorn: "⌞", dlcrop: "⌍", dollar: "$", Dopf: "𝔻", dopf: "𝕕", Dot: "¨", dot: "˙", DotDot: "⃜", doteq: "≐", doteqdot: "≑", DotEqual: "≐", dotminus: "∸", dotplus: "∔", dotsquare: "⊡", doublebarwedge: "⌆", DoubleContourIntegral: "∯", DoubleDot: "¨", DoubleDownArrow: "⇓", DoubleLeftArrow: "⇐", DoubleLeftRightArrow: "⇔", DoubleLeftTee: "⫤", DoubleLongLeftArrow: "⟸", DoubleLongLeftRightArrow: "⟺", DoubleLongRightArrow: "⟹", DoubleRightArrow: "⇒", DoubleRightTee: "⊨", DoubleUpArrow: "⇑", DoubleUpDownArrow: "⇕", DoubleVerticalBar: "∥", DownArrow: "↓", Downarrow: "⇓", downarrow: "↓", DownArrowBar: "⤓", DownArrowUpArrow: "⇵", DownBreve: "̑", downdownarrows: "⇊", downharpoonleft: "⇃", downharpoonright: "⇂", DownLeftRightVector: "⥐", DownLeftTeeVector: "⥞", DownLeftVector: "↽", DownLeftVectorBar: "⥖", DownRightTeeVector: "⥟", DownRightVector: "⇁", DownRightVectorBar: "⥗", DownTee: "⊤", DownTeeArrow: "↧", drbkarow: "⤐", drcorn: "⌟", drcrop: "⌌", Dscr: "𝒟", dscr: "𝒹", DScy: "Ѕ", dscy: "ѕ", dsol: "⧶", Dstrok: "Đ", dstrok: "đ", dtdot: "⋱", dtri: "▿", dtrif: "▾", duarr: "⇵", duhar: "⥯", dwangle: "⦦", DZcy: "Џ", dzcy: "џ", dzigrarr: "⟿", Eacute: "É", eacute: "é", easter: "⩮", Ecaron: "Ě", ecaron: "ě", ecir: "≖", Ecirc: "Ê", ecirc: "ê", ecolon: "≕", Ecy: "Э", ecy: "э", eDDot: "⩷", Edot: "Ė", eDot: "≑", edot: "ė", ee: "ⅇ", efDot: "≒", Efr: "𝔈", efr: "𝔢", eg: "⪚", Egrave: "È", egrave: "è", egs: "⪖", egsdot: "⪘", el: "⪙", Element: "∈", elinters: "⏧", ell: "ℓ", els: "⪕", elsdot: "⪗", Emacr: "Ē", emacr: "ē", empty: "∅", emptyset: "∅", EmptySmallSquare: "◻", emptyv: "∅", EmptyVerySmallSquare: "▫", emsp: " ", emsp13: " ", emsp14: " ", ENG: "Ŋ", eng: "ŋ", ensp: " ", Eogon: "Ę", eogon: "ę", Eopf: "𝔼", eopf: "𝕖", epar: "⋕", eparsl: "⧣", eplus: "⩱", epsi: "ε", Epsilon: "Ε", epsilon: "ε", epsiv: "ϵ", eqcirc: "≖", eqcolon: "≕", eqsim: "≂", eqslantgtr: "⪖", eqslantless: "⪕", Equal: "⩵", equals: "=", EqualTilde: "≂", equest: "≟", Equilibrium: "⇌", equiv: "≡", equivDD: "⩸", eqvparsl: "⧥", erarr: "⥱", erDot: "≓", Escr: "ℰ", escr: "ℯ", esdot: "≐", Esim: "⩳", esim: "≂", Eta: "Η", eta: "η", ETH: "Ð", eth: "ð", Euml: "Ë", euml: "ë", euro: "€", excl: "!", exist: "∃", Exists: "∃", expectation: "ℰ", ExponentialE: "ⅇ", exponentiale: "ⅇ", fallingdotseq: "≒", Fcy: "Ф", fcy: "ф", female: "♀", ffilig: "ﬃ", fflig: "ﬀ", ffllig: "ﬄ", Ffr: "𝔉", ffr: "𝔣", filig: "ﬁ", FilledSmallSquare: "◼", FilledVerySmallSquare: "▪", fjlig: "fj", flat: "♭", fllig: "ﬂ", fltns: "▱", fnof: "ƒ", Fopf: "𝔽", fopf: "𝕗", ForAll: "∀", forall: "∀", fork: "⋔", forkv: "⫙", Fouriertrf: "ℱ", fpartint: "⨍", frac12: "½", frac13: "⅓", frac14: "¼", frac15: "⅕", frac16: "⅙", frac18: "⅛", frac23: "⅔", frac25: "⅖", frac34: "¾", frac35: "⅗", frac38: "⅜", frac45: "⅘", frac56: "⅚", frac58: "⅝", frac78: "⅞", frasl: "⁄", frown: "⌢", Fscr: "ℱ", fscr: "𝒻", gacute: "ǵ", Gamma: "Γ", gamma: "γ", Gammad: "Ϝ", gammad: "ϝ", gap: "⪆", Gbreve: "Ğ", gbreve: "ğ", Gcedil: "Ģ", Gcirc: "Ĝ", gcirc: "ĝ", Gcy: "Г", gcy: "г", Gdot: "Ġ", gdot: "ġ", gE: "≧", ge: "≥", gEl: "⪌", gel: "⋛", geq: "≥", geqq: "≧", geqslant: "⩾", ges: "⩾", gescc: "⪩", gesdot: "⪀", gesdoto: "⪂", gesdotol: "⪄", gesl: "⋛︀", gesles: "⪔", Gfr: "𝔊", gfr: "𝔤", Gg: "⋙", gg: "≫", ggg: "⋙", gimel: "ℷ", GJcy: "Ѓ", gjcy: "ѓ", gl: "≷", gla: "⪥", glE: "⪒", glj: "⪤", gnap: "⪊", gnapprox: "⪊", gnE: "≩", gne: "⪈", gneq: "⪈", gneqq: "≩", gnsim: "⋧", Gopf: "𝔾", gopf: "𝕘", grave: "`", GreaterEqual: "≥", GreaterEqualLess: "⋛", GreaterFullEqual: "≧", GreaterGreater: "⪢", GreaterLess: "≷", GreaterSlantEqual: "⩾", GreaterTilde: "≳", Gscr: "𝒢", gscr: "ℊ", gsim: "≳", gsime: "⪎", gsiml: "⪐", Gt: "≫", GT: ">", gt: ">", gtcc: "⪧", gtcir: "⩺", gtdot: "⋗", gtlPar: "⦕", gtquest: "⩼", gtrapprox: "⪆", gtrarr: "⥸", gtrdot: "⋗", gtreqless: "⋛", gtreqqless: "⪌", gtrless: "≷", gtrsim: "≳", gvertneqq: "≩︀", gvnE: "≩︀", Hacek: "ˇ", hairsp: " ", half: "½", hamilt: "ℋ", HARDcy: "Ъ", hardcy: "ъ", hArr: "⇔", harr: "↔", harrcir: "⥈", harrw: "↭", Hat: "^", hbar: "ℏ", Hcirc: "Ĥ", hcirc: "ĥ", hearts: "♥", heartsuit: "♥", hellip: "…", hercon: "⊹", Hfr: "ℌ", hfr: "𝔥", HilbertSpace: "ℋ", hksearow: "⤥", hkswarow: "⤦", hoarr: "⇿", homtht: "∻", hookleftarrow: "↩", hookrightarrow: "↪", Hopf: "ℍ", hopf: "𝕙", horbar: "―", HorizontalLine: "─", Hscr: "ℋ", hscr: "𝒽", hslash: "ℏ", Hstrok: "Ħ", hstrok: "ħ", HumpDownHump: "≎", HumpEqual: "≏", hybull: "⁃", hyphen: "‐", Iacute: "Í", iacute: "í", ic: "⁣", Icirc: "Î", icirc: "î", Icy: "И", icy: "и", Idot: "İ", IEcy: "Е", iecy: "е", iexcl: "¡", iff: "⇔", Ifr: "ℑ", ifr: "𝔦", Igrave: "Ì", igrave: "ì", ii: "ⅈ", iiiint: "⨌", iiint: "∭", iinfin: "⧜", iiota: "℩", IJlig: "Ĳ", ijlig: "ĳ", Im: "ℑ", Imacr: "Ī", imacr: "ī", image: "ℑ", ImaginaryI: "ⅈ", imagline: "ℐ", imagpart: "ℑ", imath: "ı", imof: "⊷", imped: "Ƶ", Implies: "⇒", in: "∈", incare: "℅", infin: "∞", infintie: "⧝", inodot: "ı", Int: "∬", int: "∫", intcal: "⊺", integers: "ℤ", Integral: "∫", intercal: "⊺", Intersection: "⋂", intlarhk: "⨗", intprod: "⨼", InvisibleComma: "⁣", InvisibleTimes: "⁢", IOcy: "Ё", iocy: "ё", Iogon: "Į", iogon: "į", Iopf: "𝕀", iopf: "𝕚", Iota: "Ι", iota: "ι", iprod: "⨼", iquest: "¿", Iscr: "ℐ", iscr: "𝒾", isin: "∈", isindot: "⋵", isinE: "⋹", isins: "⋴", isinsv: "⋳", isinv: "∈", it: "⁢", Itilde: "Ĩ", itilde: "ĩ", Iukcy: "І", iukcy: "і", Iuml: "Ï", iuml: "ï", Jcirc: "Ĵ", jcirc: "ĵ", Jcy: "Й", jcy: "й", Jfr: "𝔍", jfr: "𝔧", jmath: "ȷ", Jopf: "𝕁", jopf: "𝕛", Jscr: "𝒥", jscr: "𝒿", Jsercy: "Ј", jsercy: "ј", Jukcy: "Є", jukcy: "є", Kappa: "Κ", kappa: "κ", kappav: "ϰ", Kcedil: "Ķ", kcedil: "ķ", Kcy: "К", kcy: "к", Kfr: "𝔎", kfr: "𝔨", kgreen: "ĸ", KHcy: "Х", khcy: "х", KJcy: "Ќ", kjcy: "ќ", Kopf: "𝕂", kopf: "𝕜", Kscr: "𝒦", kscr: "𝓀", lAarr: "⇚", Lacute: "Ĺ", lacute: "ĺ", laemptyv: "⦴", lagran: "ℒ", Lambda: "Λ", lambda: "λ", Lang: "⟪", lang: "⟨", langd: "⦑", langle: "⟨", lap: "⪅", Laplacetrf: "ℒ", laquo: "«", Larr: "↞", lArr: "⇐", larr: "←", larrb: "⇤", larrbfs: "⤟", larrfs: "⤝", larrhk: "↩", larrlp: "↫", larrpl: "⤹", larrsim: "⥳", larrtl: "↢", lat: "⪫", lAtail: "⤛", latail: "⤙", late: "⪭", lates: "⪭︀", lBarr: "⤎", lbarr: "⤌", lbbrk: "❲", lbrace: "{", lbrack: "[", lbrke: "⦋", lbrksld: "⦏", lbrkslu: "⦍", Lcaron: "Ľ", lcaron: "ľ", Lcedil: "Ļ", lcedil: "ļ", lceil: "⌈", lcub: "{", Lcy: "Л", lcy: "л", ldca: "⤶", ldquo: "“", ldquor: "„", ldrdhar: "⥧", ldrushar: "⥋", ldsh: "↲", lE: "≦", le: "≤", LeftAngleBracket: "⟨", LeftArrow: "←", Leftarrow: "⇐", leftarrow: "←", LeftArrowBar: "⇤", LeftArrowRightArrow: "⇆", leftarrowtail: "↢", LeftCeiling: "⌈", LeftDoubleBracket: "⟦", LeftDownTeeVector: "⥡", LeftDownVector: "⇃", LeftDownVectorBar: "⥙", LeftFloor: "⌊", leftharpoondown: "↽", leftharpoonup: "↼", leftleftarrows: "⇇", LeftRightArrow: "↔", Leftrightarrow: "⇔", leftrightarrow: "↔", leftrightarrows: "⇆", leftrightharpoons: "⇋", leftrightsquigarrow: "↭", LeftRightVector: "⥎", LeftTee: "⊣", LeftTeeArrow: "↤", LeftTeeVector: "⥚", leftthreetimes: "⋋", LeftTriangle: "⊲", LeftTriangleBar: "⧏", LeftTriangleEqual: "⊴", LeftUpDownVector: "⥑", LeftUpTeeVector: "⥠", LeftUpVector: "↿", LeftUpVectorBar: "⥘", LeftVector: "↼", LeftVectorBar: "⥒", lEg: "⪋", leg: "⋚", leq: "≤", leqq: "≦", leqslant: "⩽", les: "⩽", lescc: "⪨", lesdot: "⩿", lesdoto: "⪁", lesdotor: "⪃", lesg: "⋚︀", lesges: "⪓", lessapprox: "⪅", lessdot: "⋖", lesseqgtr: "⋚", lesseqqgtr: "⪋", LessEqualGreater: "⋚", LessFullEqual: "≦", LessGreater: "≶", lessgtr: "≶", LessLess: "⪡", lesssim: "≲", LessSlantEqual: "⩽", LessTilde: "≲", lfisht: "⥼", lfloor: "⌊", Lfr: "𝔏", lfr: "𝔩", lg: "≶", lgE: "⪑", lHar: "⥢", lhard: "↽", lharu: "↼", lharul: "⥪", lhblk: "▄", LJcy: "Љ", ljcy: "љ", Ll: "⋘", ll: "≪", llarr: "⇇", llcorner: "⌞", Lleftarrow: "⇚", llhard: "⥫", lltri: "◺", Lmidot: "Ŀ", lmidot: "ŀ", lmoust: "⎰", lmoustache: "⎰", lnap: "⪉", lnapprox: "⪉", lnE: "≨", lne: "⪇", lneq: "⪇", lneqq: "≨", lnsim: "⋦", loang: "⟬", loarr: "⇽", lobrk: "⟦", LongLeftArrow: "⟵", Longleftarrow: "⟸", longleftarrow: "⟵", LongLeftRightArrow: "⟷", Longleftrightarrow: "⟺", longleftrightarrow: "⟷", longmapsto: "⟼", LongRightArrow: "⟶", Longrightarrow: "⟹", longrightarrow: "⟶", looparrowleft: "↫", looparrowright: "↬", lopar: "⦅", Lopf: "𝕃", lopf: "𝕝", loplus: "⨭", lotimes: "⨴", lowast: "∗", lowbar: "_", LowerLeftArrow: "↙", LowerRightArrow: "↘", loz: "◊", lozenge: "◊", lozf: "⧫", lpar: "(", lparlt: "⦓", lrarr: "⇆", lrcorner: "⌟", lrhar: "⇋", lrhard: "⥭", lrm: "‎", lrtri: "⊿", lsaquo: "‹", Lscr: "ℒ", lscr: "𝓁", Lsh: "↰", lsh: "↰", lsim: "≲", lsime: "⪍", lsimg: "⪏", lsqb: "[", lsquo: "‘", lsquor: "‚", Lstrok: "Ł", lstrok: "ł", Lt: "≪", LT: "<", lt: "<", ltcc: "⪦", ltcir: "⩹", ltdot: "⋖", lthree: "⋋", ltimes: "⋉", ltlarr: "⥶", ltquest: "⩻", ltri: "◃", ltrie: "⊴", ltrif: "◂", ltrPar: "⦖", lurdshar: "⥊", luruhar: "⥦", lvertneqq: "≨︀", lvnE: "≨︀", macr: "¯", male: "♂", malt: "✠", maltese: "✠", Map: "⤅", map: "↦", mapsto: "↦", mapstodown: "↧", mapstoleft: "↤", mapstoup: "↥", marker: "▮", mcomma: "⨩", Mcy: "М", mcy: "м", mdash: "—", mDDot: "∺", measuredangle: "∡", MediumSpace: " ", Mellintrf: "ℳ", Mfr: "𝔐", mfr: "𝔪", mho: "℧", micro: "µ", mid: "∣", midast: "*", midcir: "⫰", middot: "·", minus: "−", minusb: "⊟", minusd: "∸", minusdu: "⨪", MinusPlus: "∓", mlcp: "⫛", mldr: "…", mnplus: "∓", models: "⊧", Mopf: "𝕄", mopf: "𝕞", mp: "∓", Mscr: "ℳ", mscr: "𝓂", mstpos: "∾", Mu: "Μ", mu: "μ", multimap: "⊸", mumap: "⊸", nabla: "∇", Nacute: "Ń", nacute: "ń", nang: "∠⃒", nap: "≉", napE: "⩰̸", napid: "≋̸", napos: "ŉ", napprox: "≉", natur: "♮", natural: "♮", naturals: "ℕ", nbsp: " ", nbump: "≎̸", nbumpe: "≏̸", ncap: "⩃", Ncaron: "Ň", ncaron: "ň", Ncedil: "Ņ", ncedil: "ņ", ncong: "≇", ncongdot: "⩭̸", ncup: "⩂", Ncy: "Н", ncy: "н", ndash: "–", ne: "≠", nearhk: "⤤", neArr: "⇗", nearr: "↗", nearrow: "↗", nedot: "≐̸", NegativeMediumSpace: "​", NegativeThickSpace: "​", NegativeThinSpace: "​", NegativeVeryThinSpace: "​", nequiv: "≢", nesear: "⤨", nesim: "≂̸", NestedGreaterGreater: "≫", NestedLessLess: "≪", NewLine: "\n", nexist: "∄", nexists: "∄", Nfr: "𝔑", nfr: "𝔫", ngE: "≧̸", nge: "≱", ngeq: "≱", ngeqq: "≧̸", ngeqslant: "⩾̸", nges: "⩾̸", nGg: "⋙̸", ngsim: "≵", nGt: "≫⃒", ngt: "≯", ngtr: "≯", nGtv: "≫̸", nhArr: "⇎", nharr: "↮", nhpar: "⫲", ni: "∋", nis: "⋼", nisd: "⋺", niv: "∋", NJcy: "Њ", njcy: "њ", nlArr: "⇍", nlarr: "↚", nldr: "‥", nlE: "≦̸", nle: "≰", nLeftarrow: "⇍", nleftarrow: "↚", nLeftrightarrow: "⇎", nleftrightarrow: "↮", nleq: "≰", nleqq: "≦̸", nleqslant: "⩽̸", nles: "⩽̸", nless: "≮", nLl: "⋘̸", nlsim: "≴", nLt: "≪⃒", nlt: "≮", nltri: "⋪", nltrie: "⋬", nLtv: "≪̸", nmid: "∤", NoBreak: "⁠", NonBreakingSpace: " ", Nopf: "ℕ", nopf: "𝕟", Not: "⫬", not: "¬", NotCongruent: "≢", NotCupCap: "≭", NotDoubleVerticalBar: "∦", NotElement: "∉", NotEqual: "≠", NotEqualTilde: "≂̸", NotExists: "∄", NotGreater: "≯", NotGreaterEqual: "≱", NotGreaterFullEqual: "≧̸", NotGreaterGreater: "≫̸", NotGreaterLess: "≹", NotGreaterSlantEqual: "⩾̸", NotGreaterTilde: "≵", NotHumpDownHump: "≎̸", NotHumpEqual: "≏̸", notin: "∉", notindot: "⋵̸", notinE: "⋹̸", notinva: "∉", notinvb: "⋷", notinvc: "⋶", NotLeftTriangle: "⋪", NotLeftTriangleBar: "⧏̸", NotLeftTriangleEqual: "⋬", NotLess: "≮", NotLessEqual: "≰", NotLessGreater: "≸", NotLessLess: "≪̸", NotLessSlantEqual: "⩽̸", NotLessTilde: "≴", NotNestedGreaterGreater: "⪢̸", NotNestedLessLess: "⪡̸", notni: "∌", notniva: "∌", notnivb: "⋾", notnivc: "⋽", NotPrecedes: "⊀", NotPrecedesEqual: "⪯̸", NotPrecedesSlantEqual: "⋠", NotReverseElement: "∌", NotRightTriangle: "⋫", NotRightTriangleBar: "⧐̸", NotRightTriangleEqual: "⋭", NotSquareSubset: "⊏̸", NotSquareSubsetEqual: "⋢", NotSquareSuperset: "⊐̸", NotSquareSupersetEqual: "⋣", NotSubset: "⊂⃒", NotSubsetEqual: "⊈", NotSucceeds: "⊁", NotSucceedsEqual: "⪰̸", NotSucceedsSlantEqual: "⋡", NotSucceedsTilde: "≿̸", NotSuperset: "⊃⃒", NotSupersetEqual: "⊉", NotTilde: "≁", NotTildeEqual: "≄", NotTildeFullEqual: "≇", NotTildeTilde: "≉", NotVerticalBar: "∤", npar: "∦", nparallel: "∦", nparsl: "⫽⃥", npart: "∂̸", npolint: "⨔", npr: "⊀", nprcue: "⋠", npre: "⪯̸", nprec: "⊀", npreceq: "⪯̸", nrArr: "⇏", nrarr: "↛", nrarrc: "⤳̸", nrarrw: "↝̸", nRightarrow: "⇏", nrightarrow: "↛", nrtri: "⋫", nrtrie: "⋭", nsc: "⊁", nsccue: "⋡", nsce: "⪰̸", Nscr: "𝒩", nscr: "𝓃", nshortmid: "∤", nshortparallel: "∦", nsim: "≁", nsime: "≄", nsimeq: "≄", nsmid: "∤", nspar: "∦", nsqsube: "⋢", nsqsupe: "⋣", nsub: "⊄", nsubE: "⫅̸", nsube: "⊈", nsubset: "⊂⃒", nsubseteq: "⊈", nsubseteqq: "⫅̸", nsucc: "⊁", nsucceq: "⪰̸", nsup: "⊅", nsupE: "⫆̸", nsupe: "⊉", nsupset: "⊃⃒", nsupseteq: "⊉", nsupseteqq: "⫆̸", ntgl: "≹", Ntilde: "Ñ", ntilde: "ñ", ntlg: "≸", ntriangleleft: "⋪", ntrianglelefteq: "⋬", ntriangleright: "⋫", ntrianglerighteq: "⋭", Nu: "Ν", nu: "ν", num: "#", numero: "№", numsp: " ", nvap: "≍⃒", nVDash: "⊯", nVdash: "⊮", nvDash: "⊭", nvdash: "⊬", nvge: "≥⃒", nvgt: ">⃒", nvHarr: "⤄", nvinfin: "⧞", nvlArr: "⤂", nvle: "≤⃒", nvlt: "<⃒", nvltrie: "⊴⃒", nvrArr: "⤃", nvrtrie: "⊵⃒", nvsim: "∼⃒", nwarhk: "⤣", nwArr: "⇖", nwarr: "↖", nwarrow: "↖", nwnear: "⤧", Oacute: "Ó", oacute: "ó", oast: "⊛", ocir: "⊚", Ocirc: "Ô", ocirc: "ô", Ocy: "О", ocy: "о", odash: "⊝", Odblac: "Ő", odblac: "ő", odiv: "⨸", odot: "⊙", odsold: "⦼", OElig: "Œ", oelig: "œ", ofcir: "⦿", Ofr: "𝔒", ofr: "𝔬", ogon: "˛", Ograve: "Ò", ograve: "ò", ogt: "⧁", ohbar: "⦵", ohm: "Ω", oint: "∮", olarr: "↺", olcir: "⦾", olcross: "⦻", oline: "‾", olt: "⧀", Omacr: "Ō", omacr: "ō", Omega: "Ω", omega: "ω", Omicron: "Ο", omicron: "ο", omid: "⦶", ominus: "⊖", Oopf: "𝕆", oopf: "𝕠", opar: "⦷", OpenCurlyDoubleQuote: "“", OpenCurlyQuote: "‘", operp: "⦹", oplus: "⊕", Or: "⩔", or: "∨", orarr: "↻", ord: "⩝", order: "ℴ", orderof: "ℴ", ordf: "ª", ordm: "º", origof: "⊶", oror: "⩖", orslope: "⩗", orv: "⩛", oS: "Ⓢ", Oscr: "𝒪", oscr: "ℴ", Oslash: "Ø", oslash: "ø", osol: "⊘", Otilde: "Õ", otilde: "õ", Otimes: "⨷", otimes: "⊗", otimesas: "⨶", Ouml: "Ö", ouml: "ö", ovbar: "⌽", OverBar: "‾", OverBrace: "⏞", OverBracket: "⎴", OverParenthesis: "⏜", par: "∥", para: "¶", parallel: "∥", parsim: "⫳", parsl: "⫽", part: "∂", PartialD: "∂", Pcy: "П", pcy: "п", percnt: "%", period: ".", permil: "‰", perp: "⊥", pertenk: "‱", Pfr: "𝔓", pfr: "𝔭", Phi: "Φ", phi: "φ", phiv: "ϕ", phmmat: "ℳ", phone: "☎", Pi: "Π", pi: "π", pitchfork: "⋔", piv: "ϖ", planck: "ℏ", planckh: "ℎ", plankv: "ℏ", plus: "+", plusacir: "⨣", plusb: "⊞", pluscir: "⨢", plusdo: "∔", plusdu: "⨥", pluse: "⩲", PlusMinus: "±", plusmn: "±", plussim: "⨦", plustwo: "⨧", pm: "±", Poincareplane: "ℌ", pointint: "⨕", Popf: "ℙ", popf: "𝕡", pound: "£", Pr: "⪻", pr: "≺", prap: "⪷", prcue: "≼", prE: "⪳", pre: "⪯", prec: "≺", precapprox: "⪷", preccurlyeq: "≼", Precedes: "≺", PrecedesEqual: "⪯", PrecedesSlantEqual: "≼", PrecedesTilde: "≾", preceq: "⪯", precnapprox: "⪹", precneqq: "⪵", precnsim: "⋨", precsim: "≾", Prime: "″", prime: "′", primes: "ℙ", prnap: "⪹", prnE: "⪵", prnsim: "⋨", prod: "∏", Product: "∏", profalar: "⌮", profline: "⌒", profsurf: "⌓", prop: "∝", Proportion: "∷", Proportional: "∝", propto: "∝", prsim: "≾", prurel: "⊰", Pscr: "𝒫", pscr: "𝓅", Psi: "Ψ", psi: "ψ", puncsp: " ", Qfr: "𝔔", qfr: "𝔮", qint: "⨌", Qopf: "ℚ", qopf: "𝕢", qprime: "⁗", Qscr: "𝒬", qscr: "𝓆", quaternions: "ℍ", quatint: "⨖", quest: "?", questeq: "≟", QUOT: '"', quot: '"', rAarr: "⇛", race: "∽̱", Racute: "Ŕ", racute: "ŕ", radic: "√", raemptyv: "⦳", Rang: "⟫", rang: "⟩", rangd: "⦒", range: "⦥", rangle: "⟩", raquo: "»", Rarr: "↠", rArr: "⇒", rarr: "→", rarrap: "⥵", rarrb: "⇥", rarrbfs: "⤠", rarrc: "⤳", rarrfs: "⤞", rarrhk: "↪", rarrlp: "↬", rarrpl: "⥅", rarrsim: "⥴", Rarrtl: "⤖", rarrtl: "↣", rarrw: "↝", rAtail: "⤜", ratail: "⤚", ratio: "∶", rationals: "ℚ", RBarr: "⤐", rBarr: "⤏", rbarr: "⤍", rbbrk: "❳", rbrace: "}", rbrack: "]", rbrke: "⦌", rbrksld: "⦎", rbrkslu: "⦐", Rcaron: "Ř", rcaron: "ř", Rcedil: "Ŗ", rcedil: "ŗ", rceil: "⌉", rcub: "}", Rcy: "Р", rcy: "р", rdca: "⤷", rdldhar: "⥩", rdquo: "”", rdquor: "”", rdsh: "↳", Re: "ℜ", real: "ℜ", realine: "ℛ", realpart: "ℜ", reals: "ℝ", rect: "▭", REG: "®", reg: "®", ReverseElement: "∋", ReverseEquilibrium: "⇋", ReverseUpEquilibrium: "⥯", rfisht: "⥽", rfloor: "⌋", Rfr: "ℜ", rfr: "𝔯", rHar: "⥤", rhard: "⇁", rharu: "⇀", rharul: "⥬", Rho: "Ρ", rho: "ρ", rhov: "ϱ", RightAngleBracket: "⟩", RightArrow: "→", Rightarrow: "⇒", rightarrow: "→", RightArrowBar: "⇥", RightArrowLeftArrow: "⇄", rightarrowtail: "↣", RightCeiling: "⌉", RightDoubleBracket: "⟧", RightDownTeeVector: "⥝", RightDownVector: "⇂", RightDownVectorBar: "⥕", RightFloor: "⌋", rightharpoondown: "⇁", rightharpoonup: "⇀", rightleftarrows: "⇄", rightleftharpoons: "⇌", rightrightarrows: "⇉", rightsquigarrow: "↝", RightTee: "⊢", RightTeeArrow: "↦", RightTeeVector: "⥛", rightthreetimes: "⋌", RightTriangle: "⊳", RightTriangleBar: "⧐", RightTriangleEqual: "⊵", RightUpDownVector: "⥏", RightUpTeeVector: "⥜", RightUpVector: "↾", RightUpVectorBar: "⥔", RightVector: "⇀", RightVectorBar: "⥓", ring: "˚", risingdotseq: "≓", rlarr: "⇄", rlhar: "⇌", rlm: "‏", rmoust: "⎱", rmoustache: "⎱", rnmid: "⫮", roang: "⟭", roarr: "⇾", robrk: "⟧", ropar: "⦆", Ropf: "ℝ", ropf: "𝕣", roplus: "⨮", rotimes: "⨵", RoundImplies: "⥰", rpar: ")", rpargt: "⦔", rppolint: "⨒", rrarr: "⇉", Rrightarrow: "⇛", rsaquo: "›", Rscr: "ℛ", rscr: "𝓇", Rsh: "↱", rsh: "↱", rsqb: "]", rsquo: "’", rsquor: "’", rthree: "⋌", rtimes: "⋊", rtri: "▹", rtrie: "⊵", rtrif: "▸", rtriltri: "⧎", RuleDelayed: "⧴", ruluhar: "⥨", rx: "℞", Sacute: "Ś", sacute: "ś", sbquo: "‚", Sc: "⪼", sc: "≻", scap: "⪸", Scaron: "Š", scaron: "š", sccue: "≽", scE: "⪴", sce: "⪰", Scedil: "Ş", scedil: "ş", Scirc: "Ŝ", scirc: "ŝ", scnap: "⪺", scnE: "⪶", scnsim: "⋩", scpolint: "⨓", scsim: "≿", Scy: "С", scy: "с", sdot: "⋅", sdotb: "⊡", sdote: "⩦", searhk: "⤥", seArr: "⇘", searr: "↘", searrow: "↘", sect: "§", semi: ";", seswar: "⤩", setminus: "∖", setmn: "∖", sext: "✶", Sfr: "𝔖", sfr: "𝔰", sfrown: "⌢", sharp: "♯", SHCHcy: "Щ", shchcy: "щ", SHcy: "Ш", shcy: "ш", ShortDownArrow: "↓", ShortLeftArrow: "←", shortmid: "∣", shortparallel: "∥", ShortRightArrow: "→", ShortUpArrow: "↑", shy: "­", Sigma: "Σ", sigma: "σ", sigmaf: "ς", sigmav: "ς", sim: "∼", simdot: "⩪", sime: "≃", simeq: "≃", simg: "⪞", simgE: "⪠", siml: "⪝", simlE: "⪟", simne: "≆", simplus: "⨤", simrarr: "⥲", slarr: "←", SmallCircle: "∘", smallsetminus: "∖", smashp: "⨳", smeparsl: "⧤", smid: "∣", smile: "⌣", smt: "⪪", smte: "⪬", smtes: "⪬︀", SOFTcy: "Ь", softcy: "ь", sol: "/", solb: "⧄", solbar: "⌿", Sopf: "𝕊", sopf: "𝕤", spades: "♠", spadesuit: "♠", spar: "∥", sqcap: "⊓", sqcaps: "⊓︀", sqcup: "⊔", sqcups: "⊔︀", Sqrt: "√", sqsub: "⊏", sqsube: "⊑", sqsubset: "⊏", sqsubseteq: "⊑", sqsup: "⊐", sqsupe: "⊒", sqsupset: "⊐", sqsupseteq: "⊒", squ: "□", Square: "□", square: "□", SquareIntersection: "⊓", SquareSubset: "⊏", SquareSubsetEqual: "⊑", SquareSuperset: "⊐", SquareSupersetEqual: "⊒", SquareUnion: "⊔", squarf: "▪", squf: "▪", srarr: "→", Sscr: "𝒮", sscr: "𝓈", ssetmn: "∖", ssmile: "⌣", sstarf: "⋆", Star: "⋆", star: "☆", starf: "★", straightepsilon: "ϵ", straightphi: "ϕ", strns: "¯", Sub: "⋐", sub: "⊂", subdot: "⪽", subE: "⫅", sube: "⊆", subedot: "⫃", submult: "⫁", subnE: "⫋", subne: "⊊", subplus: "⪿", subrarr: "⥹", Subset: "⋐", subset: "⊂", subseteq: "⊆", subseteqq: "⫅", SubsetEqual: "⊆", subsetneq: "⊊", subsetneqq: "⫋", subsim: "⫇", subsub: "⫕", subsup: "⫓", succ: "≻", succapprox: "⪸", succcurlyeq: "≽", Succeeds: "≻", SucceedsEqual: "⪰", SucceedsSlantEqual: "≽", SucceedsTilde: "≿", succeq: "⪰", succnapprox: "⪺", succneqq: "⪶", succnsim: "⋩", succsim: "≿", SuchThat: "∋", Sum: "∑", sum: "∑", sung: "♪", Sup: "⋑", sup: "⊃", sup1: "¹", sup2: "²", sup3: "³", supdot: "⪾", supdsub: "⫘", supE: "⫆", supe: "⊇", supedot: "⫄", Superset: "⊃", SupersetEqual: "⊇", suphsol: "⟉", suphsub: "⫗", suplarr: "⥻", supmult: "⫂", supnE: "⫌", supne: "⊋", supplus: "⫀", Supset: "⋑", supset: "⊃", supseteq: "⊇", supseteqq: "⫆", supsetneq: "⊋", supsetneqq: "⫌", supsim: "⫈", supsub: "⫔", supsup: "⫖", swarhk: "⤦", swArr: "⇙", swarr: "↙", swarrow: "↙", swnwar: "⤪", szlig: "ß", Tab: "	", target: "⌖", Tau: "Τ", tau: "τ", tbrk: "⎴", Tcaron: "Ť", tcaron: "ť", Tcedil: "Ţ", tcedil: "ţ", Tcy: "Т", tcy: "т", tdot: "⃛", telrec: "⌕", Tfr: "𝔗", tfr: "𝔱", there4: "∴", Therefore: "∴", therefore: "∴", Theta: "Θ", theta: "θ", thetasym: "ϑ", thetav: "ϑ", thickapprox: "≈", thicksim: "∼", ThickSpace: "  ", thinsp: " ", ThinSpace: " ", thkap: "≈", thksim: "∼", THORN: "Þ", thorn: "þ", Tilde: "∼", tilde: "˜", TildeEqual: "≃", TildeFullEqual: "≅", TildeTilde: "≈", times: "×", timesb: "⊠", timesbar: "⨱", timesd: "⨰", tint: "∭", toea: "⤨", top: "⊤", topbot: "⌶", topcir: "⫱", Topf: "𝕋", topf: "𝕥", topfork: "⫚", tosa: "⤩", tprime: "‴", TRADE: "™", trade: "™", triangle: "▵", triangledown: "▿", triangleleft: "◃", trianglelefteq: "⊴", triangleq: "≜", triangleright: "▹", trianglerighteq: "⊵", tridot: "◬", trie: "≜", triminus: "⨺", TripleDot: "⃛", triplus: "⨹", trisb: "⧍", tritime: "⨻", trpezium: "⏢", Tscr: "𝒯", tscr: "𝓉", TScy: "Ц", tscy: "ц", TSHcy: "Ћ", tshcy: "ћ", Tstrok: "Ŧ", tstrok: "ŧ", twixt: "≬", twoheadleftarrow: "↞", twoheadrightarrow: "↠", Uacute: "Ú", uacute: "ú", Uarr: "↟", uArr: "⇑", uarr: "↑", Uarrocir: "⥉", Ubrcy: "Ў", ubrcy: "ў", Ubreve: "Ŭ", ubreve: "ŭ", Ucirc: "Û", ucirc: "û", Ucy: "У", ucy: "у", udarr: "⇅", Udblac: "Ű", udblac: "ű", udhar: "⥮", ufisht: "⥾", Ufr: "𝔘", ufr: "𝔲", Ugrave: "Ù", ugrave: "ù", uHar: "⥣", uharl: "↿", uharr: "↾", uhblk: "▀", ulcorn: "⌜", ulcorner: "⌜", ulcrop: "⌏", ultri: "◸", Umacr: "Ū", umacr: "ū", uml: "¨", UnderBar: "_", UnderBrace: "⏟", UnderBracket: "⎵", UnderParenthesis: "⏝", Union: "⋃", UnionPlus: "⊎", Uogon: "Ų", uogon: "ų", Uopf: "𝕌", uopf: "𝕦", UpArrow: "↑", Uparrow: "⇑", uparrow: "↑", UpArrowBar: "⤒", UpArrowDownArrow: "⇅", UpDownArrow: "↕", Updownarrow: "⇕", updownarrow: "↕", UpEquilibrium: "⥮", upharpoonleft: "↿", upharpoonright: "↾", uplus: "⊎", UpperLeftArrow: "↖", UpperRightArrow: "↗", Upsi: "ϒ", upsi: "υ", upsih: "ϒ", Upsilon: "Υ", upsilon: "υ", UpTee: "⊥", UpTeeArrow: "↥", upuparrows: "⇈", urcorn: "⌝", urcorner: "⌝", urcrop: "⌎", Uring: "Ů", uring: "ů", urtri: "◹", Uscr: "𝒰", uscr: "𝓊", utdot: "⋰", Utilde: "Ũ", utilde: "ũ", utri: "▵", utrif: "▴", uuarr: "⇈", Uuml: "Ü", uuml: "ü", uwangle: "⦧", vangrt: "⦜", varepsilon: "ϵ", varkappa: "ϰ", varnothing: "∅", varphi: "ϕ", varpi: "ϖ", varpropto: "∝", vArr: "⇕", varr: "↕", varrho: "ϱ", varsigma: "ς", varsubsetneq: "⊊︀", varsubsetneqq: "⫋︀", varsupsetneq: "⊋︀", varsupsetneqq: "⫌︀", vartheta: "ϑ", vartriangleleft: "⊲", vartriangleright: "⊳", Vbar: "⫫", vBar: "⫨", vBarv: "⫩", Vcy: "В", vcy: "в", VDash: "⊫", Vdash: "⊩", vDash: "⊨", vdash: "⊢", Vdashl: "⫦", Vee: "⋁", vee: "∨", veebar: "⊻", veeeq: "≚", vellip: "⋮", Verbar: "‖", verbar: "|", Vert: "‖", vert: "|", VerticalBar: "∣", VerticalLine: "|", VerticalSeparator: "❘", VerticalTilde: "≀", VeryThinSpace: " ", Vfr: "𝔙", vfr: "𝔳", vltri: "⊲", vnsub: "⊂⃒", vnsup: "⊃⃒", Vopf: "𝕍", vopf: "𝕧", vprop: "∝", vrtri: "⊳", Vscr: "𝒱", vscr: "𝓋", vsubnE: "⫋︀", vsubne: "⊊︀", vsupnE: "⫌︀", vsupne: "⊋︀", Vvdash: "⊪", vzigzag: "⦚", Wcirc: "Ŵ", wcirc: "ŵ", wedbar: "⩟", Wedge: "⋀", wedge: "∧", wedgeq: "≙", weierp: "℘", Wfr: "𝔚", wfr: "𝔴", Wopf: "𝕎", wopf: "𝕨", wp: "℘", wr: "≀", wreath: "≀", Wscr: "𝒲", wscr: "𝓌", xcap: "⋂", xcirc: "◯", xcup: "⋃", xdtri: "▽", Xfr: "𝔛", xfr: "𝔵", xhArr: "⟺", xharr: "⟷", Xi: "Ξ", xi: "ξ", xlArr: "⟸", xlarr: "⟵", xmap: "⟼", xnis: "⋻", xodot: "⨀", Xopf: "𝕏", xopf: "𝕩", xoplus: "⨁", xotime: "⨂", xrArr: "⟹", xrarr: "⟶", Xscr: "𝒳", xscr: "𝓍", xsqcup: "⨆", xuplus: "⨄", xutri: "△", xvee: "⋁", xwedge: "⋀", Yacute: "Ý", yacute: "ý", YAcy: "Я", yacy: "я", Ycirc: "Ŷ", ycirc: "ŷ", Ycy: "Ы", ycy: "ы", yen: "¥", Yfr: "𝔜", yfr: "𝔶", YIcy: "Ї", yicy: "ї", Yopf: "𝕐", yopf: "𝕪", Yscr: "𝒴", yscr: "𝓎", YUcy: "Ю", yucy: "ю", Yuml: "Ÿ", yuml: "ÿ", Zacute: "Ź", zacute: "ź", Zcaron: "Ž", zcaron: "ž", Zcy: "З", zcy: "з", Zdot: "Ż", zdot: "ż", zeetrf: "ℨ", ZeroWidthSpace: "​", Zeta: "Ζ", zeta: "ζ", Zfr: "ℨ", zfr: "𝔷", ZHcy: "Ж", zhcy: "ж", zigrarr: "⇝", Zopf: "ℤ", zopf: "𝕫", Zscr: "𝒵", zscr: "𝓏", zwj: "‍", zwnj: "‌" }), t.entityMap = t.HTML_ENTITIES;
      }, function(e, t, r) {
        var n = r(2).NAMESPACE, o = /[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/, a = new RegExp("[\\-\\.0-9" + o.source.slice(1, -1) + "\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"), i = new RegExp("^" + o.source + a.source + "*(?::" + o.source + a.source + "*)?$"), s = 0, c = 1, u = 2, l = 3, d = 4, p = 5, f = 6, h = 7;
        function m(e2, t2) {
          this.message = e2, this.locator = t2, Error.captureStackTrace && Error.captureStackTrace(this, m);
        }
        function g() {
        }
        function y(e2, t2) {
          return t2.lineNumber = e2.lineNumber, t2.columnNumber = e2.columnNumber, t2;
        }
        function v(e2, t2, r2, o2, a2, i2) {
          function m2(e3, t3, n2) {
            r2.attributeNames.hasOwnProperty(e3) && i2.fatalError("Attribute " + e3 + " redefined"), r2.addValue(e3, t3.replace(/[\t\n\r]/g, " ").replace(/&#?\w+;/g, a2), n2);
          }
          for (var g2, y2 = ++t2, v2 = s; ; ) {
            var k2 = e2.charAt(y2);
            switch (k2) {
              case "=":
                if (v2 === c) g2 = e2.slice(t2, y2), v2 = l;
                else {
                  if (v2 !== u) throw new Error("attribute equal must after attrName");
                  v2 = l;
                }
                break;
              case "'":
              case '"':
                if (v2 === l || v2 === c) {
                  if (v2 === c && (i2.warning('attribute value must after "="'), g2 = e2.slice(t2, y2)), t2 = y2 + 1, !((y2 = e2.indexOf(k2, t2)) > 0)) throw new Error("attribute value no end '" + k2 + "' match");
                  m2(g2, b2 = e2.slice(t2, y2), t2 - 1), v2 = p;
                } else {
                  if (v2 != d) throw new Error('attribute value must after "="');
                  m2(g2, b2 = e2.slice(t2, y2), t2), i2.warning('attribute "' + g2 + '" missed start quot(' + k2 + ")!!"), t2 = y2 + 1, v2 = p;
                }
                break;
              case "/":
                switch (v2) {
                  case s:
                    r2.setTagName(e2.slice(t2, y2));
                  case p:
                  case f:
                  case h:
                    v2 = h, r2.closed = true;
                  case d:
                  case c:
                    break;
                  case u:
                    r2.closed = true;
                    break;
                  default:
                    throw new Error("attribute invalid close char('/')");
                }
                break;
              case "":
                return i2.error("unexpected end of input"), v2 == s && r2.setTagName(e2.slice(t2, y2)), y2;
              case ">":
                switch (v2) {
                  case s:
                    r2.setTagName(e2.slice(t2, y2));
                  case p:
                  case f:
                  case h:
                    break;
                  case d:
                  case c:
                    "/" === (b2 = e2.slice(t2, y2)).slice(-1) && (r2.closed = true, b2 = b2.slice(0, -1));
                  case u:
                    v2 === u && (b2 = g2), v2 == d ? (i2.warning('attribute "' + b2 + '" missed quot(")!'), m2(g2, b2, t2)) : (n.isHTML(o2[""]) && b2.match(/^(?:disabled|checked|selected)$/i) || i2.warning('attribute "' + b2 + '" missed value!! "' + b2 + '" instead!!'), m2(b2, b2, t2));
                    break;
                  case l:
                    throw new Error("attribute value missed!!");
                }
                return y2;
              case "":
                k2 = " ";
              default:
                if (k2 <= " ") switch (v2) {
                  case s:
                    r2.setTagName(e2.slice(t2, y2)), v2 = f;
                    break;
                  case c:
                    g2 = e2.slice(t2, y2), v2 = u;
                    break;
                  case d:
                    var b2 = e2.slice(t2, y2);
                    i2.warning('attribute "' + b2 + '" missed quot(")!!'), m2(g2, b2, t2);
                  case p:
                    v2 = f;
                }
                else switch (v2) {
                  case u:
                    r2.tagName;
                    n.isHTML(o2[""]) && g2.match(/^(?:disabled|checked|selected)$/i) || i2.warning('attribute "' + g2 + '" missed value!! "' + g2 + '" instead2!!'), m2(g2, g2, t2), t2 = y2, v2 = c;
                    break;
                  case p:
                    i2.warning('attribute space is required"' + g2 + '"!!');
                  case f:
                    v2 = c, t2 = y2;
                    break;
                  case l:
                    v2 = d, t2 = y2;
                    break;
                  case h:
                    throw new Error("elements closed character '/' and '>' must be connected to");
                }
            }
            y2++;
          }
        }
        function k(e2, t2, r2) {
          for (var o2 = e2.tagName, a2 = null, i2 = e2.length; i2--; ) {
            var s2 = e2[i2], c2 = s2.qName, u2 = s2.value;
            if ((f2 = c2.indexOf(":")) > 0) var l2 = s2.prefix = c2.slice(0, f2), d2 = c2.slice(f2 + 1), p2 = "xmlns" === l2 && d2;
            else d2 = c2, l2 = null, p2 = "xmlns" === c2 && "";
            s2.localName = d2, false !== p2 && (null == a2 && (a2 = {}, S(r2, r2 = {})), r2[p2] = a2[p2] = u2, s2.uri = n.XMLNS, t2.startPrefixMapping(p2, u2));
          }
          for (i2 = e2.length; i2--; ) {
            (l2 = (s2 = e2[i2]).prefix) && ("xml" === l2 && (s2.uri = n.XML), "xmlns" !== l2 && (s2.uri = r2[l2 || ""]));
          }
          var f2;
          (f2 = o2.indexOf(":")) > 0 ? (l2 = e2.prefix = o2.slice(0, f2), d2 = e2.localName = o2.slice(f2 + 1)) : (l2 = null, d2 = e2.localName = o2);
          var h2 = e2.uri = r2[l2 || ""];
          if (t2.startElement(h2, d2, o2, e2), !e2.closed) return e2.currentNSMap = r2, e2.localNSMap = a2, true;
          if (t2.endElement(h2, d2, o2), a2) for (l2 in a2) Object.prototype.hasOwnProperty.call(a2, l2) && t2.endPrefixMapping(l2);
        }
        function b(e2, t2, r2, n2, o2) {
          if (/^(?:script|textarea)$/i.test(r2)) {
            var a2 = e2.indexOf("</" + r2 + ">", t2), i2 = e2.substring(t2 + 1, a2);
            if (/[&<]/.test(i2)) return /^script$/i.test(r2) ? (o2.characters(i2, 0, i2.length), a2) : (i2 = i2.replace(/&#?\w+;/g, n2), o2.characters(i2, 0, i2.length), a2);
          }
          return t2 + 1;
        }
        function C(e2, t2, r2, n2) {
          var o2 = n2[r2];
          return null == o2 && ((o2 = e2.lastIndexOf("</" + r2 + ">")) < t2 && (o2 = e2.lastIndexOf("</" + r2)), n2[r2] = o2), o2 < t2;
        }
        function S(e2, t2) {
          for (var r2 in e2) Object.prototype.hasOwnProperty.call(e2, r2) && (t2[r2] = e2[r2]);
        }
        function T(e2, t2, r2, n2) {
          if ("-" === e2.charAt(t2 + 2)) return "-" === e2.charAt(t2 + 3) ? (o2 = e2.indexOf("-->", t2 + 4)) > t2 ? (r2.comment(e2, t2 + 4, o2 - t2 - 4), o2 + 3) : (n2.error("Unclosed comment"), -1) : -1;
          if ("CDATA[" == e2.substr(t2 + 3, 6)) {
            var o2 = e2.indexOf("]]>", t2 + 9);
            return r2.startCDATA(), r2.characters(e2, t2 + 9, o2 - t2 - 9), r2.endCDATA(), o2 + 3;
          }
          var a2 = function(e3, t3) {
            var r3, n3 = [], o3 = /'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;
            o3.lastIndex = t3, o3.exec(e3);
            for (; r3 = o3.exec(e3); ) if (n3.push(r3), r3[1]) return n3;
          }(e2, t2), i2 = a2.length;
          if (i2 > 1 && /!doctype/i.test(a2[0][0])) {
            var s2 = a2[1][0], c2 = false, u2 = false;
            i2 > 3 && (/^public$/i.test(a2[2][0]) ? (c2 = a2[3][0], u2 = i2 > 4 && a2[4][0]) : /^system$/i.test(a2[2][0]) && (u2 = a2[3][0]));
            var l2 = a2[i2 - 1];
            return r2.startDTD(s2, c2, u2), r2.endDTD(), l2.index + l2[0].length;
          }
          return -1;
        }
        function w(e2, t2, r2) {
          var n2 = e2.indexOf("?>", t2);
          if (n2) {
            var o2 = e2.substring(t2, n2).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);
            if (o2) {
              o2[0].length;
              return r2.processingInstruction(o2[1], o2[2]), n2 + 2;
            }
            return -1;
          }
          return -1;
        }
        function R() {
          this.attributeNames = {};
        }
        m.prototype = new Error(), m.prototype.name = m.name, g.prototype = { parse: function(e2, t2, r2) {
          var o2 = this.domBuilder;
          o2.startDocument(), S(t2, t2 = {}), function(e3, t3, r3, o3, a2) {
            function i2(e4) {
              if (e4 > 65535) {
                var t4 = 55296 + ((e4 -= 65536) >> 10), r4 = 56320 + (1023 & e4);
                return String.fromCharCode(t4, r4);
              }
              return String.fromCharCode(e4);
            }
            function s2(e4) {
              var t4 = e4.slice(1, -1);
              return Object.hasOwnProperty.call(r3, t4) ? r3[t4] : "#" === t4.charAt(0) ? i2(parseInt(t4.substr(1).replace("x", "0x"))) : (a2.error("entity not found:" + e4), e4);
            }
            function c2(t4) {
              if (t4 > S2) {
                var r4 = e3.substring(S2, t4).replace(/&#?\w+;/g, s2);
                f2 && u2(S2), o3.characters(r4, 0, t4 - S2), S2 = t4;
              }
            }
            function u2(t4, r4) {
              for (; t4 >= d2 && (r4 = p2.exec(e3)); ) l2 = r4.index, d2 = l2 + r4[0].length, f2.lineNumber++;
              f2.columnNumber = t4 - l2 + 1;
            }
            var l2 = 0, d2 = 0, p2 = /.*(?:\r\n?|\n)|.*$/g, f2 = o3.locator, h2 = [{ currentNSMap: t3 }], g2 = {}, S2 = 0;
            for (; ; ) {
              try {
                var x = e3.indexOf("<", S2);
                if (x < 0) {
                  if (!e3.substr(S2).match(/^\s*$/)) {
                    var A = o3.doc, E = A.createTextNode(e3.substr(S2));
                    A.appendChild(E), o3.currentElement = E;
                  }
                  return;
                }
                switch (x > S2 && c2(x), e3.charAt(x + 1)) {
                  case "/":
                    var B = e3.indexOf(">", x + 3), D = e3.substring(x + 2, B).replace(/[ \t\n\r]+$/g, ""), N = h2.pop();
                    B < 0 ? (D = e3.substring(x + 2).replace(/[\s<].*/, ""), a2.error("end tag name: " + D + " is not complete:" + N.tagName), B = x + 1 + D.length) : D.match(/\s</) && (D = D.replace(/[\s<].*/, ""), a2.error("end tag name: " + D + " maybe not complete"), B = x + 1 + D.length);
                    var P = N.localNSMap, O = N.tagName == D;
                    if (O || N.tagName && N.tagName.toLowerCase() == D.toLowerCase()) {
                      if (o3.endElement(N.uri, N.localName, D), P) for (var I in P) Object.prototype.hasOwnProperty.call(P, I) && o3.endPrefixMapping(I);
                      O || a2.fatalError("end tag name: " + D + " is not match the current start tagName:" + N.tagName);
                    } else h2.push(N);
                    B++;
                    break;
                  case "?":
                    f2 && u2(x), B = w(e3, x, o3);
                    break;
                  case "!":
                    f2 && u2(x), B = T(e3, x, o3, a2);
                    break;
                  default:
                    f2 && u2(x);
                    var L = new R(), U = h2[h2.length - 1].currentNSMap, _ = (B = v(e3, x, L, U, s2, a2), L.length);
                    if (!L.closed && C(e3, B, L.tagName, g2) && (L.closed = true, r3.nbsp || a2.warning("unclosed xml attribute")), f2 && _) {
                      for (var M = y(f2, {}), q = 0; q < _; q++) {
                        var j = L[q];
                        u2(j.offset), j.locator = y(f2, {});
                      }
                      o3.locator = M, k(L, o3, U) && h2.push(L), o3.locator = f2;
                    } else k(L, o3, U) && h2.push(L);
                    n.isHTML(L.uri) && !L.closed ? B = b(e3, B, L.tagName, s2, o3) : B++;
                }
              } catch (e4) {
                if (e4 instanceof m) throw e4;
                a2.error("element parse error: " + e4), B = -1;
              }
              B > S2 ? S2 = B : c2(Math.max(x, S2) + 1);
            }
          }(e2, t2, r2, o2, this.errorHandler), o2.endDocument();
        } }, R.prototype = { setTagName: function(e2) {
          if (!i.test(e2)) throw new Error("invalid tagName:" + e2);
          this.tagName = e2;
        }, addValue: function(e2, t2, r2) {
          if (!i.test(e2)) throw new Error("invalid attribute:" + e2);
          this.attributeNames[e2] = this.length, this[this.length++] = { qName: e2, value: t2, offset: r2 };
        }, length: 0, getLocalName: function(e2) {
          return this[e2].localName;
        }, getLocator: function(e2) {
          return this[e2].locator;
        }, getQName: function(e2) {
          return this[e2].qName;
        }, getURI: function(e2) {
          return this[e2].uri;
        }, getValue: function(e2) {
          return this[e2].value;
        } }, t.XMLReader = g, t.ParseError = m;
      }, function(e, t, r) {
        var n = r(0), o = "a-zA-Z_À-ÖØ-öø-ÿͰ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿿、-퟿豈-﷏ﷰ-�", a = new RegExp("^([^" + o + "])|^((x|X)(m|M)(l|L))|([^" + o + "-.0-9·̀-ͯ‿⁀])", "g"), i = /[^\x09\x0A\x0D\x20-\xFF\x85\xA0-\uD7FF\uE000-\uFDCF\uFDE0-\uFFFD]/gm, s = function(e2) {
          var t2 = [];
          if (e2 instanceof Object) for (var r2 in e2) e2.hasOwnProperty(r2) && t2.push(r2);
          return t2;
        }, c = function(e2, t2) {
          var r2 = function(e3, r3, n2, o2, i2) {
            var s2 = void 0 !== t2.indent ? t2.indent : "	", c2 = t2.prettyPrint ? "\n" + new Array(o2).join(s2) : "";
            t2.removeIllegalNameCharacters && (e3 = e3.replace(a, "_"));
            var u2 = [c2, "<", e3, n2 || ""];
            return r3 && r3.length > 0 ? (u2.push(">"), u2.push(r3), i2 && u2.push(c2), u2.push("</"), u2.push(e3), u2.push(">")) : u2.push("/>"), u2.join("");
          };
          return function e3(o2, a2, c2) {
            var u2 = n(o2);
            switch ((Array.isArray ? Array.isArray(o2) : o2 instanceof Array) ? u2 = "array" : o2 instanceof Date && (u2 = "date"), u2) {
              case "array":
                var l = [];
                return o2.map(function(t3) {
                  l.push(e3(t3, 1, c2 + 1));
                }), t2.prettyPrint && l.push("\n"), l.join("");
              case "date":
                return o2.toJSON ? o2.toJSON() : o2 + "";
              case "object":
                var d = [];
                for (var p in o2) if (o2.hasOwnProperty(p)) if (o2[p] instanceof Array) for (var f = 0; f < o2[p].length; f++) o2[p].hasOwnProperty(f) && d.push(r2(p, e3(o2[p][f], 0, c2 + 1), null, c2 + 1, s(o2[p][f]).length));
                else d.push(r2(p, e3(o2[p], 0, c2 + 1), null, c2 + 1));
                return t2.prettyPrint && d.length > 0 && d.push("\n"), d.join("");
              case "function":
                return o2();
              default:
                return t2.escape ? ("" + o2).replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/'/g, "&apos;").replace(/"/g, "&quot;").replace(i, "") : "" + o2;
            }
          }(e2, 0, 0);
        }, u = function(e2) {
          var t2 = ['<?xml version="1.0" encoding="UTF-8"'];
          return e2 && t2.push(' standalone="yes"'), t2.push("?>"), t2.join("");
        };
        e.exports = function(e2, t2) {
          if (t2 || (t2 = { xmlHeader: { standalone: true }, prettyPrint: true, indent: "  ", escape: true }), "string" == typeof e2) try {
            e2 = JSON.parse(e2.toString());
          } catch (e3) {
            return false;
          }
          var r2 = "", o2 = "";
          return t2 && ("object" == n(t2) ? (t2.xmlHeader && (r2 = u(!!t2.xmlHeader.standalone)), void 0 !== t2.docType && (o2 = "<!DOCTYPE " + t2.docType + ">")) : r2 = u()), [r2, (t2 = t2 || {}).prettyPrint && o2 ? "\n" : "", o2, c(e2, t2)].join("").replace(/\n{2,}/g, "\n").replace(/\s+$/g, "");
        };
      }, function(e, t) {
        var r = function(e2) {
          var t2 = (e2 = e2 || {}).Base64, r2 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/", n = function(e3) {
            for (var t3 = {}, r3 = 0, n2 = e3.length; r3 < n2; r3++) t3[e3.charAt(r3)] = r3;
            return t3;
          }(r2), o = String.fromCharCode, a = function(e3) {
            if (e3.length < 2) return (t3 = e3.charCodeAt(0)) < 128 ? e3 : t3 < 2048 ? o(192 | t3 >>> 6) + o(128 | 63 & t3) : o(224 | t3 >>> 12 & 15) + o(128 | t3 >>> 6 & 63) + o(128 | 63 & t3);
            var t3 = 65536 + 1024 * (e3.charCodeAt(0) - 55296) + (e3.charCodeAt(1) - 56320);
            return o(240 | t3 >>> 18 & 7) + o(128 | t3 >>> 12 & 63) + o(128 | t3 >>> 6 & 63) + o(128 | 63 & t3);
          }, i = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g, s = function(e3) {
            return e3.replace(i, a);
          }, c = function(e3) {
            var t3 = [0, 2, 1][e3.length % 3], n2 = e3.charCodeAt(0) << 16 | (e3.length > 1 ? e3.charCodeAt(1) : 0) << 8 | (e3.length > 2 ? e3.charCodeAt(2) : 0);
            return [r2.charAt(n2 >>> 18), r2.charAt(n2 >>> 12 & 63), t3 >= 2 ? "=" : r2.charAt(n2 >>> 6 & 63), t3 >= 1 ? "=" : r2.charAt(63 & n2)].join("");
          }, u = e2.btoa ? function(t3) {
            return e2.btoa(t3);
          } : function(e3) {
            return e3.replace(/[\s\S]{1,3}/g, c);
          }, l = function(e3) {
            return u(s(e3));
          }, d = function(e3, t3) {
            return t3 ? l(String(e3)).replace(/[+\/]/g, function(e4) {
              return "+" == e4 ? "-" : "_";
            }).replace(/=/g, "") : l(String(e3));
          }, p = new RegExp(["[À-ß][-¿]", "[à-ï][-¿]{2}", "[ð-÷][-¿]{3}"].join("|"), "g"), f = function(e3) {
            switch (e3.length) {
              case 4:
                var t3 = ((7 & e3.charCodeAt(0)) << 18 | (63 & e3.charCodeAt(1)) << 12 | (63 & e3.charCodeAt(2)) << 6 | 63 & e3.charCodeAt(3)) - 65536;
                return o(55296 + (t3 >>> 10)) + o(56320 + (1023 & t3));
              case 3:
                return o((15 & e3.charCodeAt(0)) << 12 | (63 & e3.charCodeAt(1)) << 6 | 63 & e3.charCodeAt(2));
              default:
                return o((31 & e3.charCodeAt(0)) << 6 | 63 & e3.charCodeAt(1));
            }
          }, h = function(e3) {
            return e3.replace(p, f);
          }, m = function(e3) {
            var t3 = e3.length, r3 = t3 % 4, a2 = (t3 > 0 ? n[e3.charAt(0)] << 18 : 0) | (t3 > 1 ? n[e3.charAt(1)] << 12 : 0) | (t3 > 2 ? n[e3.charAt(2)] << 6 : 0) | (t3 > 3 ? n[e3.charAt(3)] : 0), i2 = [o(a2 >>> 16), o(a2 >>> 8 & 255), o(255 & a2)];
            return i2.length -= [0, 0, 2, 1][r3], i2.join("");
          }, g = e2.atob ? function(t3) {
            return e2.atob(t3);
          } : function(e3) {
            return e3.replace(/[\s\S]{1,4}/g, m);
          }, y = function(e3) {
            return h(g(e3));
          }, v = function(e3) {
            return y(String(e3).replace(/[-_]/g, function(e4) {
              return "-" == e4 ? "+" : "/";
            }).replace(/[^A-Za-z0-9\+\/]/g, ""));
          };
          return { VERSION: "2.1.9", atob: g, btoa: u, fromBase64: v, toBase64: d, utob: s, encode: d, encodeURI: function(e3) {
            return d(e3, true);
          }, btou: h, decode: v, noConflict: function() {
            var r3 = e2.Base64;
            return e2.Base64 = t2, r3;
          } };
        }();
        e.exports = r;
      }, function(e, t, r) {
        var n = r(0).default, o = r(28);
        e.exports = function(e2) {
          var t2 = o(e2, "string");
          return "symbol" == n(t2) ? t2 : t2 + "";
        }, e.exports.__esModule = true, e.exports.default = e.exports;
      }, function(e, t, r) {
        var n = r(0).default;
        e.exports = function(e2, t2) {
          if ("object" != n(e2) || !e2) return e2;
          var r2 = e2[Symbol.toPrimitive];
          if (void 0 !== r2) {
            var o = r2.call(e2, t2 || "default");
            if ("object" != n(o)) return o;
            throw new TypeError("@@toPrimitive must return a primitive value.");
          }
          return ("string" === t2 ? String : Number)(e2);
        }, e.exports.__esModule = true, e.exports.default = e.exports;
      }, function(e, t, r) {
        var n = r(0), o = function(e2) {
          switch (n(e2)) {
            case "string":
              return e2;
            case "boolean":
              return e2 ? "true" : "false";
            case "number":
              return isFinite(e2) ? e2 : "";
            default:
              return "";
          }
        }, a = function(e2, t2, r2) {
          var n2 = {}, o2 = t2.getAllResponseHeaders();
          return o2 && o2.length > 0 && o2.trim().split("\n").forEach(function(e3) {
            if (e3) {
              var t3 = e3.indexOf(":"), r3 = e3.substr(0, t3).trim().toLowerCase(), o3 = e3.substr(t3 + 1).trim();
              n2[r3] = o3;
            }
          }), { error: e2, statusCode: t2.status, statusMessage: t2.statusText, headers: n2, body: r2 };
        }, i = function(e2, t2) {
          return t2 || "text" !== t2 ? e2.response : e2.responseText;
        };
        e.exports = function(e2, t2) {
          var r2, s, c, u, l = (e2.method || "GET").toUpperCase(), d = e2.url;
          if (e2.qs) {
            var p = (r2 = e2.qs, s = s || "&", c = c || "=", null === r2 && (r2 = void 0), "object" === n(r2) ? Object.keys(r2).map(function(e3) {
              var t3 = encodeURIComponent(o(e3)) + c;
              return Array.isArray(r2[e3]) ? r2[e3].map(function(e4) {
                return t3 + encodeURIComponent(o(e4));
              }).join(s) : t3 + encodeURIComponent(o(r2[e3]));
            }).filter(Boolean).join(s) : u ? encodeURIComponent(o(u)) + c + encodeURIComponent(o(r2)) : "");
            p && (d += (-1 === d.indexOf("?") ? "?" : "&") + p);
          }
          var f = new XMLHttpRequest();
          if (f.open(l, d, true), f.responseType = e2.dataType || "text", e2.xhrFields) for (var h in e2.xhrFields) f[h] = e2.xhrFields[h];
          var m = e2.headers;
          if (m) for (var g in m) m.hasOwnProperty(g) && "content-length" !== g.toLowerCase() && "user-agent" !== g.toLowerCase() && "origin" !== g.toLowerCase() && "host" !== g.toLowerCase() && f.setRequestHeader(g, m[g]);
          return e2.onProgress && f.upload && (f.upload.onprogress = e2.onProgress), e2.onDownloadProgress && (f.onprogress = e2.onDownloadProgress), e2.timeout && (f.timeout = e2.timeout), f.ontimeout = function(e3) {
            var r3 = new Error("timeout");
            t2(a(r3, f));
          }, f.onload = function() {
            t2(a(null, f, i(f, e2.dataType)));
          }, f.onerror = function(r3) {
            var n2 = i(f, e2.dataType);
            if (n2) t2(a(null, f, n2));
            else {
              var o2 = f.statusText;
              o2 || 0 !== f.status || (o2 = new Error("CORS blocked or network error")), t2(a(o2, f, n2));
            }
          }, f.send(e2.body || ""), f;
        };
      }, function(e, t) {
        var r = { eachLimit: function(e2, t2, r2, n) {
          if (n = n || function() {
          }, !e2.length || ("function" == typeof t2 ? t2() : t2) <= 0) return n();
          var o = 0, a = 0, i = 0;
          !function s() {
            if (o >= e2.length) return n();
            for (; i < ("function" == typeof t2 ? t2() : t2) && a < e2.length; ) i += 1, r2(e2[(a += 1) - 1], function(t3) {
              t3 ? (n(t3), n = function() {
              }) : (i -= 1, (o += 1) >= e2.length ? n() : s());
            });
          }();
        }, retry: function(e2, t2, r2) {
          e2 < 1 ? r2() : function n(o) {
            t2(function(t3, a) {
              t3 && o < e2 ? n(o + 1) : r2(t3, a);
            });
          }(1);
        } };
        e.exports = r;
      }, function(e, t, r) {
        "use strict";
        var n = r(8), o = r(9);
        function a(e2, t2) {
          var r2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
          if (!r2) {
            if (Array.isArray(e2) || (r2 = function(e3, t3) {
              if (e3) {
                if ("string" == typeof e3) return i(e3, t3);
                var r3 = {}.toString.call(e3).slice(8, -1);
                return "Object" === r3 && e3.constructor && (r3 = e3.constructor.name), "Map" === r3 || "Set" === r3 ? Array.from(e3) : "Arguments" === r3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r3) ? i(e3, t3) : void 0;
              }
            }(e2)) || t2 && e2 && "number" == typeof e2.length) {
              r2 && (e2 = r2);
              var n2 = 0, o2 = function() {
              };
              return { s: o2, n: function() {
                return n2 >= e2.length ? { done: true } : { done: false, value: e2[n2++] };
              }, e: function(e3) {
                throw e3;
              }, f: o2 };
            }
            throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
          }
          var a2, s2 = true, c2 = false;
          return { s: function() {
            r2 = r2.call(e2);
          }, n: function() {
            var e3 = r2.next();
            return s2 = e3.done, e3;
          }, e: function(e3) {
            c2 = true, a2 = e3;
          }, f: function() {
            try {
              s2 || null == r2.return || r2.return();
            } finally {
              if (c2) throw a2;
            }
          } };
        }
        function i(e2, t2) {
          (null == t2 || t2 > e2.length) && (t2 = e2.length);
          for (var r2 = 0, n2 = Array(t2); r2 < t2; r2++) n2[r2] = e2[r2];
          return n2;
        }
        var s = r(1), c = (r(4), r(10), r(12), r(13), r(3), {}), u = function() {
          return o(function e2(t2) {
            n(this, e2), this.options = s.extend(s.clone(c), t2 || {}), this.strategy = "aggressive", this.loop = 0, this.reachLimit = false, this.curParallelLimit = 6, this.chunkUploadDurationArray = [], this.lastLoopUploadDuration = [], this.margin = 1.25;
          }, [{ key: "calAvarageDuration", value: function() {
            var e2 = [], t2 = 0;
            if (this.chunkUploadDurationArray.length < this.curParallelLimit) return 0;
            for (var r2 = this.chunkUploadDurationArray.length - 1; r2 >= this.chunkUploadDurationArray.length - 1 - this.curParallelLimit; r2--) this.chunkUploadDurationArray[r2] && (t2 += this.chunkUploadDurationArray[r2].duration, e2.push(this.chunkUploadDurationArray[r2].duration));
            return this.chunkUploadDurationArray = [], t2 / this.curParallelLimit;
          } }, { key: "nextTick", value: function() {
            var e2 = this.calAvarageDuration();
            if (!e2) return false;
            0 !== this.lastLoopUploadDuration.length && e2 >= this.getAvarageData(this.lastLoopUploadDuration) * this.margin ? this.curParallelLimit-- : "progressive" === this.strategy || this.reachLimit ? this.curParallelLimit++ : (this.curParallelLimit = 2 * this.curParallelLimit, this.curParallelLimit >= 18 && (this.curParallelLimit = 18)), this.lastLoopUploadDuration.push(e2);
          } }, { key: "getAvarageData", value: function(e2, t2) {
            var r2, n2 = 0, o2 = a(e2);
            try {
              for (o2.s(); !(r2 = o2.n()).done; ) {
                n2 += r2.value;
              }
            } catch (e3) {
              o2.e(e3);
            } finally {
              o2.f();
            }
            return n2 / e2.length;
          } }, { key: "triggerFallback", value: function() {
            this.reachLimit && (this.curParallelLimit = Math.floor(0.75 * this.curParallelLimit)), this.reachLimit = true;
          } }, { key: "getScheduledParallelLimit", value: function() {
            return this.curParallelLimit > 18 && (this.curParallelLimit = 18), this.curParallelLimit < 6 && (this.curParallelLimit = 6), this.curParallelLimit;
          } }, { key: "markStart", value: function(e2) {
            window.performance && window.performance.mark && window.performance.mark("".concat(e2, "_start"));
          } }, { key: "markEnd", value: function(e2) {
            if (window.performance && window.performance.mark && window.performance.mark("".concat(e2, "_end")), window.performance && window.performance.measure) {
              window.performance.measure(e2, "".concat(e2, "_start"), "".concat(e2, "_end"));
              var t2 = window.performance.getEntriesByName(e2)[0].duration;
              this.chunkUploadDurationArray.push({ partNumber: e2, duration: t2 }), this.nextTick();
            }
          } }]);
        }();
        e.exports = u;
      }]);
    });
  }
});

// node_modules/eventemitter3/index.js
var require_eventemitter3 = __commonJS({
  "node_modules/eventemitter3/index.js"(exports2, module2) {
    "use strict";
    var has = Object.prototype.hasOwnProperty;
    var prefix = "~";
    function Events() {
    }
    if (Object.create) {
      Events.prototype = /* @__PURE__ */ Object.create(null);
      if (!new Events().__proto__) prefix = false;
    }
    function EE(fn, context, once) {
      this.fn = fn;
      this.context = context;
      this.once = once || false;
    }
    function addListener(emitter, event, fn, context, once) {
      if (typeof fn !== "function") {
        throw new TypeError("The listener must be a function");
      }
      var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
      if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;
      else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);
      else emitter._events[evt] = [emitter._events[evt], listener];
      return emitter;
    }
    function clearEvent(emitter, evt) {
      if (--emitter._eventsCount === 0) emitter._events = new Events();
      else delete emitter._events[evt];
    }
    function EventEmitter() {
      this._events = new Events();
      this._eventsCount = 0;
    }
    EventEmitter.prototype.eventNames = function eventNames() {
      var names = [], events, name;
      if (this._eventsCount === 0) return names;
      for (name in events = this._events) {
        if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
      }
      if (Object.getOwnPropertySymbols) {
        return names.concat(Object.getOwnPropertySymbols(events));
      }
      return names;
    };
    EventEmitter.prototype.listeners = function listeners(event) {
      var evt = prefix ? prefix + event : event, handlers = this._events[evt];
      if (!handlers) return [];
      if (handlers.fn) return [handlers.fn];
      for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {
        ee[i] = handlers[i].fn;
      }
      return ee;
    };
    EventEmitter.prototype.listenerCount = function listenerCount(event) {
      var evt = prefix ? prefix + event : event, listeners = this._events[evt];
      if (!listeners) return 0;
      if (listeners.fn) return 1;
      return listeners.length;
    };
    EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return false;
      var listeners = this._events[evt], len = arguments.length, args, i;
      if (listeners.fn) {
        if (listeners.once) this.removeListener(event, listeners.fn, void 0, true);
        switch (len) {
          case 1:
            return listeners.fn.call(listeners.context), true;
          case 2:
            return listeners.fn.call(listeners.context, a1), true;
          case 3:
            return listeners.fn.call(listeners.context, a1, a2), true;
          case 4:
            return listeners.fn.call(listeners.context, a1, a2, a3), true;
          case 5:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
          case 6:
            return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
        }
        for (i = 1, args = new Array(len - 1); i < len; i++) {
          args[i - 1] = arguments[i];
        }
        listeners.fn.apply(listeners.context, args);
      } else {
        var length = listeners.length, j;
        for (i = 0; i < length; i++) {
          if (listeners[i].once) this.removeListener(event, listeners[i].fn, void 0, true);
          switch (len) {
            case 1:
              listeners[i].fn.call(listeners[i].context);
              break;
            case 2:
              listeners[i].fn.call(listeners[i].context, a1);
              break;
            case 3:
              listeners[i].fn.call(listeners[i].context, a1, a2);
              break;
            case 4:
              listeners[i].fn.call(listeners[i].context, a1, a2, a3);
              break;
            default:
              if (!args) for (j = 1, args = new Array(len - 1); j < len; j++) {
                args[j - 1] = arguments[j];
              }
              listeners[i].fn.apply(listeners[i].context, args);
          }
        }
      }
      return true;
    };
    EventEmitter.prototype.on = function on(event, fn, context) {
      return addListener(this, event, fn, context, false);
    };
    EventEmitter.prototype.once = function once(event, fn, context) {
      return addListener(this, event, fn, context, true);
    };
    EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {
      var evt = prefix ? prefix + event : event;
      if (!this._events[evt]) return this;
      if (!fn) {
        clearEvent(this, evt);
        return this;
      }
      var listeners = this._events[evt];
      if (listeners.fn) {
        if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
          clearEvent(this, evt);
        }
      } else {
        for (var i = 0, events = [], length = listeners.length; i < length; i++) {
          if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
            events.push(listeners[i]);
          }
        }
        if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;
        else clearEvent(this, evt);
      }
      return this;
    };
    EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {
      var evt;
      if (event) {
        evt = prefix ? prefix + event : event;
        if (this._events[evt]) clearEvent(this, evt);
      } else {
        this._events = new Events();
        this._eventsCount = 0;
      }
      return this;
    };
    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
    EventEmitter.prototype.addListener = EventEmitter.prototype.on;
    EventEmitter.prefixed = prefix;
    EventEmitter.EventEmitter = EventEmitter;
    if ("undefined" !== typeof module2) {
      module2.exports = EventEmitter;
    }
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/bind.js
var require_bind = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/bind.js"(exports2, module2) {
    "use strict";
    module2.exports = function bind(fn, thisArg) {
      return function wrap() {
        var args = new Array(arguments.length);
        for (var i = 0; i < args.length; i++) {
          args[i] = arguments[i];
        }
        return fn.apply(thisArg, args);
      };
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/utils.js
var require_utils = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/utils.js"(exports2, module2) {
    "use strict";
    var bind = require_bind();
    var toString = Object.prototype.toString;
    function isArray(val) {
      return toString.call(val) === "[object Array]";
    }
    function isUndefined(val) {
      return typeof val === "undefined";
    }
    function isBuffer(val) {
      return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && typeof val.constructor.isBuffer === "function" && val.constructor.isBuffer(val);
    }
    function isArrayBuffer(val) {
      return toString.call(val) === "[object ArrayBuffer]";
    }
    function isFormData(val) {
      return typeof FormData !== "undefined" && val instanceof FormData;
    }
    function isArrayBufferView(val) {
      var result;
      if (typeof ArrayBuffer !== "undefined" && ArrayBuffer.isView) {
        result = ArrayBuffer.isView(val);
      } else {
        result = val && val.buffer && val.buffer instanceof ArrayBuffer;
      }
      return result;
    }
    function isString(val) {
      return typeof val === "string";
    }
    function isNumber(val) {
      return typeof val === "number";
    }
    function isObject(val) {
      return val !== null && typeof val === "object";
    }
    function isPlainObject(val) {
      if (toString.call(val) !== "[object Object]") {
        return false;
      }
      var prototype = Object.getPrototypeOf(val);
      return prototype === null || prototype === Object.prototype;
    }
    function isDate(val) {
      return toString.call(val) === "[object Date]";
    }
    function isFile(val) {
      return toString.call(val) === "[object File]";
    }
    function isBlob(val) {
      return toString.call(val) === "[object Blob]";
    }
    function isFunction(val) {
      return toString.call(val) === "[object Function]";
    }
    function isStream(val) {
      return isObject(val) && isFunction(val.pipe);
    }
    function isURLSearchParams(val) {
      return typeof URLSearchParams !== "undefined" && val instanceof URLSearchParams;
    }
    function trim(str) {
      return str.trim ? str.trim() : str.replace(/^\s+|\s+$/g, "");
    }
    function isStandardBrowserEnv() {
      if (typeof navigator !== "undefined" && (navigator.product === "ReactNative" || navigator.product === "NativeScript" || navigator.product === "NS")) {
        return false;
      }
      return typeof window !== "undefined" && typeof document !== "undefined";
    }
    function forEach(obj, fn) {
      if (obj === null || typeof obj === "undefined") {
        return;
      }
      if (typeof obj !== "object") {
        obj = [obj];
      }
      if (isArray(obj)) {
        for (var i = 0, l = obj.length; i < l; i++) {
          fn.call(null, obj[i], i, obj);
        }
      } else {
        for (var key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            fn.call(null, obj[key], key, obj);
          }
        }
      }
    }
    function merge() {
      var result = {};
      function assignValue(val, key) {
        if (isPlainObject(result[key]) && isPlainObject(val)) {
          result[key] = merge(result[key], val);
        } else if (isPlainObject(val)) {
          result[key] = merge({}, val);
        } else if (isArray(val)) {
          result[key] = val.slice();
        } else {
          result[key] = val;
        }
      }
      for (var i = 0, l = arguments.length; i < l; i++) {
        forEach(arguments[i], assignValue);
      }
      return result;
    }
    function extend(a, b, thisArg) {
      forEach(b, function assignValue(val, key) {
        if (thisArg && typeof val === "function") {
          a[key] = bind(val, thisArg);
        } else {
          a[key] = val;
        }
      });
      return a;
    }
    function stripBOM(content) {
      if (content.charCodeAt(0) === 65279) {
        content = content.slice(1);
      }
      return content;
    }
    module2.exports = {
      isArray,
      isArrayBuffer,
      isBuffer,
      isFormData,
      isArrayBufferView,
      isString,
      isNumber,
      isObject,
      isPlainObject,
      isUndefined,
      isDate,
      isFile,
      isBlob,
      isFunction,
      isStream,
      isURLSearchParams,
      isStandardBrowserEnv,
      forEach,
      merge,
      extend,
      trim,
      stripBOM
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/buildURL.js
var require_buildURL = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/buildURL.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    function encode(val) {
      return encodeURIComponent(val).replace(/%3A/gi, ":").replace(/%24/g, "$").replace(/%2C/gi, ",").replace(/%20/g, "+").replace(/%5B/gi, "[").replace(/%5D/gi, "]");
    }
    module2.exports = function buildURL(url, params, paramsSerializer) {
      if (!params) {
        return url;
      }
      var serializedParams;
      if (paramsSerializer) {
        serializedParams = paramsSerializer(params);
      } else if (utils.isURLSearchParams(params)) {
        serializedParams = params.toString();
      } else {
        var parts = [];
        utils.forEach(params, function serialize(val, key) {
          if (val === null || typeof val === "undefined") {
            return;
          }
          if (utils.isArray(val)) {
            key = key + "[]";
          } else {
            val = [val];
          }
          utils.forEach(val, function parseValue(v) {
            if (utils.isDate(v)) {
              v = v.toISOString();
            } else if (utils.isObject(v)) {
              v = JSON.stringify(v);
            }
            parts.push(encode(key) + "=" + encode(v));
          });
        });
        serializedParams = parts.join("&");
      }
      if (serializedParams) {
        var hashmarkIndex = url.indexOf("#");
        if (hashmarkIndex !== -1) {
          url = url.slice(0, hashmarkIndex);
        }
        url += (url.indexOf("?") === -1 ? "?" : "&") + serializedParams;
      }
      return url;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/InterceptorManager.js
var require_InterceptorManager = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/InterceptorManager.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    function InterceptorManager() {
      this.handlers = [];
    }
    InterceptorManager.prototype.use = function use(fulfilled, rejected, options) {
      this.handlers.push({
        fulfilled,
        rejected,
        synchronous: options ? options.synchronous : false,
        runWhen: options ? options.runWhen : null
      });
      return this.handlers.length - 1;
    };
    InterceptorManager.prototype.eject = function eject(id) {
      if (this.handlers[id]) {
        this.handlers[id] = null;
      }
    };
    InterceptorManager.prototype.forEach = function forEach(fn) {
      utils.forEach(this.handlers, function forEachHandler(h) {
        if (h !== null) {
          fn(h);
        }
      });
    };
    module2.exports = InterceptorManager;
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/normalizeHeaderName.js
var require_normalizeHeaderName = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/normalizeHeaderName.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    module2.exports = function normalizeHeaderName(headers, normalizedName) {
      utils.forEach(headers, function processHeader(value, name) {
        if (name !== normalizedName && name.toUpperCase() === normalizedName.toUpperCase()) {
          headers[normalizedName] = value;
          delete headers[name];
        }
      });
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/enhanceError.js
var require_enhanceError = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/enhanceError.js"(exports2, module2) {
    "use strict";
    module2.exports = function enhanceError(error, config, code, request, response) {
      error.config = config;
      if (code) {
        error.code = code;
      }
      error.request = request;
      error.response = response;
      error.isAxiosError = true;
      error.toJSON = function toJSON() {
        return {
          // Standard
          message: this.message,
          name: this.name,
          // Microsoft
          description: this.description,
          number: this.number,
          // Mozilla
          fileName: this.fileName,
          lineNumber: this.lineNumber,
          columnNumber: this.columnNumber,
          stack: this.stack,
          // Axios
          config: this.config,
          code: this.code
        };
      };
      return error;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/createError.js
var require_createError = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/createError.js"(exports2, module2) {
    "use strict";
    var enhanceError = require_enhanceError();
    module2.exports = function createError(message, config, code, request, response) {
      var error = new Error(message);
      return enhanceError(error, config, code, request, response);
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/settle.js
var require_settle = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/settle.js"(exports2, module2) {
    "use strict";
    var createError = require_createError();
    module2.exports = function settle(resolve, reject, response) {
      var validateStatus = response.config.validateStatus;
      if (!response.status || !validateStatus || validateStatus(response.status)) {
        resolve(response);
      } else {
        reject(createError(
          "Request failed with status code " + response.status,
          response.config,
          null,
          response.request,
          response
        ));
      }
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/cookies.js
var require_cookies = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/cookies.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    module2.exports = utils.isStandardBrowserEnv() ? (
      // Standard browser envs support document.cookie
      /* @__PURE__ */ function standardBrowserEnv() {
        return {
          write: function write(name, value, expires, path, domain, secure) {
            var cookie = [];
            cookie.push(name + "=" + encodeURIComponent(value));
            if (utils.isNumber(expires)) {
              cookie.push("expires=" + new Date(expires).toGMTString());
            }
            if (utils.isString(path)) {
              cookie.push("path=" + path);
            }
            if (utils.isString(domain)) {
              cookie.push("domain=" + domain);
            }
            if (secure === true) {
              cookie.push("secure");
            }
            document.cookie = cookie.join("; ");
          },
          read: function read(name) {
            var match = document.cookie.match(new RegExp("(^|;\\s*)(" + name + ")=([^;]*)"));
            return match ? decodeURIComponent(match[3]) : null;
          },
          remove: function remove(name) {
            this.write(name, "", Date.now() - 864e5);
          }
        };
      }()
    ) : (
      // Non standard browser env (web workers, react-native) lack needed support.
      /* @__PURE__ */ function nonStandardBrowserEnv() {
        return {
          write: function write() {
          },
          read: function read() {
            return null;
          },
          remove: function remove() {
          }
        };
      }()
    );
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/isAbsoluteURL.js
var require_isAbsoluteURL = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/isAbsoluteURL.js"(exports2, module2) {
    "use strict";
    module2.exports = function isAbsoluteURL(url) {
      return /^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(url);
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/combineURLs.js
var require_combineURLs = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/combineURLs.js"(exports2, module2) {
    "use strict";
    module2.exports = function combineURLs(baseURL, relativeURL) {
      return relativeURL ? baseURL.replace(/\/+$/, "") + "/" + relativeURL.replace(/^\/+/, "") : baseURL;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/buildFullPath.js
var require_buildFullPath = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/buildFullPath.js"(exports2, module2) {
    "use strict";
    var isAbsoluteURL = require_isAbsoluteURL();
    var combineURLs = require_combineURLs();
    module2.exports = function buildFullPath(baseURL, requestedURL) {
      if (baseURL && !isAbsoluteURL(requestedURL)) {
        return combineURLs(baseURL, requestedURL);
      }
      return requestedURL;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/parseHeaders.js
var require_parseHeaders = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/parseHeaders.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var ignoreDuplicateOf = [
      "age",
      "authorization",
      "content-length",
      "content-type",
      "etag",
      "expires",
      "from",
      "host",
      "if-modified-since",
      "if-unmodified-since",
      "last-modified",
      "location",
      "max-forwards",
      "proxy-authorization",
      "referer",
      "retry-after",
      "user-agent"
    ];
    module2.exports = function parseHeaders(headers) {
      var parsed = {};
      var key;
      var val;
      var i;
      if (!headers) {
        return parsed;
      }
      utils.forEach(headers.split("\n"), function parser(line) {
        i = line.indexOf(":");
        key = utils.trim(line.substr(0, i)).toLowerCase();
        val = utils.trim(line.substr(i + 1));
        if (key) {
          if (parsed[key] && ignoreDuplicateOf.indexOf(key) >= 0) {
            return;
          }
          if (key === "set-cookie") {
            parsed[key] = (parsed[key] ? parsed[key] : []).concat([val]);
          } else {
            parsed[key] = parsed[key] ? parsed[key] + ", " + val : val;
          }
        }
      });
      return parsed;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/isURLSameOrigin.js
var require_isURLSameOrigin = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/isURLSameOrigin.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    module2.exports = utils.isStandardBrowserEnv() ? (
      // Standard browser envs have full support of the APIs needed to test
      // whether the request URL is of the same origin as current location.
      function standardBrowserEnv() {
        var msie = /(msie|trident)/i.test(navigator.userAgent);
        var urlParsingNode = document.createElement("a");
        var originURL;
        function resolveURL(url) {
          var href = url;
          if (msie) {
            urlParsingNode.setAttribute("href", href);
            href = urlParsingNode.href;
          }
          urlParsingNode.setAttribute("href", href);
          return {
            href: urlParsingNode.href,
            protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, "") : "",
            host: urlParsingNode.host,
            search: urlParsingNode.search ? urlParsingNode.search.replace(/^\?/, "") : "",
            hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, "") : "",
            hostname: urlParsingNode.hostname,
            port: urlParsingNode.port,
            pathname: urlParsingNode.pathname.charAt(0) === "/" ? urlParsingNode.pathname : "/" + urlParsingNode.pathname
          };
        }
        originURL = resolveURL(window.location.href);
        return function isURLSameOrigin(requestURL) {
          var parsed = utils.isString(requestURL) ? resolveURL(requestURL) : requestURL;
          return parsed.protocol === originURL.protocol && parsed.host === originURL.host;
        };
      }()
    ) : (
      // Non standard browser envs (web workers, react-native) lack needed support.
      /* @__PURE__ */ function nonStandardBrowserEnv() {
        return function isURLSameOrigin() {
          return true;
        };
      }()
    );
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/adapters/xhr.js
var require_xhr = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/adapters/xhr.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var settle = require_settle();
    var cookies = require_cookies();
    var buildURL = require_buildURL();
    var buildFullPath = require_buildFullPath();
    var parseHeaders = require_parseHeaders();
    var isURLSameOrigin = require_isURLSameOrigin();
    var createError = require_createError();
    module2.exports = function xhrAdapter(config) {
      return new Promise(function dispatchXhrRequest(resolve, reject) {
        var requestData = config.data;
        var requestHeaders = config.headers;
        var responseType = config.responseType;
        if (utils.isFormData(requestData)) {
          delete requestHeaders["Content-Type"];
        }
        var request = new XMLHttpRequest();
        if (config.auth) {
          var username = config.auth.username || "";
          var password = config.auth.password ? unescape(encodeURIComponent(config.auth.password)) : "";
          requestHeaders.Authorization = "Basic " + btoa(username + ":" + password);
        }
        var fullPath = buildFullPath(config.baseURL, config.url);
        request.open(config.method.toUpperCase(), buildURL(fullPath, config.params, config.paramsSerializer), true);
        request.timeout = config.timeout;
        function onloadend() {
          if (!request) {
            return;
          }
          var responseHeaders = "getAllResponseHeaders" in request ? parseHeaders(request.getAllResponseHeaders()) : null;
          var responseData = !responseType || responseType === "text" || responseType === "json" ? request.responseText : request.response;
          var response = {
            data: responseData,
            status: request.status,
            statusText: request.statusText,
            headers: responseHeaders,
            config,
            request
          };
          settle(resolve, reject, response);
          request = null;
        }
        if ("onloadend" in request) {
          request.onloadend = onloadend;
        } else {
          request.onreadystatechange = function handleLoad() {
            if (!request || request.readyState !== 4) {
              return;
            }
            if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf("file:") === 0)) {
              return;
            }
            setTimeout(onloadend);
          };
        }
        request.onabort = function handleAbort() {
          if (!request) {
            return;
          }
          reject(createError("Request aborted", config, "ECONNABORTED", request));
          request = null;
        };
        request.onerror = function handleError() {
          reject(createError("Network Error", config, null, request));
          request = null;
        };
        request.ontimeout = function handleTimeout() {
          var timeoutErrorMessage = "timeout of " + config.timeout + "ms exceeded";
          if (config.timeoutErrorMessage) {
            timeoutErrorMessage = config.timeoutErrorMessage;
          }
          reject(createError(
            timeoutErrorMessage,
            config,
            config.transitional && config.transitional.clarifyTimeoutError ? "ETIMEDOUT" : "ECONNABORTED",
            request
          ));
          request = null;
        };
        if (utils.isStandardBrowserEnv()) {
          var xsrfValue = (config.withCredentials || isURLSameOrigin(fullPath)) && config.xsrfCookieName ? cookies.read(config.xsrfCookieName) : void 0;
          if (xsrfValue) {
            requestHeaders[config.xsrfHeaderName] = xsrfValue;
          }
        }
        if ("setRequestHeader" in request) {
          utils.forEach(requestHeaders, function setRequestHeader(val, key) {
            if (typeof requestData === "undefined" && key.toLowerCase() === "content-type") {
              delete requestHeaders[key];
            } else {
              request.setRequestHeader(key, val);
            }
          });
        }
        if (!utils.isUndefined(config.withCredentials)) {
          request.withCredentials = !!config.withCredentials;
        }
        if (responseType && responseType !== "json") {
          request.responseType = config.responseType;
        }
        if (typeof config.onDownloadProgress === "function") {
          request.addEventListener("progress", config.onDownloadProgress);
        }
        if (typeof config.onUploadProgress === "function" && request.upload) {
          request.upload.addEventListener("progress", config.onUploadProgress);
        }
        if (config.cancelToken) {
          config.cancelToken.promise.then(function onCanceled(cancel) {
            if (!request) {
              return;
            }
            request.abort();
            reject(cancel);
            request = null;
          });
        }
        if (!requestData) {
          requestData = null;
        }
        request.send(requestData);
      });
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/defaults.js
var require_defaults = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/defaults.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var normalizeHeaderName = require_normalizeHeaderName();
    var enhanceError = require_enhanceError();
    var DEFAULT_CONTENT_TYPE = {
      "Content-Type": "application/x-www-form-urlencoded"
    };
    function setContentTypeIfUnset(headers, value) {
      if (!utils.isUndefined(headers) && utils.isUndefined(headers["Content-Type"])) {
        headers["Content-Type"] = value;
      }
    }
    function getDefaultAdapter() {
      var adapter;
      if (typeof XMLHttpRequest !== "undefined") {
        adapter = require_xhr();
      } else if (typeof process !== "undefined" && Object.prototype.toString.call(process) === "[object process]") {
        adapter = require_xhr();
      }
      return adapter;
    }
    function stringifySafely(rawValue, parser, encoder) {
      if (utils.isString(rawValue)) {
        try {
          (parser || JSON.parse)(rawValue);
          return utils.trim(rawValue);
        } catch (e) {
          if (e.name !== "SyntaxError") {
            throw e;
          }
        }
      }
      return (encoder || JSON.stringify)(rawValue);
    }
    var defaults = {
      transitional: {
        silentJSONParsing: true,
        forcedJSONParsing: true,
        clarifyTimeoutError: false
      },
      adapter: getDefaultAdapter(),
      transformRequest: [function transformRequest(data, headers) {
        normalizeHeaderName(headers, "Accept");
        normalizeHeaderName(headers, "Content-Type");
        if (utils.isFormData(data) || utils.isArrayBuffer(data) || utils.isBuffer(data) || utils.isStream(data) || utils.isFile(data) || utils.isBlob(data)) {
          return data;
        }
        if (utils.isArrayBufferView(data)) {
          return data.buffer;
        }
        if (utils.isURLSearchParams(data)) {
          setContentTypeIfUnset(headers, "application/x-www-form-urlencoded;charset=utf-8");
          return data.toString();
        }
        if (utils.isObject(data) || headers && headers["Content-Type"] === "application/json") {
          setContentTypeIfUnset(headers, "application/json");
          return stringifySafely(data);
        }
        return data;
      }],
      transformResponse: [function transformResponse(data) {
        var transitional = this.transitional;
        var silentJSONParsing = transitional && transitional.silentJSONParsing;
        var forcedJSONParsing = transitional && transitional.forcedJSONParsing;
        var strictJSONParsing = !silentJSONParsing && this.responseType === "json";
        if (strictJSONParsing || forcedJSONParsing && utils.isString(data) && data.length) {
          try {
            return JSON.parse(data);
          } catch (e) {
            if (strictJSONParsing) {
              if (e.name === "SyntaxError") {
                throw enhanceError(e, this, "E_JSON_PARSE");
              }
              throw e;
            }
          }
        }
        return data;
      }],
      /**
       * A timeout in milliseconds to abort a request. If set to 0 (default) a
       * timeout is not created.
       */
      timeout: 0,
      xsrfCookieName: "XSRF-TOKEN",
      xsrfHeaderName: "X-XSRF-TOKEN",
      maxContentLength: -1,
      maxBodyLength: -1,
      validateStatus: function validateStatus(status) {
        return status >= 200 && status < 300;
      }
    };
    defaults.headers = {
      common: {
        "Accept": "application/json, text/plain, */*"
      }
    };
    utils.forEach(["delete", "get", "head"], function forEachMethodNoData(method2) {
      defaults.headers[method2] = {};
    });
    utils.forEach(["post", "put", "patch"], function forEachMethodWithData(method2) {
      defaults.headers[method2] = utils.merge(DEFAULT_CONTENT_TYPE);
    });
    module2.exports = defaults;
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/transformData.js
var require_transformData = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/transformData.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var defaults = require_defaults();
    module2.exports = function transformData(data, headers, fns) {
      var context = this || defaults;
      utils.forEach(fns, function transform(fn) {
        data = fn.call(context, data, headers);
      });
      return data;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/cancel/isCancel.js
var require_isCancel = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/cancel/isCancel.js"(exports2, module2) {
    "use strict";
    module2.exports = function isCancel(value) {
      return !!(value && value.__CANCEL__);
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/dispatchRequest.js
var require_dispatchRequest = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/dispatchRequest.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var transformData = require_transformData();
    var isCancel = require_isCancel();
    var defaults = require_defaults();
    function throwIfCancellationRequested(config) {
      if (config.cancelToken) {
        config.cancelToken.throwIfRequested();
      }
    }
    module2.exports = function dispatchRequest(config) {
      throwIfCancellationRequested(config);
      config.headers = config.headers || {};
      config.data = transformData.call(
        config,
        config.data,
        config.headers,
        config.transformRequest
      );
      config.headers = utils.merge(
        config.headers.common || {},
        config.headers[config.method] || {},
        config.headers
      );
      utils.forEach(
        ["delete", "get", "head", "post", "put", "patch", "common"],
        function cleanHeaderConfig(method2) {
          delete config.headers[method2];
        }
      );
      var adapter = config.adapter || defaults.adapter;
      return adapter(config).then(function onAdapterResolution(response) {
        throwIfCancellationRequested(config);
        response.data = transformData.call(
          config,
          response.data,
          response.headers,
          config.transformResponse
        );
        return response;
      }, function onAdapterRejection(reason) {
        if (!isCancel(reason)) {
          throwIfCancellationRequested(config);
          if (reason && reason.response) {
            reason.response.data = transformData.call(
              config,
              reason.response.data,
              reason.response.headers,
              config.transformResponse
            );
          }
        }
        return Promise.reject(reason);
      });
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/mergeConfig.js
var require_mergeConfig = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/mergeConfig.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    module2.exports = function mergeConfig(config1, config2) {
      config2 = config2 || {};
      var config = {};
      var valueFromConfig2Keys = ["url", "method", "data"];
      var mergeDeepPropertiesKeys = ["headers", "auth", "proxy", "params"];
      var defaultToConfig2Keys = [
        "baseURL",
        "transformRequest",
        "transformResponse",
        "paramsSerializer",
        "timeout",
        "timeoutMessage",
        "withCredentials",
        "adapter",
        "responseType",
        "xsrfCookieName",
        "xsrfHeaderName",
        "onUploadProgress",
        "onDownloadProgress",
        "decompress",
        "maxContentLength",
        "maxBodyLength",
        "maxRedirects",
        "transport",
        "httpAgent",
        "httpsAgent",
        "cancelToken",
        "socketPath",
        "responseEncoding"
      ];
      var directMergeKeys = ["validateStatus"];
      function getMergedValue(target, source) {
        if (utils.isPlainObject(target) && utils.isPlainObject(source)) {
          return utils.merge(target, source);
        } else if (utils.isPlainObject(source)) {
          return utils.merge({}, source);
        } else if (utils.isArray(source)) {
          return source.slice();
        }
        return source;
      }
      function mergeDeepProperties(prop) {
        if (!utils.isUndefined(config2[prop])) {
          config[prop] = getMergedValue(config1[prop], config2[prop]);
        } else if (!utils.isUndefined(config1[prop])) {
          config[prop] = getMergedValue(void 0, config1[prop]);
        }
      }
      utils.forEach(valueFromConfig2Keys, function valueFromConfig2(prop) {
        if (!utils.isUndefined(config2[prop])) {
          config[prop] = getMergedValue(void 0, config2[prop]);
        }
      });
      utils.forEach(mergeDeepPropertiesKeys, mergeDeepProperties);
      utils.forEach(defaultToConfig2Keys, function defaultToConfig2(prop) {
        if (!utils.isUndefined(config2[prop])) {
          config[prop] = getMergedValue(void 0, config2[prop]);
        } else if (!utils.isUndefined(config1[prop])) {
          config[prop] = getMergedValue(void 0, config1[prop]);
        }
      });
      utils.forEach(directMergeKeys, function merge(prop) {
        if (prop in config2) {
          config[prop] = getMergedValue(config1[prop], config2[prop]);
        } else if (prop in config1) {
          config[prop] = getMergedValue(void 0, config1[prop]);
        }
      });
      var axiosKeys = valueFromConfig2Keys.concat(mergeDeepPropertiesKeys).concat(defaultToConfig2Keys).concat(directMergeKeys);
      var otherKeys = Object.keys(config1).concat(Object.keys(config2)).filter(function filterAxiosKeys(key) {
        return axiosKeys.indexOf(key) === -1;
      });
      utils.forEach(otherKeys, mergeDeepProperties);
      return config;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/package.json
var require_package = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/package.json"(exports2, module2) {
    module2.exports = {
      name: "axios",
      version: "0.21.4",
      description: "Promise based HTTP client for the browser and node.js",
      main: "index.js",
      scripts: {
        test: "grunt test",
        start: "node ./sandbox/server.js",
        build: "NODE_ENV=production grunt build",
        preversion: "npm test",
        version: "npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json",
        postversion: "git push && git push --tags",
        examples: "node ./examples/server.js",
        coveralls: "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",
        fix: "eslint --fix lib/**/*.js"
      },
      repository: {
        type: "git",
        url: "https://github.com/axios/axios.git"
      },
      keywords: [
        "xhr",
        "http",
        "ajax",
        "promise",
        "node"
      ],
      author: "Matt Zabriskie",
      license: "MIT",
      bugs: {
        url: "https://github.com/axios/axios/issues"
      },
      homepage: "https://axios-http.com",
      devDependencies: {
        coveralls: "^3.0.0",
        "es6-promise": "^4.2.4",
        grunt: "^1.3.0",
        "grunt-banner": "^0.6.0",
        "grunt-cli": "^1.2.0",
        "grunt-contrib-clean": "^1.1.0",
        "grunt-contrib-watch": "^1.0.0",
        "grunt-eslint": "^23.0.0",
        "grunt-karma": "^4.0.0",
        "grunt-mocha-test": "^0.13.3",
        "grunt-ts": "^6.0.0-beta.19",
        "grunt-webpack": "^4.0.2",
        "istanbul-instrumenter-loader": "^1.0.0",
        "jasmine-core": "^2.4.1",
        karma: "^6.3.2",
        "karma-chrome-launcher": "^3.1.0",
        "karma-firefox-launcher": "^2.1.0",
        "karma-jasmine": "^1.1.1",
        "karma-jasmine-ajax": "^0.1.13",
        "karma-safari-launcher": "^1.0.0",
        "karma-sauce-launcher": "^4.3.6",
        "karma-sinon": "^1.0.5",
        "karma-sourcemap-loader": "^0.3.8",
        "karma-webpack": "^4.0.2",
        "load-grunt-tasks": "^3.5.2",
        minimist: "^1.2.0",
        mocha: "^8.2.1",
        sinon: "^4.5.0",
        "terser-webpack-plugin": "^4.2.3",
        typescript: "^4.0.5",
        "url-search-params": "^0.10.0",
        webpack: "^4.44.2",
        "webpack-dev-server": "^3.11.0"
      },
      browser: {
        "./lib/adapters/http.js": "./lib/adapters/xhr.js"
      },
      jsdelivr: "dist/axios.min.js",
      unpkg: "dist/axios.min.js",
      typings: "./index.d.ts",
      dependencies: {
        "follow-redirects": "^1.14.0"
      },
      bundlesize: [
        {
          path: "./dist/axios.min.js",
          threshold: "5kB"
        }
      ]
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/validator.js
var require_validator = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/validator.js"(exports2, module2) {
    "use strict";
    var pkg = require_package();
    var validators = {};
    ["object", "boolean", "number", "function", "string", "symbol"].forEach(function(type, i) {
      validators[type] = function validator(thing) {
        return typeof thing === type || "a" + (i < 1 ? "n " : " ") + type;
      };
    });
    var deprecatedWarnings = {};
    var currentVerArr = pkg.version.split(".");
    function isOlderVersion(version, thanVersion) {
      var pkgVersionArr = thanVersion ? thanVersion.split(".") : currentVerArr;
      var destVer = version.split(".");
      for (var i = 0; i < 3; i++) {
        if (pkgVersionArr[i] > destVer[i]) {
          return true;
        } else if (pkgVersionArr[i] < destVer[i]) {
          return false;
        }
      }
      return false;
    }
    validators.transitional = function transitional(validator, version, message) {
      var isDeprecated = version && isOlderVersion(version);
      function formatMessage(opt, desc) {
        return "[Axios v" + pkg.version + "] Transitional option '" + opt + "'" + desc + (message ? ". " + message : "");
      }
      return function(value, opt, opts) {
        if (validator === false) {
          throw new Error(formatMessage(opt, " has been removed in " + version));
        }
        if (isDeprecated && !deprecatedWarnings[opt]) {
          deprecatedWarnings[opt] = true;
          console.warn(
            formatMessage(
              opt,
              " has been deprecated since v" + version + " and will be removed in the near future"
            )
          );
        }
        return validator ? validator(value, opt, opts) : true;
      };
    };
    function assertOptions(options, schema, allowUnknown) {
      if (typeof options !== "object") {
        throw new TypeError("options must be an object");
      }
      var keys = Object.keys(options);
      var i = keys.length;
      while (i-- > 0) {
        var opt = keys[i];
        var validator = schema[opt];
        if (validator) {
          var value = options[opt];
          var result = value === void 0 || validator(value, opt, options);
          if (result !== true) {
            throw new TypeError("option " + opt + " must be " + result);
          }
          continue;
        }
        if (allowUnknown !== true) {
          throw Error("Unknown option " + opt);
        }
      }
    }
    module2.exports = {
      isOlderVersion,
      assertOptions,
      validators
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/Axios.js
var require_Axios = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/core/Axios.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var buildURL = require_buildURL();
    var InterceptorManager = require_InterceptorManager();
    var dispatchRequest = require_dispatchRequest();
    var mergeConfig = require_mergeConfig();
    var validator = require_validator();
    var validators = validator.validators;
    function Axios(instanceConfig) {
      this.defaults = instanceConfig;
      this.interceptors = {
        request: new InterceptorManager(),
        response: new InterceptorManager()
      };
    }
    Axios.prototype.request = function request(config) {
      if (typeof config === "string") {
        config = arguments[1] || {};
        config.url = arguments[0];
      } else {
        config = config || {};
      }
      config = mergeConfig(this.defaults, config);
      if (config.method) {
        config.method = config.method.toLowerCase();
      } else if (this.defaults.method) {
        config.method = this.defaults.method.toLowerCase();
      } else {
        config.method = "get";
      }
      var transitional = config.transitional;
      if (transitional !== void 0) {
        validator.assertOptions(transitional, {
          silentJSONParsing: validators.transitional(validators.boolean, "1.0.0"),
          forcedJSONParsing: validators.transitional(validators.boolean, "1.0.0"),
          clarifyTimeoutError: validators.transitional(validators.boolean, "1.0.0")
        }, false);
      }
      var requestInterceptorChain = [];
      var synchronousRequestInterceptors = true;
      this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {
        if (typeof interceptor.runWhen === "function" && interceptor.runWhen(config) === false) {
          return;
        }
        synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;
        requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);
      });
      var responseInterceptorChain = [];
      this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {
        responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);
      });
      var promise;
      if (!synchronousRequestInterceptors) {
        var chain = [dispatchRequest, void 0];
        Array.prototype.unshift.apply(chain, requestInterceptorChain);
        chain = chain.concat(responseInterceptorChain);
        promise = Promise.resolve(config);
        while (chain.length) {
          promise = promise.then(chain.shift(), chain.shift());
        }
        return promise;
      }
      var newConfig = config;
      while (requestInterceptorChain.length) {
        var onFulfilled = requestInterceptorChain.shift();
        var onRejected = requestInterceptorChain.shift();
        try {
          newConfig = onFulfilled(newConfig);
        } catch (error) {
          onRejected(error);
          break;
        }
      }
      try {
        promise = dispatchRequest(newConfig);
      } catch (error) {
        return Promise.reject(error);
      }
      while (responseInterceptorChain.length) {
        promise = promise.then(responseInterceptorChain.shift(), responseInterceptorChain.shift());
      }
      return promise;
    };
    Axios.prototype.getUri = function getUri(config) {
      config = mergeConfig(this.defaults, config);
      return buildURL(config.url, config.params, config.paramsSerializer).replace(/^\?/, "");
    };
    utils.forEach(["delete", "get", "head", "options"], function forEachMethodNoData(method2) {
      Axios.prototype[method2] = function(url, config) {
        return this.request(mergeConfig(config || {}, {
          method: method2,
          url,
          data: (config || {}).data
        }));
      };
    });
    utils.forEach(["post", "put", "patch"], function forEachMethodWithData(method2) {
      Axios.prototype[method2] = function(url, data, config) {
        return this.request(mergeConfig(config || {}, {
          method: method2,
          url,
          data
        }));
      };
    });
    module2.exports = Axios;
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/cancel/Cancel.js
var require_Cancel = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/cancel/Cancel.js"(exports2, module2) {
    "use strict";
    function Cancel(message) {
      this.message = message;
    }
    Cancel.prototype.toString = function toString() {
      return "Cancel" + (this.message ? ": " + this.message : "");
    };
    Cancel.prototype.__CANCEL__ = true;
    module2.exports = Cancel;
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/cancel/CancelToken.js
var require_CancelToken = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/cancel/CancelToken.js"(exports2, module2) {
    "use strict";
    var Cancel = require_Cancel();
    function CancelToken(executor) {
      if (typeof executor !== "function") {
        throw new TypeError("executor must be a function.");
      }
      var resolvePromise;
      this.promise = new Promise(function promiseExecutor(resolve) {
        resolvePromise = resolve;
      });
      var token = this;
      executor(function cancel(message) {
        if (token.reason) {
          return;
        }
        token.reason = new Cancel(message);
        resolvePromise(token.reason);
      });
    }
    CancelToken.prototype.throwIfRequested = function throwIfRequested() {
      if (this.reason) {
        throw this.reason;
      }
    };
    CancelToken.source = function source() {
      var cancel;
      var token = new CancelToken(function executor(c) {
        cancel = c;
      });
      return {
        token,
        cancel
      };
    };
    module2.exports = CancelToken;
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/spread.js
var require_spread = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/spread.js"(exports2, module2) {
    "use strict";
    module2.exports = function spread(callback) {
      return function wrap(arr) {
        return callback.apply(null, arr);
      };
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/isAxiosError.js
var require_isAxiosError = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/helpers/isAxiosError.js"(exports2, module2) {
    "use strict";
    module2.exports = function isAxiosError(payload) {
      return typeof payload === "object" && payload.isAxiosError === true;
    };
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/lib/axios.js
var require_axios = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/lib/axios.js"(exports2, module2) {
    "use strict";
    var utils = require_utils();
    var bind = require_bind();
    var Axios = require_Axios();
    var mergeConfig = require_mergeConfig();
    var defaults = require_defaults();
    function createInstance(defaultConfig) {
      var context = new Axios(defaultConfig);
      var instance = bind(Axios.prototype.request, context);
      utils.extend(instance, Axios.prototype, context);
      utils.extend(instance, context);
      return instance;
    }
    var axios = createInstance(defaults);
    axios.Axios = Axios;
    axios.create = function create(instanceConfig) {
      return createInstance(mergeConfig(axios.defaults, instanceConfig));
    };
    axios.Cancel = require_Cancel();
    axios.CancelToken = require_CancelToken();
    axios.isCancel = require_isCancel();
    axios.all = function all(promises) {
      return Promise.all(promises);
    };
    axios.spread = require_spread();
    axios.isAxiosError = require_isAxiosError();
    module2.exports = axios;
    module2.exports.default = axios;
  }
});

// node_modules/vod-js-sdk-v6/node_modules/axios/index.js
var require_axios2 = __commonJS({
  "node_modules/vod-js-sdk-v6/node_modules/axios/index.js"(exports2, module2) {
    module2.exports = require_axios();
  }
});

// node_modules/vod-js-sdk-v6/lib/src/util.js
var require_util = __commonJS({
  "node_modules/vod-js-sdk-v6/lib/src/util.js"(exports2) {
    "use strict";
    Object.defineProperty(exports2, "__esModule", { value: true });
    exports2.HOST = void 0;
    function isFile(v) {
      return Object.prototype.toString.call(v) == "[object File]";
    }
    function isFunction(v) {
      return typeof v === "function";
    }
    function isString(v) {
      return typeof v === "string";
    }
    function noop() {
    }
    function delay(ms) {
      return new Promise(function(resolve) {
        setTimeout(function() {
          resolve();
        }, ms);
      });
    }
    function getUnix() {
      return Math.floor(Date.now() / 1e3);
    }
    var CLIENT_ERROR_CODE;
    (function(CLIENT_ERROR_CODE2) {
      CLIENT_ERROR_CODE2[CLIENT_ERROR_CODE2["UPLOAD_FAIL"] = 1] = "UPLOAD_FAIL";
    })(CLIENT_ERROR_CODE || (CLIENT_ERROR_CODE = {}));
    var HOST;
    (function(HOST2) {
      HOST2["MAIN"] = "vod2.qcloud.com";
      HOST2["BACKUP"] = "vod2.dnsv1.com";
    })(HOST || (exports2.HOST = HOST = {}));
    exports2.default = {
      isFile,
      isFunction,
      isString,
      noop,
      delay,
      getUnix,
      isTest: false,
      isDev: true,
      CLIENT_ERROR_CODE
    };
  }
});

// node_modules/vod-js-sdk-v6/lib/package.json
var require_package2 = __commonJS({
  "node_modules/vod-js-sdk-v6/lib/package.json"(exports2, module2) {
    module2.exports = {
      name: "vod-js-sdk-v6",
      version: "1.7.0",
      description: "tencent cloud vod js sdk v6",
      main: "lib/src/tc_vod.js",
      unpkg: "dist/vod-js-sdk-v6.js",
      typings: "lib/src/tc_vod.d.ts",
      scripts: {
        test: "cross-env NODE_ENV=test mocha -r espower-typescript/guess -r jsdom-global/register -r test/env test/**/*.test.ts",
        cover: "cross-env NODE_ENV=test nyc mocha -r espower-typescript/guess -r jsdom-global/register -r test/env test/**/*.test.ts",
        dev: "webpack --config webpack.dev.config.js --watch",
        dist: "webpack --config webpack.config.js",
        build: "npm run dist && npm run compile",
        compile: "tsc -p tsconfig.json",
        prepublish: "npm run build",
        lint: "tsc --noEmit && eslint 'src/**/*.{js,ts,tsx}' --quiet --fix"
      },
      repository: {
        type: "git",
        url: "git+https://github.com/tencentyun/vod-js-sdk-v6.git"
      },
      keywords: [
        "tencentcloud",
        "sdk",
        "vod"
      ],
      author: "alsotang <<EMAIL>>",
      license: "MIT",
      bugs: {
        url: "https://github.com/tencentyun/vod-js-sdk-v6/issues"
      },
      homepage: "https://github.com/tencentyun/vod-js-sdk-v6#readme",
      dependencies: {
        axios: "^0.21.1",
        eventemitter3: "^4.0.7",
        "js-sha1": "^0.6.0",
        typescript: "^5.1.6",
        uuid: "^3.4.0"
      },
      devDependencies: {
        "@types/mocha": "^5.2.5",
        "@types/semver": "^6.0.0",
        "@types/sha1": "^1.1.1",
        "@types/uuid": "^3.4.4",
        "@typescript-eslint/eslint-plugin": "^1.9.0",
        "@typescript-eslint/parser": "^1.9.0",
        "cross-env": "^6.0.3",
        eslint: "^5.16.0",
        "eslint-config-prettier": "^4.3.0",
        "eslint-plugin-prettier": "^3.1.0",
        "espower-typescript": "^9.0.1",
        jsdom: "^13.1.0",
        "jsdom-global": "^3.0.2",
        mm: "^2.4.1",
        mocha: "^5.2.0",
        nyc: "^13.1.0",
        "power-assert": "^1.6.1",
        prettier: "^1.17.1",
        semver: "^6.1.1",
        "ts-loader": "^9.4.4",
        webpack: "^5.88.1",
        "webpack-cli": "^5.1.4"
      },
      nyc: {
        extension: [
          ".ts",
          ".tsx"
        ],
        include: [
          "src"
        ],
        reporter: [
          "html"
        ],
        all: true
      }
    };
  }
});

// node_modules/vod-js-sdk-v6/lib/src/vod_reporter.js
var require_vod_reporter = __commonJS({
  "node_modules/vod-js-sdk-v6/lib/src/vod_reporter.js"(exports2) {
    "use strict";
    var __assign = exports2 && exports2.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports2, "__esModule", { value: true });
    exports2.VodReporter = exports2.VodReportEvent = void 0;
    var uploader_1 = require_uploader();
    var pkg = require_package2();
    var util_1 = require_util();
    var VodReportEvent;
    (function(VodReportEvent2) {
      VodReportEvent2["report_prepare"] = "report_prepare";
      VodReportEvent2["report_apply"] = "report_apply";
      VodReportEvent2["report_cos_upload"] = "report_cos_upload";
      VodReportEvent2["report_commit"] = "report_commit";
      VodReportEvent2["report_done"] = "report_done";
    })(VodReportEvent || (exports2.VodReportEvent = VodReportEvent = {}));
    var ReqType;
    (function(ReqType2) {
      ReqType2[ReqType2["apply"] = 10001] = "apply";
      ReqType2[ReqType2["cos_upload"] = 20001] = "cos_upload";
      ReqType2[ReqType2["commit"] = 10002] = "commit";
      ReqType2[ReqType2["done"] = 40001] = "done";
    })(ReqType || (ReqType = {}));
    var VodReporter = (
      /** @class */
      function() {
        function VodReporter2(uploader, options) {
          this.baseReportData = {
            version: pkg.version,
            platform: 3e3,
            device: navigator.userAgent
          };
          this.reportUrl = "https://vodreport.qcloud.com/ugcupload_new";
          this.uploader = uploader;
          this.options = options;
          this.init();
        }
        VodReporter2.prototype.init = function() {
          this.uploader.on(VodReportEvent.report_apply, this.onApply.bind(this));
          this.uploader.on(VodReportEvent.report_cos_upload, this.onCosUpload.bind(this));
          this.uploader.on(VodReportEvent.report_commit, this.onCommit.bind(this));
          this.uploader.on(VodReportEvent.report_done, this.onDone.bind(this));
        };
        VodReporter2.prototype.onApply = function(reportObj) {
          try {
            var uploader = this.uploader;
            if (!uploader.videoFile) {
              return;
            }
            Object.assign(this.baseReportData, {
              appId: uploader.appId,
              fileSize: uploader.videoFile.size,
              fileName: uploader.videoFile.name,
              fileType: uploader.videoFile.type,
              vodSessionKey: uploader.vodSessionKey,
              reqKey: uploader.reqKey,
              reportId: uploader.reportId
            });
            var customReportData = {
              reqType: ReqType.apply,
              errCode: 0,
              vodErrCode: 0,
              errMsg: "",
              reqTimeCost: Number(/* @__PURE__ */ new Date()) - Number(reportObj.requestStartTime),
              reqTime: Number(reportObj.requestStartTime)
            };
            if (reportObj.err) {
              customReportData.errCode = 1;
              customReportData.vodErrCode = reportObj.err.code;
              customReportData.errMsg = reportObj.err.message;
            }
            if (reportObj.data) {
              this.baseReportData.cosRegion = reportObj.data.storageRegionV5;
            }
            this.report(customReportData);
          } catch (e) {
            console.error("onApply", e);
            if (util_1.default.isTest) {
              throw e;
            }
          }
        };
        VodReporter2.prototype.onCosUpload = function(reportObj) {
          console.log("reportObj", reportObj);
          console.log("timecost", Number(/* @__PURE__ */ new Date()) - Number(reportObj.requestStartTime));
          try {
            var customReportData = {
              reqType: ReqType.cos_upload,
              errCode: 0,
              cosErrCode: "",
              errMsg: "",
              reqTimeCost: Number(/* @__PURE__ */ new Date()) - Number(reportObj.requestStartTime),
              reqTime: Number(reportObj.requestStartTime)
            };
            if (reportObj.err) {
              customReportData.errCode = 1;
              customReportData.cosErrCode = reportObj.err.error ? reportObj.err.error.Code : reportObj.err;
              if (reportObj.err && reportObj.err.error === "error") {
                customReportData.cosErrCode = "cors error";
              }
              customReportData.errMsg = JSON.stringify(reportObj.err);
            }
            this.report(customReportData);
          } catch (e) {
            console.error("onCosUpload", e);
            if (util_1.default.isTest) {
              throw e;
            }
          }
        };
        VodReporter2.prototype.onCommit = function(reportObj) {
          try {
            var customReportData = {
              reqType: ReqType.commit,
              errCode: 0,
              vodErrCode: 0,
              errMsg: "",
              reqTimeCost: Number(/* @__PURE__ */ new Date()) - Number(reportObj.requestStartTime),
              reqTime: Number(reportObj.requestStartTime)
            };
            if (reportObj.err) {
              customReportData.errCode = 1;
              customReportData.vodErrCode = reportObj.err.code;
              customReportData.errMsg = reportObj.err.message;
            }
            if (reportObj.data) {
              this.baseReportData.fileId = reportObj.data.fileId;
            }
            this.report(customReportData);
          } catch (e) {
            console.error("onCommit", e);
            if (util_1.default.isTest) {
              throw e;
            }
          }
        };
        VodReporter2.prototype.onDone = function(reportObj) {
          try {
            var customReportData = {
              reqType: ReqType.done,
              errCode: reportObj.err && reportObj.err.code,
              reqTimeCost: Number(/* @__PURE__ */ new Date()) - Number(reportObj.requestStartTime),
              reqTime: Number(reportObj.requestStartTime)
            };
            this.report(customReportData);
          } catch (e) {
            console.error("onDone", e);
            if (util_1.default.isTest) {
              throw e;
            }
          }
        };
        VodReporter2.prototype.report = function(reportData) {
          reportData = __assign(__assign({}, this.baseReportData), reportData);
          this.send(reportData);
        };
        VodReporter2.prototype.send = function(reportData) {
          if (util_1.default.isDev || util_1.default.isTest) {
            console.log("send reportData", reportData);
            return;
          }
          uploader_1.vodAxios.post(this.reportUrl, reportData, {
            withCredentials: false
          });
        };
        return VodReporter2;
      }()
    );
    exports2.VodReporter = VodReporter;
  }
});

// node_modules/uuid/lib/rng-browser.js
var require_rng_browser = __commonJS({
  "node_modules/uuid/lib/rng-browser.js"(exports2, module2) {
    var getRandomValues = typeof crypto != "undefined" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto != "undefined" && typeof window.msCrypto.getRandomValues == "function" && msCrypto.getRandomValues.bind(msCrypto);
    if (getRandomValues) {
      rnds8 = new Uint8Array(16);
      module2.exports = function whatwgRNG() {
        getRandomValues(rnds8);
        return rnds8;
      };
    } else {
      rnds = new Array(16);
      module2.exports = function mathRNG() {
        for (var i = 0, r; i < 16; i++) {
          if ((i & 3) === 0) r = Math.random() * 4294967296;
          rnds[i] = r >>> ((i & 3) << 3) & 255;
        }
        return rnds;
      };
    }
    var rnds8;
    var rnds;
  }
});

// node_modules/uuid/lib/bytesToUuid.js
var require_bytesToUuid = __commonJS({
  "node_modules/uuid/lib/bytesToUuid.js"(exports2, module2) {
    var byteToHex = [];
    for (i = 0; i < 256; ++i) {
      byteToHex[i] = (i + 256).toString(16).substr(1);
    }
    var i;
    function bytesToUuid(buf, offset) {
      var i2 = offset || 0;
      var bth = byteToHex;
      return [
        bth[buf[i2++]],
        bth[buf[i2++]],
        bth[buf[i2++]],
        bth[buf[i2++]],
        "-",
        bth[buf[i2++]],
        bth[buf[i2++]],
        "-",
        bth[buf[i2++]],
        bth[buf[i2++]],
        "-",
        bth[buf[i2++]],
        bth[buf[i2++]],
        "-",
        bth[buf[i2++]],
        bth[buf[i2++]],
        bth[buf[i2++]],
        bth[buf[i2++]],
        bth[buf[i2++]],
        bth[buf[i2++]]
      ].join("");
    }
    module2.exports = bytesToUuid;
  }
});

// node_modules/uuid/v4.js
var require_v4 = __commonJS({
  "node_modules/uuid/v4.js"(exports2, module2) {
    var rng = require_rng_browser();
    var bytesToUuid = require_bytesToUuid();
    function v4(options, buf, offset) {
      var i = buf && offset || 0;
      if (typeof options == "string") {
        buf = options === "binary" ? new Array(16) : null;
        options = null;
      }
      options = options || {};
      var rnds = options.random || (options.rng || rng)();
      rnds[6] = rnds[6] & 15 | 64;
      rnds[8] = rnds[8] & 63 | 128;
      if (buf) {
        for (var ii = 0; ii < 16; ++ii) {
          buf[i + ii] = rnds[ii];
        }
      }
      return buf || bytesToUuid(rnds);
    }
    module2.exports = v4;
  }
});

// node_modules/vod-js-sdk-v6/lib/src/uploader.js
var require_uploader = __commonJS({
  "node_modules/vod-js-sdk-v6/lib/src/uploader.js"(exports2) {
    "use strict";
    var __extends = exports2 && exports2.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d, b) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d2, b2) {
          d2.__proto__ = b2;
        } || function(d2, b2) {
          for (var p in b2) if (Object.prototype.hasOwnProperty.call(b2, p)) d2[p] = b2[p];
        };
        return extendStatics(d, b);
      };
      return function(d, b) {
        if (typeof b !== "function" && b !== null)
          throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() {
          this.constructor = d;
        }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
      };
    }();
    var __assign = exports2 && exports2.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    var __awaiter = exports2 && exports2.__awaiter || function(thisArg, _arguments, P, generator) {
      function adopt(value) {
        return value instanceof P ? value : new P(function(resolve) {
          resolve(value);
        });
      }
      return new (P || (P = Promise))(function(resolve, reject) {
        function fulfilled(value) {
          try {
            step(generator.next(value));
          } catch (e) {
            reject(e);
          }
        }
        function rejected(value) {
          try {
            step(generator["throw"](value));
          } catch (e) {
            reject(e);
          }
        }
        function step(result) {
          result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected);
        }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
      });
    };
    var __generator = exports2 && exports2.__generator || function(thisArg, body) {
      var _ = { label: 0, sent: function() {
        if (t[0] & 1) throw t[1];
        return t[1];
      }, trys: [], ops: [] }, f, y, t, g;
      return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
      }), g;
      function verb(n) {
        return function(v) {
          return step([n, v]);
        };
      }
      function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
          if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
          if (y = 0, t) op = [op[0] & 2, t.value];
          switch (op[0]) {
            case 0:
            case 1:
              t = op;
              break;
            case 4:
              _.label++;
              return { value: op[1], done: false };
            case 5:
              _.label++;
              y = op[1];
              op = [0];
              continue;
            case 7:
              op = _.ops.pop();
              _.trys.pop();
              continue;
            default:
              if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                _ = 0;
                continue;
              }
              if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                _.label = op[1];
                break;
              }
              if (op[0] === 6 && _.label < t[1]) {
                _.label = t[1];
                t = op;
                break;
              }
              if (t && _.label < t[2]) {
                _.label = t[2];
                _.ops.push(op);
                break;
              }
              if (t[2]) _.ops.pop();
              _.trys.pop();
              continue;
          }
          op = body.call(thisArg, _);
        } catch (e) {
          op = [6, e];
          y = 0;
        } finally {
          f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return { value: op[0] ? op[1] : void 0, done: true };
      }
    };
    Object.defineProperty(exports2, "__esModule", { value: true });
    exports2.UploaderEvent = exports2.vodAxios = void 0;
    var sha1 = require_sha1();
    var COS = require_base();
    var eventemitter3_1 = require_eventemitter3();
    var axios_1 = require_axios2();
    var util_1 = require_util();
    var vod_reporter_1 = require_vod_reporter();
    var uuidv4 = require_v4();
    exports2.vodAxios = axios_1.default.create();
    var hostList = ["vod-upload-accelerate2", "vod-upload-accelerate", "vod-upload-proxy", "vod-quic"];
    var targetHostList = ["vod-upload-accelerate2", "vod-upload-proxy", "vod-quic"];
    var hostIndex = 2;
    exports2.vodAxios.interceptors.response.use(function(response) {
      return response;
    }, function(error) {
      if (isNaN(error.code)) {
        error.code = 500;
      }
      return Promise.reject(error);
    });
    var UploaderEvent;
    (function(UploaderEvent2) {
      UploaderEvent2["video_progress"] = "video_progress";
      UploaderEvent2["media_progress"] = "media_progress";
      UploaderEvent2["video_upload"] = "video_upload";
      UploaderEvent2["media_upload"] = "media_upload";
      UploaderEvent2["cover_progress"] = "cover_progress";
      UploaderEvent2["cover_upload"] = "cover_upload";
    })(UploaderEvent || (exports2.UploaderEvent = UploaderEvent = {}));
    var Uploader = (
      /** @class */
      function(_super) {
        __extends(Uploader2, _super);
        function Uploader2(params) {
          var _this = _super.call(this) || this;
          _this.sessionName = "";
          _this.vodSessionKey = "";
          _this.appId = 0;
          _this.reqKey = uuidv4();
          _this.reportId = "";
          _this.enableResume = true;
          _this.enableRaceRegion = true;
          _this.applyRequestTimeout = 5e3;
          _this.applyRequestRetryCount = 3;
          _this.commitRequestTimeout = 5e3;
          _this.commitRequestRetryCount = 3;
          _this.retryDelay = 1e3;
          _this.targetAccelerate = false;
          _this.validateInitParams(params);
          _this.videoFile = params.mediaFile || params.videoFile;
          _this.getSignature = params.getSignature;
          _this.cosStrategy = _this.getCosStrategy(params);
          _this.enableResume = params.enableResume;
          _this.videoName = params.mediaName || params.videoName;
          _this.coverFile = params.coverFile;
          _this.fileId = params.fileId;
          _this.applyRequestTimeout = params.applyRequestTimeout || _this.applyRequestTimeout;
          _this.commitRequestTimeout = params.commitRequestTimeout || _this.commitRequestTimeout;
          _this.retryDelay = params.retryDelay || _this.retryDelay;
          _this.appId = params.appId || _this.appId;
          _this.reportId = params.reportId || _this.reportId;
          _this.enableRaceRegion = params.enableRaceRegion;
          _this.expireTime = params === null || params === void 0 ? void 0 : params.expireTime;
          _this.cosAuthTime = 0;
          _this.genFileInfo();
          return _this;
        }
        Uploader2.prototype.getCosStrategy = function(params) {
          var sourceData = {
            FileParallelLimit: params.fileParallelLimit,
            ChunkParallelLimit: params.chunkParallelLimit || 6,
            ChunkRetryTimes: params.chunkRetryTimes || 5,
            ChunkSize: params.chunkSize || 1048576 * 8,
            SliceSize: params.sliceSize,
            CopyChunkParallelLimit: params.copyChunkParallelLimit,
            CopyChunkSize: params.copyChunkSize,
            CopySliceSize: params.copySliceSize,
            ProgressInterval: params.progressInterval,
            DynamicAccelerate: params.dynamicAccelerate
          };
          var cosStrategy = Object.keys(sourceData).filter(function(key) {
            return sourceData[key] !== void 0;
          }).reduce(function(acc, key) {
            var _a;
            return __assign(__assign({}, acc), (_a = {}, _a[key] = sourceData[key], _a));
          }, {});
          return cosStrategy;
        };
        Uploader2.prototype.setStorage = function(name, value) {
          if (!name) {
            return;
          }
          var cname = "webugc_" + sha1(name);
          try {
            localStorage.setItem(cname, value);
          } catch (e) {
          }
        };
        Uploader2.prototype.getStorage = function(name) {
          if (!name) {
            return;
          }
          var cname = "webugc_" + sha1(name);
          var result = null;
          try {
            result = localStorage.getItem(cname);
          } catch (e) {
          }
          return result;
        };
        Uploader2.prototype.delStorage = function(name) {
          if (!name) {
            return;
          }
          var cname = "webugc_" + sha1(name);
          try {
            localStorage.removeItem(cname);
          } catch (e) {
          }
        };
        Uploader2.prototype.validateInitParams = function(params) {
          if (!util_1.default.isFunction(params.getSignature)) {
            throw new Error("getSignature must be a function");
          }
          if (params.videoFile && !util_1.default.isFile(params.videoFile)) {
            throw new Error("videoFile must be a File");
          }
        };
        Uploader2.prototype.genFileInfo = function() {
          var videoFile = this.videoFile;
          if (videoFile) {
            var lastDotIndex = videoFile.name.lastIndexOf(".");
            var videoName = "";
            if (this.videoName) {
              if (!util_1.default.isString(this.videoName)) {
                throw new Error("mediaName must be a string");
              } else if (/[:*?<>\"\\/|]/g.test(this.videoName)) {
                throw new Error('Cant use these chars in filename: \\ / : * ? " < > |');
              } else {
                videoName = this.videoName;
              }
            } else {
              videoName = videoFile.name.substring(0, lastDotIndex);
            }
            this.videoInfo = {
              name: videoName,
              type: videoFile.name.substring(lastDotIndex + 1).toLowerCase(),
              size: videoFile.size
            };
            this.sessionName += "".concat(videoFile.name, "_").concat(videoFile.size, ";");
          }
          var coverFile = this.coverFile;
          if (coverFile) {
            var coverName = coverFile.name;
            var coverLastDotIndex = coverName.lastIndexOf(".");
            this.coverInfo = {
              name: coverName.substring(0, coverLastDotIndex),
              type: coverName.substring(coverLastDotIndex + 1).toLowerCase(),
              size: coverFile.size
            };
            this.sessionName += "".concat(coverFile.name, "_").concat(coverFile.size, ";");
          }
        };
        Uploader2.prototype.requestRegion = function(callback) {
          return __awaiter(this, void 0, void 0, function() {
            var self2, signature, sendParams, requestStartTime, response, e_1, prepareData;
            return __generator(this, function(_a) {
              switch (_a.label) {
                case 0:
                  self2 = this;
                  return [4, this.getSignature()];
                case 1:
                  signature = _a.sent();
                  sendParams = {
                    signature
                  };
                  requestStartTime = Date.now();
                  _a.label = 2;
                case 2:
                  _a.trys.push([2, 4, , 5]);
                  return [4, exports2.vodAxios.post("https://vod2.qcloud.com/v3/index.php?Action=PrepareUploadUGC", sendParams, {
                    timeout: this.applyRequestTimeout,
                    withCredentials: false
                  })];
                case 3:
                  response = _a.sent();
                  return [3, 5];
                case 4:
                  e_1 = _a.sent();
                  return [2, callback()];
                case 5:
                  prepareData = response.data;
                  if (prepareData.code === 0) {
                    self2.appId = self2.appId || prepareData.data.appId;
                    if (!prepareData.data.cosRegionList || prepareData.data.cosRegionList && prepareData.data.cosRegionList.length <= 1) {
                      return [2, callback()];
                    }
                    return [2, self2.regionRace(prepareData.data.cosRegionList, function(res) {
                      self2.emit(vod_reporter_1.VodReportEvent.report_prepare, {
                        data: {
                          region: res
                        },
                        requestStartTime
                      });
                      return callback(res);
                    })];
                  } else {
                    return [2, callback()];
                  }
                  return [
                    2
                    /*return*/
                  ];
              }
            });
          });
        };
        Uploader2.prototype.regionRace = function(cosRegionList, cb) {
          return __awaiter(this, void 0, void 0, function() {
            var _this = this;
            return __generator(this, function(_a) {
              return [2, Promise.race(cosRegionList.map(function(item) {
                return _this.raceRequest(item);
              })).then(function(res) {
                return cb(res);
              })];
            });
          });
        };
        Uploader2.prototype.raceRequest = function(options) {
          return __awaiter(this, void 0, void 0, function() {
            var _this = this;
            return __generator(this, function(_a) {
              return [2, new Promise(function(resolve, reject) {
                return __awaiter(_this, void 0, void 0, function() {
                  var e_2;
                  return __generator(this, function(_a2) {
                    switch (_a2.label) {
                      case 0:
                        _a2.trys.push([0, 2, , 3]);
                        return [4, exports2.vodAxios.head("https://" + options.domain, {
                          timeout: this.applyRequestTimeout,
                          withCredentials: false
                        })];
                      case 1:
                        _a2.sent();
                        return [3, 3];
                      case 2:
                        e_2 = _a2.sent();
                        resolve(options.region);
                        return [3, 3];
                      case 3:
                        resolve(options.region);
                        return [
                          2
                          /*return*/
                        ];
                    }
                  });
                });
              })];
            });
          });
        };
        Uploader2.prototype.applyUploadUGC = function(retryCount, region) {
          if (retryCount === void 0) {
            retryCount = 0;
          }
          return __awaiter(this, void 0, void 0, function() {
            function whenError(err2) {
              return __awaiter(this, void 0, void 0, function() {
                return __generator(this, function(_a) {
                  switch (_a.label) {
                    case 0:
                      if (err2.code === 500) {
                        Uploader2.host = Uploader2.host === util_1.HOST.MAIN ? util_1.HOST.BACKUP : util_1.HOST.MAIN;
                      }
                      self2.emit(vod_reporter_1.VodReportEvent.report_apply, {
                        err: err2,
                        requestStartTime
                      });
                      self2.delStorage(self2.sessionName);
                      if (self2.applyRequestRetryCount == retryCount) {
                        if (err2) {
                          throw err2;
                        }
                        throw new Error("apply upload failed");
                      }
                      return [4, util_1.default.delay(self2.retryDelay)];
                    case 1:
                      _a.sent();
                      return [2, self2.applyUploadUGC(retryCount + 1)];
                  }
                });
              });
            }
            var self2, signature, sendParams, videoInfo, coverInfo, vodSessionKey, requestStartTime, response, e_3, applyResult, applyData_1, vodSessionKey_1, err;
            return __generator(this, function(_a) {
              switch (_a.label) {
                case 0:
                  self2 = this;
                  return [4, this.getSignature()];
                case 1:
                  signature = _a.sent();
                  videoInfo = this.videoInfo;
                  coverInfo = this.coverInfo;
                  vodSessionKey = this.vodSessionKey || this.enableResume && this.getStorage(this.sessionName);
                  if (vodSessionKey) {
                    sendParams = {
                      signature,
                      vodSessionKey
                    };
                  } else if (videoInfo) {
                    sendParams = {
                      signature,
                      videoName: videoInfo.name,
                      videoType: videoInfo.type,
                      videoSize: videoInfo.size
                    };
                    if (coverInfo) {
                      sendParams.coverName = coverInfo.name;
                      sendParams.coverType = coverInfo.type;
                      sendParams.coverSize = coverInfo.size;
                    }
                  } else if (this.fileId && coverInfo) {
                    sendParams = {
                      signature,
                      fileId: this.fileId,
                      coverName: coverInfo.name,
                      coverType: coverInfo.type,
                      coverSize: coverInfo.size
                    };
                  } else {
                    throw "Wrong params, please check and try again";
                  }
                  sendParams.uploadFromWeb = true;
                  if (this.expireTime) {
                    sendParams.ExpireTime = this.expireTime;
                  }
                  requestStartTime = /* @__PURE__ */ new Date();
                  region && (sendParams.storageRegion = region);
                  _a.label = 2;
                case 2:
                  _a.trys.push([2, 4, , 5]);
                  return [4, exports2.vodAxios.post("https://".concat(Uploader2.host, "/v3/index.php?Action=ApplyUploadUGC"), sendParams, {
                    timeout: this.applyRequestTimeout,
                    withCredentials: false
                  })];
                case 3:
                  response = _a.sent();
                  return [3, 5];
                case 4:
                  e_3 = _a.sent();
                  return [2, whenError(e_3)];
                case 5:
                  applyResult = response.data;
                  if (applyResult.code == 0) {
                    applyData_1 = applyResult.data;
                    vodSessionKey_1 = applyData_1.vodSessionKey;
                    this.setStorage(this.sessionName, vodSessionKey_1);
                    this.vodSessionKey = vodSessionKey_1;
                    this.appId = applyData_1.appId;
                    this.targetAccelerate = hostList.some(function(item) {
                      return applyData_1.StorageRegionV5 === item;
                    });
                    this.emit(vod_reporter_1.VodReportEvent.report_apply, {
                      data: applyData_1,
                      requestStartTime
                    });
                    return [2, applyData_1];
                  } else {
                    err = new Error(applyResult.message);
                    err.code = applyResult.code;
                    return [2, whenError(err)];
                  }
                  return [
                    2
                    /*return*/
                  ];
              }
            });
          });
        };
        Uploader2.prototype.uploadToCos = function(applyData) {
          return __awaiter(this, void 0, void 0, function() {
            var self2, cosParam, cos, uploadCosParams, cosVideoParam, cosCoverParam, requestStartTime, uploadPromises;
            return __generator(this, function(_a) {
              switch (_a.label) {
                case 0:
                  self2 = this;
                  cosParam = {
                    bucket: applyData.storageBucket + "-" + applyData.storageAppId,
                    region: applyData.storageRegionV5
                  };
                  if (!this.targetAccelerate) {
                    this.cosStrategy.DynamicAccelerate = false;
                  }
                  cos = new COS(Object.assign({
                    ForceSignHost: false,
                    // UseRawKey: true,
                    getAuthorization: function(options, callback) {
                      return __awaiter(this, void 0, void 0, function() {
                        var currentTimeStamp, safeExpireTime;
                        return __generator(this, function(_a2) {
                          switch (_a2.label) {
                            case 0:
                              currentTimeStamp = util_1.default.getUnix();
                              safeExpireTime = (applyData.tempCertificate.expiredTime - applyData.timestamp) * 0.9;
                              if (!(self2.cosAuthTime === 0)) return [3, 1];
                              self2.cosAuthTime = currentTimeStamp;
                              return [3, 3];
                            case 1:
                              if (!(self2.cosAuthTime && currentTimeStamp - self2.cosAuthTime >= safeExpireTime)) return [3, 3];
                              return [4, self2.applyUploadUGC()];
                            case 2:
                              applyData = _a2.sent();
                              self2.cosAuthTime = util_1.default.getUnix();
                              _a2.label = 3;
                            case 3:
                              callback({
                                TmpSecretId: applyData.tempCertificate.secretId,
                                TmpSecretKey: applyData.tempCertificate.secretKey,
                                XCosSecurityToken: applyData.tempCertificate.token,
                                StartTime: applyData.timestamp,
                                ExpiredTime: applyData.tempCertificate.expiredTime
                              });
                              return [
                                2
                                /*return*/
                              ];
                          }
                        });
                      });
                    }
                  }, this.cosStrategy));
                  this.cos = cos;
                  cos.on("before-send", function(opt) {
                    var url = opt.url;
                    if (!self2.targetAccelerate) {
                      return false;
                    }
                    if (!self2.cosStrategy.DynamicAccelerate) {
                      return false;
                    }
                    if (url.indexOf("https") === -1) {
                      url = url.replace("http", "https");
                    }
                    var u = url.match(/^(https?:\/\/([^\/]+)\/)([^\/]*\/?)(.*)$/);
                    var v = url.match(/cos\.(.+)\.myqcloud/);
                    if (url.indexOf("uploads") === -1) {
                      opt.url = url.replace(v[1], targetHostList[hostIndex]);
                      opt.headers["Vod-Forward-Cos"] = u[2];
                    }
                    if (hostIndex <= targetHostList.length - 2) {
                      hostIndex++;
                    } else {
                      hostIndex = 0;
                    }
                  });
                  uploadCosParams = [];
                  if (this.videoFile) {
                    cosVideoParam = __assign(__assign({}, cosParam), { file: this.videoFile, key: applyData.video.storagePath, onProgress: function(data) {
                      self2.emit(UploaderEvent.video_progress, data);
                      self2.emit(UploaderEvent.media_progress, data);
                    }, onUpload: function(data) {
                      self2.emit(UploaderEvent.video_upload, data);
                      self2.emit(UploaderEvent.media_upload, data);
                    }, onTaskReady: function(taskId) {
                      self2.taskId = taskId;
                    } });
                    uploadCosParams.push(cosVideoParam);
                  }
                  if (this.coverFile) {
                    cosCoverParam = __assign(__assign({}, cosParam), { file: this.coverFile, key: applyData.cover.storagePath, onProgress: function(data) {
                      self2.emit(UploaderEvent.cover_progress, data);
                    }, onUpload: function(data) {
                      self2.emit(UploaderEvent.cover_upload, data);
                    }, onTaskReady: util_1.default.noop });
                    uploadCosParams.push(cosCoverParam);
                  }
                  requestStartTime = /* @__PURE__ */ new Date();
                  uploadPromises = uploadCosParams.map(function(uploadCosParam) {
                    return new Promise(function(resolve, reject) {
                      cos.sliceUploadFile({
                        Bucket: uploadCosParam.bucket,
                        Region: uploadCosParam.region,
                        Key: uploadCosParam.key,
                        Body: uploadCosParam.file,
                        onTaskReady: uploadCosParam.onTaskReady,
                        onProgress: uploadCosParam.onProgress
                      }, function(err, data) {
                        if (uploadCosParam.file === self2.videoFile) {
                          self2.emit(vod_reporter_1.VodReportEvent.report_cos_upload, {
                            err,
                            requestStartTime
                          });
                        }
                        if (!err) {
                          uploadCosParam.onUpload(data);
                          return resolve();
                        }
                        self2.delStorage(self2.sessionName);
                        if (JSON.stringify(err) === '{"error":"error","headers":{}}') {
                          return reject(new Error("cors error"));
                        }
                        reject(err);
                      });
                    });
                  });
                  return [4, Promise.all(uploadPromises)];
                case 1:
                  return [2, _a.sent()];
              }
            });
          });
        };
        Uploader2.prototype.commitUploadUGC = function(retryCount) {
          if (retryCount === void 0) {
            retryCount = 0;
          }
          return __awaiter(this, void 0, void 0, function() {
            function whenError(err2) {
              return __awaiter(this, void 0, void 0, function() {
                return __generator(this, function(_a) {
                  switch (_a.label) {
                    case 0:
                      if (err2.code === 500) {
                        Uploader2.host = Uploader2.host === util_1.HOST.MAIN ? util_1.HOST.BACKUP : util_1.HOST.MAIN;
                      }
                      self2.emit(vod_reporter_1.VodReportEvent.report_commit, {
                        err: err2,
                        requestStartTime
                      });
                      if (self2.commitRequestRetryCount == retryCount) {
                        if (err2) {
                          throw err2;
                        }
                        throw new Error("commit upload failed");
                      }
                      return [4, util_1.default.delay(self2.retryDelay)];
                    case 1:
                      _a.sent();
                      return [2, self2.commitUploadUGC(retryCount + 1)];
                  }
                });
              });
            }
            var self2, signature, vodSessionKey, requestStartTime, response, e_4, commitResult, err;
            return __generator(this, function(_a) {
              switch (_a.label) {
                case 0:
                  self2 = this;
                  return [4, this.getSignature()];
                case 1:
                  signature = _a.sent();
                  this.delStorage(this.sessionName);
                  vodSessionKey = this.vodSessionKey;
                  requestStartTime = /* @__PURE__ */ new Date();
                  _a.label = 2;
                case 2:
                  _a.trys.push([2, 4, , 5]);
                  return [4, exports2.vodAxios.post("https://".concat(Uploader2.host, "/v3/index.php?Action=CommitUploadUGC"), {
                    signature,
                    vodSessionKey
                  }, {
                    timeout: this.commitRequestTimeout,
                    withCredentials: false
                  })];
                case 3:
                  response = _a.sent();
                  return [3, 5];
                case 4:
                  e_4 = _a.sent();
                  return [2, whenError(e_4)];
                case 5:
                  commitResult = response.data;
                  if (commitResult.code == 0) {
                    this.emit(vod_reporter_1.VodReportEvent.report_commit, {
                      data: commitResult.data,
                      requestStartTime
                    });
                    return [2, commitResult.data];
                  } else {
                    err = new Error(commitResult.message);
                    err.code = commitResult.code;
                    return [2, whenError(err)];
                  }
                  return [
                    2
                    /*return*/
                  ];
              }
            });
          });
        };
        Uploader2.prototype.start = function() {
          var _this = this;
          var requestStartTime = /* @__PURE__ */ new Date();
          this.donePromise = this._start().then(function(doneResult) {
            _this.emit(vod_reporter_1.VodReportEvent.report_done, {
              err: { code: 0 },
              requestStartTime
            });
            return doneResult;
          }).catch(function(err) {
            _this.emit(vod_reporter_1.VodReportEvent.report_done, {
              err: {
                code: err && err.code || util_1.default.CLIENT_ERROR_CODE.UPLOAD_FAIL
              },
              requestStartTime
            });
            throw err;
          });
        };
        Uploader2.prototype._start = function() {
          return __awaiter(this, void 0, void 0, function() {
            var uploadFunc;
            var _this = this;
            return __generator(this, function(_a) {
              switch (_a.label) {
                case 0:
                  uploadFunc = function(region) {
                    return __awaiter(_this, void 0, void 0, function() {
                      var applyData;
                      return __generator(this, function(_a2) {
                        switch (_a2.label) {
                          case 0:
                            return [4, this.applyUploadUGC(null, region)];
                          case 1:
                            applyData = _a2.sent();
                            return [4, this.uploadToCos(applyData)];
                          case 2:
                            _a2.sent();
                            return [4, this.commitUploadUGC()];
                          case 3:
                            return [2, _a2.sent()];
                        }
                      });
                    });
                  };
                  if (!this.enableRaceRegion) return [3, 2];
                  return [4, this.requestRegion(uploadFunc)];
                case 1:
                  return [2, _a.sent()];
                case 2:
                  return [4, uploadFunc()];
                case 3:
                  return [2, _a.sent()];
              }
            });
          });
        };
        Uploader2.prototype.done = function() {
          return this.donePromise;
        };
        Uploader2.prototype.cancel = function() {
          this.cos.cancelTask(this.taskId);
        };
        Uploader2.host = util_1.HOST.MAIN;
        return Uploader2;
      }(eventemitter3_1.EventEmitter)
    );
    exports2.default = Uploader;
  }
});

// node_modules/vod-js-sdk-v6/lib/src/tc_vod.js
var require_tc_vod = __commonJS({
  "node_modules/vod-js-sdk-v6/lib/src/tc_vod.js"(exports2) {
    var __assign = exports2 && exports2.__assign || function() {
      __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
          s = arguments[i];
          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
            t[p] = s[p];
        }
        return t;
      };
      return __assign.apply(this, arguments);
    };
    Object.defineProperty(exports2, "__esModule", { value: true });
    var uploader_1 = require_uploader();
    var vod_reporter_1 = require_vod_reporter();
    var TcVod = (
      /** @class */
      function() {
        function TcVod2(params) {
          this.allowReport = true;
          this.enableResume = true;
          this.enableRaceRegion = true;
          this.getSignature = params.getSignature;
          if (params.allowReport !== void 0) {
            this.allowReport = params.allowReport;
          }
          if (params.enableResume !== void 0) {
            this.enableResume = params.enableResume;
          }
          this.appId = params.appId;
          this.reportId = params.reportId;
          params.enableRaceRegion === false && (this.enableRaceRegion = params.enableRaceRegion);
        }
        TcVod2.prototype.upload = function(params) {
          var uploaderParams = __assign({ getSignature: this.getSignature, appId: this.appId, reportId: this.reportId, enableResume: this.enableResume, enableRaceRegion: this.enableRaceRegion }, params);
          var uploader = new uploader_1.default(uploaderParams);
          if (this.allowReport) {
            this.initReporter(uploader);
          }
          uploader.start();
          return uploader;
        };
        TcVod2.prototype.initReporter = function(uploader) {
          new vod_reporter_1.VodReporter(uploader);
        };
        return TcVod2;
      }()
    );
    exports2.default = TcVod;
  }
});
export default require_tc_vod();
/*! Bundled license information:

js-sha1/src/sha1.js:
  (*
   * [js-sha1]{@link https://github.com/emn178/js-sha1}
   *
   * @version 0.6.0
   * <AUTHOR> Yi-Cyuan [<EMAIL>]
   * @copyright Chen, Yi-Cyuan 2014-2017
   * @license MIT
   *)
*/
//# sourceMappingURL=vod-js-sdk-v6.js.map
