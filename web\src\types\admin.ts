export interface Admin {
  id: number
  username: string
  email: string
  nickname: string
  avatar: string
  status: number
  last_login: string | null
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface LoginResponse {
  token: string
  user_info: Admin
}

export interface CreateAdminRequest {
  username: string
  password: string
  email?: string
  nickname?: string
  avatar?: string
  status?: number
}

export interface UpdateAdminRequest {
  email?: string
  nickname?: string
  avatar?: string
  status?: number
}
