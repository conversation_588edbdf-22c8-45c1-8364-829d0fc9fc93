package logger

import (
	"ai_select_admin/config"
	"io"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
)

var Logger *logrus.Logger

func Init() {
	Logger = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(config.AppConfig.Log.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	Logger.SetLevel(level)

	// 设置日志格式
	Logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 创建日志目录
	logFile := config.AppConfig.Log.File
	if logFile != "" {
		dir := filepath.Dir(logFile)
		if err := os.MkdirAll(dir, 0755); err != nil {
			logrus.Errorf("创建日志目录失败: %v", err)
			return
		}

		// 打开日志文件
		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			logrus.Errorf("打开日志文件失败: %v", err)
			return
		}

		// 同时输出到文件和控制台
		Logger.SetOutput(io.MultiWriter(os.Stdout, file))
	}

	Logger.Info("日志系统初始化完成")
}

// Info 记录信息日志
func Info(args ...interface{}) {
	Logger.Info(args...)
}

// Infof 记录格式化信息日志
func Infof(format string, args ...interface{}) {
	Logger.Infof(format, args...)
}

// Error 记录错误日志
func Error(args ...interface{}) {
	Logger.Error(args...)
}

// Errorf 记录格式化错误日志
func Errorf(format string, args ...interface{}) {
	Logger.Errorf(format, args...)
}

// Warn 记录警告日志
func Warn(args ...interface{}) {
	Logger.Warn(args...)
}

// Warnf 记录格式化警告日志
func Warnf(format string, args ...interface{}) {
	Logger.Warnf(format, args...)
}

// Debug 记录调试日志
func Debug(args ...interface{}) {
	Logger.Debug(args...)
}

// Debugf 记录格式化调试日志
func Debugf(format string, args ...interface{}) {
	Logger.Debugf(format, args...)
}
