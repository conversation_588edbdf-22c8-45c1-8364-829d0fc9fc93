package models

import (
	"time"

	"gorm.io/gorm"
)

// Category 课程分类模型
type Category struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string         `json:"name" gorm:"type:varchar(100);not null;comment:分类名称"`
	Description string         `json:"description" gorm:"type:text;comment:分类描述"`
	Sort        int            `json:"sort" gorm:"type:int;default:0;comment:排序，数字越小越靠前"`
	Status      int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1启用 0禁用"`
	ParentID    uint           `json:"parent_id" gorm:"type:int;default:0;comment:父级分类ID，0为顶级分类"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Category) TableName() string {
	return "categories"
}

// CategoryListRequest 分类列表请求参数
type CategoryListRequest struct {
	Page     int    `form:"page" binding:"omitempty,min=1"`
	PageSize int    `form:"page_size" binding:"omitempty,min=1,max=100"`
	Name     string `form:"name"`
	Status   *int   `form:"status" binding:"omitempty,oneof=0 1"`
	ParentID *uint  `form:"parent_id"`
}

// CategoryCreateRequest 创建分类请求参数
type CategoryCreateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Sort        int    `json:"sort" binding:"omitempty,min=0"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
	ParentID    uint   `json:"parent_id" binding:"omitempty,min=0"`
}

// CategoryUpdateRequest 更新分类请求参数
type CategoryUpdateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Sort        int    `json:"sort" binding:"omitempty,min=0"`
	Status      int    `json:"status" binding:"omitempty,oneof=0 1"`
	ParentID    uint   `json:"parent_id" binding:"omitempty,min=0"`
}

// CategoryResponse 分类响应数据
type CategoryResponse struct {
	ID          uint                `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Sort        int                 `json:"sort"`
	Status      int                 `json:"status"`
	ParentID    uint                `json:"parent_id"`
	Children    []*CategoryResponse `json:"children,omitempty"`
	CreatedAt   time.Time           `json:"created_at"`
	UpdatedAt   time.Time           `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (c *Category) ToResponse() *CategoryResponse {
	return &CategoryResponse{
		ID:          c.ID,
		Name:        c.Name,
		Description: c.Description,
		Sort:        c.Sort,
		Status:      c.Status,
		ParentID:    c.ParentID,
		CreatedAt:   c.CreatedAt,
		UpdatedAt:   c.UpdatedAt,
	}
}
