import request from '@/utils/request'
import type { 
  LoginRequest, 
  LoginResponse, 
  Admin, 
  CreateAdminRequest, 
  UpdateAdminRequest 
} from '@/types/admin'
import type { ApiResponse, PageResponse, PageRequest } from '@/types/common'

// 管理员登录
export const login = (data: LoginRequest): Promise<ApiResponse<LoginResponse>> => {
  return request.post('/public/admin/login', data)
}

// 获取管理员个人信息
export const getProfile = (): Promise<ApiResponse<Admin>> => {
  return request.get('/auth/admin/profile')
}

// 获取管理员列表
export const getAdminList = (params: PageRequest): Promise<PageResponse<Admin[]>> => {
  return request.get('/admin/admins', { params })
}

// 创建管理员
export const createAdmin = (data: CreateAdminRequest): Promise<ApiResponse<Admin>> => {
  return request.post('/admin/admins', data)
}

// 更新管理员
export const updateAdmin = (id: number, data: UpdateAdminRequest): Promise<ApiResponse<Admin>> => {
  return request.put(`/admin/admins/${id}`, data)
}

// 删除管理员
export const deleteAdmin = (id: number): Promise<ApiResponse> => {
  return request.delete(`/admin/admins/${id}`)
}
