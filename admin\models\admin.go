package models

import (
	"crypto/md5"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type Admin struct {
	ID        uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	Username  string         `json:"username" gorm:"type:varchar(50);uniqueIndex;not null;comment:用户名"`
	Password  string         `json:"-" gorm:"type:varchar(255);not null;comment:密码"`
	Salt      string         `json:"-" gorm:"type:varchar(50);not null;comment:盐值"`
	Email     string         `json:"email" gorm:"type:varchar(100);comment:邮箱"`
	Nickname  string         `json:"nickname" gorm:"type:varchar(50);comment:昵称"`
	Avatar    string         `json:"avatar" gorm:"type:varchar(255);comment:头像"`
	Status    int            `json:"status" gorm:"type:tinyint(1);default:1;comment:状态 1正常 0禁用"`
	LastLogin *time.Time     `json:"last_login" gorm:"comment:最后登录时间"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// HashPassword 生成密码哈希 md5(password+salt)
func HashPassword(password, salt string) string {
	h := md5.New()
	h.Write([]byte(password + salt))
	return fmt.Sprintf("%x", h.Sum(nil))
}

// CheckPassword 验证密码
func (a *Admin) CheckPassword(password string) bool {
	return a.Password == HashPassword(password, a.Salt)
}

// BeforeCreate 创建前钩子
func (a *Admin) BeforeCreate(tx *gorm.DB) error {
	if a.Salt == "" {
		a.Salt = generateSalt()
	}
	if a.Password != "" {
		a.Password = HashPassword(a.Password, a.Salt)
	}
	return nil
}

// GenerateSalt 生成随机盐值
func GenerateSalt() string {
	str := fmt.Sprintf("%d", time.Now().UnixNano())
	return str[:6]
}

// generateSalt 生成随机盐值（内部使用）
func generateSalt() string {
	return GenerateSalt()
}
