import request from '@/utils/request'
import type { 
  Video, 
  VideoListRequest, 
  VideoCreateRequest, 
  VideoUpdateRequest,
  UploadSignatureResponse
} from '@/types/video'
import type { ApiResponse, PageResponse } from '@/types/api'

// 获取视频列表
export const getVideoList = (params: VideoListRequest): Promise<PageResponse<Video[]>> => {
  return request.get('/admin/videos', { params })
}

// 获取视频详情
export const getVideo = (id: number): Promise<ApiResponse<Video>> => {
  return request.get(`/admin/videos/${id}`)
}

// 创建视频
export const createVideo = (data: VideoCreateRequest): Promise<ApiResponse<Video>> => {
  return request.post('/admin/videos', data)
}

// 更新视频
export const updateVideo = (id: number, data: VideoUpdateRequest): Promise<ApiResponse<Video>> => {
  return request.put(`/admin/videos/${id}`, data)
}

// 删除视频
export const deleteVideo = (id: number): Promise<ApiResponse<null>> => {
  return request.delete(`/admin/videos/${id}`)
}

// 获取腾讯云上传签名
export const getUploadSignature = (): Promise<ApiResponse<UploadSignatureResponse>> => {
  return request.get('/auth/get_upload_vedio_signature')
}
