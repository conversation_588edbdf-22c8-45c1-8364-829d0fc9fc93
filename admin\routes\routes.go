package routes

import (
	"ai_select_admin/controllers"
	"ai_select_admin/middleware"

	"github.com/gin-gonic/gin"
)

func SetupRoutes() *gin.Engine {
	r := gin.New()

	// 添加中间件
	r.Use(middleware.LoggerMiddleware())
	r.Use(middleware.RequestBodyLoggerMiddleware())
	r.Use(middleware.ErrorLoggerMiddleware())
	r.Use(gin.Recovery())

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON>er("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 创建控制器实例
	adminController := &controllers.AdminController{}
	uploadController := controllers.NewUploadController()

	// API路由组
	api := r.Group("/api")
	{
		// 公开路由
		public := api.Group("/public")
		{
			public.POST("/admin/login", adminController.Login)
			public.GET("/upload/credentials", uploadController.GetUploadCredentials)
			public.GET("/upload/cos-url", uploadController.GetCOSURL)
		}

		// 需要认证的路由
		auth := api.Group("/auth")
		auth.Use(middleware.AuthMiddleware())
		{
			// 管理员个人信息
			auth.GET("/admin/profile", adminController.GetProfile)
		}

		// 需要管理员权限的路由
		admin := api.Group("/admin")
		admin.Use(middleware.AdminAuthMiddleware())
		{
			// 管理员管理
			adminGroup := admin.Group("/admins")
			{
				adminGroup.GET("", adminController.ListAdmins)
				adminGroup.POST("", adminController.CreateAdmin)
				adminGroup.PUT("/:id", adminController.UpdateAdmin)
				adminGroup.DELETE("/:id", adminController.DeleteAdmin)
			}
		}
	}

	// 静态文件服务
	r.Static("/uploads", "./uploads")

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "AI Select Admin API is running",
		})
	})

	return r
}
