package middleware

import (
	"ai_select_admin/utils"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取token
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "请求头中缺少Authorization字段",
			})
			c.Abort()
			return
		}

		// 检查token格式
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "Authorization格式错误",
			})
			c.Abort()
			return
		}

		// 解析token
		claims, err := utils.ParseToken(parts[1])
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"code": 401,
				"msg":  "token无效: " + err.Error(),
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_type", claims.UserType)

		c.Next()
	}
}

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 先执行基础认证
		AuthMiddleware()(c)
		
		if c.IsAborted() {
			return
		}

		// 检查用户类型
		userType, exists := c.Get("user_type")
		if !exists || userType != "admin" {
			c.JSON(http.StatusForbidden, gin.H{
				"code": 403,
				"msg":  "需要管理员权限",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
