// 分类基础接口
export interface Category {
  id: number
  name: string
  description: string
  sort: number
  status: number
  parent_id: number
  children?: Category[]
  created_at: string
  updated_at: string
}

// 分类列表请求参数
export interface CategoryListRequest {
  page?: number
  page_size?: number
  name?: string
  status?: number
  parent_id?: number
}

// 创建分类请求参数
export interface CategoryCreateRequest {
  name: string
  description?: string
  sort?: number
  status?: number
  parent_id?: number
}

// 更新分类请求参数
export interface CategoryUpdateRequest {
  name: string
  description?: string
  sort?: number
  status?: number
  parent_id?: number
}

// 分类表单数据
export interface CategoryFormData {
  name: string
  description: string
  sort: number
  status: number
  parent_id: number
}

// 分类状态选项
export interface CategoryStatusOption {
  label: string
  value: number
}

// 分类树节点
export interface CategoryTreeNode {
  id: number
  label: string
  children?: CategoryTreeNode[]
}
