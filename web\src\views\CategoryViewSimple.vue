<template>
  <div class="category-container">
    <h2>课程分类管理</h2>
    <p>这是课程分类管理页面</p>
    
    <el-button type="primary" @click="testMessage">测试按钮</el-button>
    
    <div v-if="loading">加载中...</div>
    <div v-else>
      <p>分类列表将在这里显示</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

const loading = ref(false)

const testMessage = () => {
  ElMessage.success('测试成功！')
}
</script>

<style scoped>
.category-container {
  padding: 20px;
}
</style>
