# [Air](https://github.com/cosmtrek/air) TOML 格式的配置文件

# 工作目录
# 使用 . 或绝对路径，请注意 `bin_dir` 目录必须在 `root` 目录下
root = "."
bin_dir = "bin"

[build]
# 只需要写你平常编译使用的shell命令。你也可以使用 `make`
cmd = "go build -o ./bin/main.exe ."
# 由`cmd`命令得到的二进制文件名
bin = "bin/main.exe"
# 自定义的二进制，可以添加额外的编译标识例如添加 GIN_MODE=release
full_bin = "./bin/main.exe"
# 监听以下文件扩展名的文件.
include_ext = ["go", "tpl", "binl", "html"]
# 忽略这些文件扩展名或目录
exclude_dir = ["assets", "bin", "vendor", "frontend/node_modules"]
# 监听以下指定目录的文件
include_dir = []
# 排除以下文件
exclude_file = []
# 如果文件更改过于频繁，则没有必要在每次更改时都触发构建。可以设置触发构建的延迟时间
delay = 1000 # ms
# 发生构建错误时，停止运行旧的二进制文件。
stop_on_error = true
# air的日志文件名，该日志文件放置在你的`bin_dir`中
log = "air_errors.log"

[log]
# 显示日志时间
time = true

[color]
# 自定义每个部分显示的颜色。如果找不到颜色，使用原始的应用程序日志。
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# 退出时删除bin目录
clean_on_exit = true