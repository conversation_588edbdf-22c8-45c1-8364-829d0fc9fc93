import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Admin } from '@/types/admin'
import { login as apiLogin } from '@/api/admin'

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string>(localStorage.getItem('token') || '')
  const userInfo = ref<Admin | null>(null)

  const isLoggedIn = computed(() => !!token.value)

  const login = async (username: string, password: string) => {
    try {
      const response = await apiLogin({ username, password })

      if (response.data?.token && response.data?.user_info) {
        token.value = response.data.token
        userInfo.value = response.data.user_info

        localStorage.setItem('token', token.value)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      } else {
        throw new Error('登录响应数据格式错误')
      }

      return response
    } catch (error) {
      throw error
    }
  }

  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  const initAuth = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  return {
    token,
    userInfo,
    isLoggedIn,
    login,
    logout,
    initAuth
  }
})
