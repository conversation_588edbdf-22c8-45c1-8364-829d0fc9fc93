package controllers

import (
	"ai_select_admin/config"
	"ai_select_admin/utils"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type TencentAuthController struct{}

func NewTencentAuthController() *TencentAuthController {
	return &TencentAuthController{}
}
func (*TencentAuthController) GetSignature(c *gin.Context) {
	secretId := config.AppConfig.TencentCloud.SecretId
	secretKey := config.AppConfig.TencentCloud.SecretKey
	subAppid := config.AppConfig.TencentCloud.TcVod.SubAppid
	procedure := config.AppConfig.TencentCloud.TcVod.Procedure
	timestamp := time.Now().Unix()
	timestampStr := strconv.FormatInt(timestamp, 10)
	expireTimeStr := strconv.FormatInt(timestamp+3600*24, 10)
	randomStr := strconv.Itoa(rand.Intn(10000))
	original := fmt.Sprintf("secretId=%s&currentTimeStamp=%s&expireTime=%s&random=%s&procedure=%s&vodSubAppId=%s",
		secretId, timestampStr, expireTimeStr, randomStr, procedure, subAppid)

	mac := hmac.New(sha1.New, []byte(secretKey))
	mac.Write([]byte(original))
	signature := mac.Sum(nil)
	signature = append(signature, []byte(original)...)
	data := make(map[string]string)
	data["signature"] = base64.StdEncoding.EncodeToString(signature)
	utils.Success(c, data)
}
